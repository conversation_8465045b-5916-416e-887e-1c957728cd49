-- TheRateRace.io Database Functions
-- Functions for game logic and bot management

-- Function to generate unique invite codes
CREATE OR REPLACE FUNCTION generate_invite_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..8 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to create a game room
CREATE OR REPLACE FUNCTION create_game_room(
    room_name TEXT,
    room_description TEXT DEFAULT NULL,
    max_players_count INTEGER DEFAULT 4,
    room_password TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_room_id UUID;
    invite_code TEXT;
    password_hash TEXT;
BEGIN
    -- Generate unique invite code
    LOOP
        invite_code := generate_invite_code();
        EXIT WHEN NOT EXISTS (SELECT 1 FROM public.game_rooms WHERE invite_code = invite_code);
    END LOOP;
    
    -- Hash password if provided
    IF room_password IS NOT NULL THEN
        password_hash := crypt(room_password, gen_salt('bf'));
    END IF;
    
    -- Create room
    INSERT INTO public.game_rooms (name, description, max_players, password_hash, invite_code, created_by)
    VALUES (room_name, room_description, max_players_count, password_hash, invite_code, auth.uid())
    RETURNING id INTO new_room_id;
    
    -- Add creator as first player
    INSERT INTO public.room_players (room_id, player_id, player_name, player_index)
    SELECT new_room_id, auth.uid(), profiles.username, 0
    FROM public.profiles
    WHERE profiles.id = auth.uid();
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = 1 
    WHERE id = new_room_id;
    
    RETURN new_room_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to join a game room
CREATE OR REPLACE FUNCTION join_game_room(
    room_id UUID,
    room_password TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    room_record RECORD;
    next_index INTEGER;
BEGIN
    -- Get room info
    SELECT * INTO room_record FROM public.game_rooms WHERE id = room_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room not found';
    END IF;
    
    -- Check if room is full
    IF room_record.current_players >= room_record.max_players THEN
        RAISE EXCEPTION 'Room is full';
    END IF;
    
    -- Check if game already started
    IF room_record.status != 'waiting' THEN
        RAISE EXCEPTION 'Game already started';
    END IF;
    
    -- Check password if required
    IF room_record.password_hash IS NOT NULL THEN
        IF room_password IS NULL OR NOT (room_record.password_hash = crypt(room_password, room_record.password_hash)) THEN
            RAISE EXCEPTION 'Invalid password';
        END IF;
    END IF;
    
    -- Check if player already in room
    IF EXISTS (SELECT 1 FROM public.room_players WHERE room_id = room_id AND player_id = auth.uid()) THEN
        RAISE EXCEPTION 'Already in this room';
    END IF;
    
    -- Get next available player index
    SELECT COALESCE(MAX(player_index), -1) + 1 INTO next_index
    FROM public.room_players 
    WHERE room_id = room_id;
    
    -- Add player to room
    INSERT INTO public.room_players (room_id, player_id, player_name, player_index)
    SELECT room_id, auth.uid(), profiles.username, next_index
    FROM public.profiles
    WHERE profiles.id = auth.uid();
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = current_players + 1 
    WHERE id = room_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add bots to fill a room
CREATE OR REPLACE FUNCTION add_bots_to_room(room_id UUID)
RETURNS INTEGER AS $$
DECLARE
    room_record RECORD;
    bots_to_add INTEGER;
    bot_names TEXT[] := ARRAY[
        'Alex_Trader', 'Sophie_CEO', 'Marc_Startup', 'Julie_Finance', 
        'Thomas_Tech', 'Emma_Marketing', 'Lucas_Consultant', 'Chloe_Manager',
        'Antoine_Investor', 'Lea_Executive', 'Maxime_Entrepreneur', 'Clara_Director'
    ];
    bot_personalities TEXT[] := ARRAY['aggressive', 'conservative', 'balanced', 'random'];
    i INTEGER;
    bot_name TEXT;
    bot_personality TEXT;
    next_index INTEGER;
BEGIN
    -- Get room info
    SELECT * INTO room_record FROM public.game_rooms WHERE id = room_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room not found';
    END IF;
    
    -- Calculate how many bots to add
    bots_to_add := room_record.max_players - room_record.current_players;
    
    IF bots_to_add <= 0 THEN
        RETURN 0;
    END IF;
    
    -- Add bots
    FOR i IN 1..bots_to_add LOOP
        -- Get next available player index
        SELECT COALESCE(MAX(player_index), -1) + 1 INTO next_index
        FROM public.room_players 
        WHERE room_id = room_id;
        
        -- Select random bot name and personality
        bot_name := bot_names[1 + floor(random() * array_length(bot_names, 1))::INTEGER];
        bot_personality := bot_personalities[1 + floor(random() * array_length(bot_personalities, 1))::INTEGER];
        
        -- Ensure unique bot name in this room
        WHILE EXISTS (SELECT 1 FROM public.room_players WHERE room_id = room_id AND player_name = bot_name) LOOP
            bot_name := bot_names[1 + floor(random() * array_length(bot_names, 1))::INTEGER] || '_' || floor(random() * 1000)::TEXT;
        END LOOP;
        
        -- Add bot
        INSERT INTO public.room_players (room_id, player_name, is_bot, bot_personality, player_index)
        VALUES (room_id, bot_name, TRUE, bot_personality, next_index);
    END LOOP;
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = room_record.max_players 
    WHERE id = room_id;
    
    RETURN bots_to_add;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
