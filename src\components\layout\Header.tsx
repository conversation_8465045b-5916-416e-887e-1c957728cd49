'use client'

import { useI18n } from '@/lib/i18n/hooks'
import { locales, localeNames } from '@/lib/i18n/config'
import { useViewport } from '@/hooks/useViewport'
import { useState } from 'react'

export default function Header() {
  const { t, changeLanguage, currentLanguage } = useI18n('common')
  const viewport = useViewport()
  const [showMobileMenu, setShowMobileMenu] = useState(false)

  return (
    <header className="bg-card border-b border-border flex-shrink-0">
      <div className="container mx-auto px-2 md:px-4 py-2 md:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-1 md:space-x-2">
            <div className={`font-bold text-primary neon-text ${viewport.isMobile ? 'text-lg' : 'text-2xl'}`}>
              TheRateRace.io
            </div>
            {!viewport.isMobile && (
              <div className="text-xs text-muted bg-accent text-black px-2 py-1 rounded">
                BETA
              </div>
            )}
          </div>

          {/* Navigation Desktop */}
          {viewport.isDesktop && (
            <nav className="flex items-center space-x-6">
              <a href="/" className="text-foreground hover:text-primary transition-colors text-sm">
                🏠 Accueil
              </a>
              <a href="/salons" className="text-foreground hover:text-primary transition-colors text-sm">
                🎮 Salons
              </a>
              <a href="/game" className="text-foreground hover:text-primary transition-colors text-sm">
                🎲 Jeu
              </a>
            </nav>
          )}

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Language Selector - Desktop only */}
            {viewport.isDesktop && (
              <select
                value={currentLanguage}
                onChange={(e) => changeLanguage(e.target.value)}
                className="bg-muted text-foreground border border-border rounded px-2 py-1 text-sm"
              >
                {locales.map((locale) => (
                  <option key={locale} value={locale}>
                    {localeNames[locale]}
                  </option>
                ))}
              </select>
            )}

            {/* Mobile Menu Button */}
            {viewport.isMobile && (
              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-2 text-foreground hover:text-primary transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            )}

            {/* Auth Button - Desktop */}
            {viewport.isDesktop && (
              <button className="bg-primary text-black px-4 py-2 rounded font-medium hover:bg-primary/80 transition-colors text-sm">
                Connexion
              </button>
            )}
          </div>
        </div>

        {/* Mobile Menu */}
        {viewport.isMobile && showMobileMenu && (
          <div className="mt-2 py-2 border-t border-border">
            <nav className="flex flex-col space-y-2">
              <a href="/" className="text-foreground hover:text-primary transition-colors text-sm py-1">
                🏠 Accueil
              </a>
              <a href="/salons" className="text-foreground hover:text-primary transition-colors text-sm py-1">
                🎮 Salons
              </a>
              <a href="/game" className="text-foreground hover:text-primary transition-colors text-sm py-1">
                🎲 Jeu
              </a>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
