'use client'

import { useI18n } from '@/lib/i18n/hooks'
import { locales, localeNames } from '@/lib/i18n/config'

export default function Header() {
  const { t, changeLanguage, currentLanguage } = useI18n('common')

  return (
    <header className="bg-card border-b border-border">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="text-2xl font-bold text-primary neon-text">
              {t('appName')}
            </div>
            <div className="text-xs text-muted bg-accent text-black px-2 py-1 rounded">
              BETA
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <a href="/" className="text-foreground hover:text-primary transition-colors">
              {t('navigation.home')}
            </a>
            <a href="/salons" className="text-foreground hover:text-primary transition-colors">
              🎮 Salons
            </a>
            <a href="/test" className="text-foreground hover:text-secondary transition-colors">
              🧪 Test
            </a>
            <a href="/test-db" className="text-foreground hover:text-accent transition-colors">
              🗄️ Test DB
            </a>
            <a href="/debug-supabase" className="text-foreground hover:text-red-400 transition-colors">
              🔧 Debug
            </a>
            <a href="/play" className="text-foreground hover:text-primary transition-colors">
              {t('navigation.play')}
            </a>
            <a href="/profile" className="text-foreground hover:text-primary transition-colors">
              {t('navigation.profile')}
            </a>
          </nav>

          {/* Language Selector & Auth */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <select
              value={currentLanguage}
              onChange={(e) => changeLanguage(e.target.value)}
              className="bg-muted text-foreground border border-border rounded px-2 py-1 text-sm"
            >
              {locales.map((locale) => (
                <option key={locale} value={locale}>
                  {localeNames[locale]}
                </option>
              ))}
            </select>

            {/* Auth Buttons */}
            <button className="bg-primary text-black px-4 py-2 rounded font-medium hover:bg-primary/80 transition-colors">
              {t('buttons.login')}
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}
