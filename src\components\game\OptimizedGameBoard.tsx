import React from 'react'
import { SimpleBoardCase, SimplePlayer } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

interface OptimizedGameBoardProps {
  cases: SimpleBoardCase[]
  players: SimplePlayer[]
  currentPlayer: number
  movingPlayers?: Set<string>
  onCaseClick?: (caseId: number) => void
}

export default function OptimizedGameBoard({
  cases,
  players,
  currentPlayer,
  movingPlayers = new Set(),
  onCaseClick
}: OptimizedGameBoardProps) {
  const viewport = useViewport()

  // Organiser les cases en bordure du plateau
  const getBoardLayout = () => {
    const bottom = cases.slice(0, 11) // Cases 0-10
    const left = cases.slice(11, 20) // Cases 11-19
    const top = cases.slice(20, 31).reverse() // Cases 20-30 (inversées)
    const right = cases.slice(31, 40).reverse() // Cases 31-39 (inversées)
    
    return { bottom, left, top, right }
  }

  const { bottom, left, top, right } = getBoardLayout()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  // Tailles adaptatives améliorées pour un meilleur rendu
  const getBoardDimensions = () => {
    if (viewport.isMobile) {
      return {
        caseSize: 'w-10 h-14',
        cornerSize: 'w-14 h-14',
        textSize: 'text-[7px]',
        iconSize: 'text-sm',
        playerSize: 'w-3 h-3',
        boardPadding: 'p-1',
        gap: 'gap-0'
      }
    } else if (viewport.isTablet) {
      return {
        caseSize: 'w-14 h-18',
        cornerSize: 'w-18 h-18',
        textSize: 'text-[9px]',
        iconSize: 'text-base',
        playerSize: 'w-4 h-4',
        boardPadding: 'p-2',
        gap: 'gap-0'
      }
    } else {
      return {
        caseSize: 'w-18 h-24',
        cornerSize: 'w-24 h-24',
        textSize: 'text-sm',
        iconSize: 'text-lg',
        playerSize: 'w-5 h-5',
        boardPadding: 'p-3',
        gap: 'gap-0'
      }
    }
  }

  const dimensions = getBoardDimensions()

  const getColorForType = (type: string) => {
    switch (type) {
      case 'start': return 'from-green-500 to-green-600'
      case 'property': return 'from-blue-500 to-blue-600'
      case 'chance': return 'from-orange-500 to-orange-600'
      case 'tax': return 'from-red-500 to-red-600'
      case 'corner': return 'from-purple-500 to-purple-600'
      case 'utility': return 'from-yellow-500 to-yellow-600'
      case 'railroad': return 'from-gray-500 to-gray-600'
      default: return 'from-gray-400 to-gray-500'
    }
  }

  const renderCase = (boardCase: SimpleBoardCase, position: 'bottom' | 'left' | 'top' | 'right') => {
    const playersOnCase = getPlayersOnCase(boardCase.id)
    const isCorner = [0, 10, 20, 30].includes(boardCase.id)
    const sizeClass = isCorner ? dimensions.cornerSize : dimensions.caseSize
    
    return (
      <div
        key={boardCase.id}
        className={`
          ${sizeClass}
          relative border-2 border-white/30 shadow-lg
          bg-gradient-to-br ${getColorForType(boardCase.type)}
          hover:scale-105 hover:z-10 hover:shadow-xl transition-all duration-300
          cursor-pointer group overflow-hidden rounded-lg
          ${dimensions.gap}
        `}
        onClick={() => onCaseClick?.(boardCase.id)}
        style={{
          borderTopColor: boardCase.color || 'transparent',
          borderTopWidth: boardCase.type === 'property' ? '6px' : '2px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
        }}
      >
        {/* Effet de brillance au survol */}
        <div className="absolute inset-0 bg-gradient-to-tr from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* Contenu de la case */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center p-1 text-white">
          {/* Icône */}
          <div className={`${dimensions.iconSize} mb-1 drop-shadow-sm`}>
            {boardCase.icon}
          </div>
          
          {/* Nom de la case */}
          <div className={`${dimensions.textSize} font-bold text-center leading-tight drop-shadow-sm`}>
            {boardCase.name}
          </div>
          
          {/* Prix si applicable */}
          {boardCase.price && boardCase.price > 0 && (
            <div className={`${dimensions.textSize} font-bold bg-black/30 px-1 rounded mt-1`}>
              {boardCase.price}€
            </div>
          )}
        </div>
        
        {/* Joueurs sur cette case */}
        {playersOnCase.length > 0 && (
          <div className="absolute -top-1 -right-1 flex flex-wrap gap-0.5 z-20">
            {playersOnCase.slice(0, viewport.isMobile ? 2 : 4).map((player, idx) => (
              <div
                key={player.id}
                className={`
                  ${dimensions.playerSize} rounded-full border border-white shadow-lg
                  flex items-center justify-center font-bold text-white
                  transform transition-all duration-300 hover:scale-125
                  ${player.id === players[currentPlayer]?.id ? 'ring-2 ring-yellow-400 animate-pulse' : ''}
                  ${movingPlayers.has(player.id) ? 'animate-bounce scale-110' : ''}
                `}
                style={{ backgroundColor: player.color }}
                title={player.name}
              >
                {viewport.isMobile ? '' : player.avatar}
              </div>
            ))}
            {playersOnCase.length > (viewport.isMobile ? 2 : 4) && (
              <div className={`${dimensions.playerSize} rounded-full bg-black/70 flex items-center justify-center text-white font-bold text-[6px]`}>
                +{playersOnCase.length - (viewport.isMobile ? 2 : 4)}
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`w-full h-full flex items-center justify-center ${dimensions.boardPadding}`}>
      <div className="relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border-4 border-white/10 overflow-hidden">
        
        {/* Plateau en grille */}
        <div className="grid grid-cols-11 grid-rows-11 gap-0">
          
          {/* Ligne du haut */}
          <div className="col-span-11 flex">
            {top.map(boardCase => renderCase(boardCase, 'top'))}
          </div>
          
          {/* Lignes du milieu */}
          {Array.from({ length: 9 }, (_, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {/* Case de gauche */}
              <div>
                {left[8 - rowIndex] && renderCase(left[8 - rowIndex], 'left')}
              </div>
              
              {/* Centre du plateau */}
              <div className="col-span-9 flex items-center justify-center bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 border border-white/10 relative overflow-hidden">
                {/* Effet de brillance subtil */}
                <div className="absolute inset-0 bg-gradient-to-tr from-white/5 to-transparent" />

                {rowIndex === 4 && (
                  <div className="text-center relative z-10">
                    <div className={`font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 drop-shadow-lg ${viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-4xl'}`}>
                      TheRateRace
                    </div>
                    <div className={`text-white/80 font-semibold ${viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-lg'}`}>
                      .io
                    </div>
                    <div className={`text-white/50 font-medium ${viewport.isMobile ? 'text-[8px]' : viewport.isTablet ? 'text-xs' : 'text-sm'} mt-1`}>
                      Jeu de plateau satirique
                    </div>
                  </div>
                )}
              </div>
              
              {/* Case de droite */}
              <div>
                {right[rowIndex] && renderCase(right[rowIndex], 'right')}
              </div>
            </React.Fragment>
          ))}
          
          {/* Ligne du bas */}
          <div className="col-span-11 flex">
            {bottom.map(boardCase => renderCase(boardCase, 'bottom'))}
          </div>
        </div>
      </div>
    </div>
  )
}
