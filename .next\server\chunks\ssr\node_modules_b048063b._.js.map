{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/void-elements/index.js"], "sourcesContent": ["/**\n * This file automatically generated from `pre-publish.js`.\n * Do not manually edit.\n */\n\nmodule.exports = {\n  \"area\": true,\n  \"base\": true,\n  \"br\": true,\n  \"col\": true,\n  \"embed\": true,\n  \"hr\": true,\n  \"img\": true,\n  \"input\": true,\n  \"link\": true,\n  \"meta\": true,\n  \"param\": true,\n  \"source\": true,\n  \"track\": true,\n  \"wbr\": true\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,OAAO,OAAO,GAAG;IACf,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,UAAU;IACV,SAAS;IACT,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "file": "html-parse-stringify.module.js", "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/html-parse-stringify/src/parse-tag.js", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/html-parse-stringify/src/parse.js", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/html-parse-stringify/src/stringify.js", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/html-parse-stringify/src/index.js"], "sourcesContent": ["import lookup from 'void-elements'\nconst attrRE = /\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g\n\nexport default function stringify(tag) {\n  const res = {\n    type: 'tag',\n    name: '',\n    voidElement: false,\n    attrs: {},\n    children: [],\n  }\n\n  const tagMatch = tag.match(/<\\/?([^\\s]+?)[/\\s>]/)\n  if (tagMatch) {\n    res.name = tagMatch[1]\n    if (\n      lookup[tagMatch[1]] ||\n      tag.charAt(tag.length - 2) === '/'\n    ) {\n      res.voidElement = true\n    }\n\n    // handle comment tag\n    if (res.name.startsWith('!--')) {\n      const endIndex = tag.indexOf('-->')\n      return {\n        type: 'comment',\n        comment: endIndex !== -1 ? tag.slice(4, endIndex) : '',\n      }\n    }\n  }\n\n  const reg = new RegExp(attrRE)\n  let result = null\n  for (;;) {\n    result = reg.exec(tag)\n\n    if (result === null) {\n      break\n    }\n\n    if (!result[0].trim()) {\n      continue\n    }\n\n    if (result[1]) {\n      const attr = result[1].trim()\n      let arr = [attr, '']\n\n      if (attr.indexOf('=') > -1) {\n        arr = attr.split('=')\n      }\n\n      res.attrs[arr[0]] = arr[1]\n      reg.lastIndex--\n    } else if (result[2]) {\n      res.attrs[result[2]] = result[3].trim().substring(1, result[3].length - 1)\n    }\n  }\n\n  return res\n}\n", "import parseTag from './parse-tag'\n\nconst tagRE = /<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g\nconst whitespaceRE = /^\\s*$/\n\n// re-used obj for quick lookups of components\nconst empty = Object.create(null)\n\nexport default function parse(html, options) {\n  options || (options = {})\n  options.components || (options.components = empty)\n  const result = []\n  const arr = []\n  let current\n  let level = -1\n  let inComponent = false\n\n  // handle text at top level\n  if (html.indexOf('<') !== 0) {\n    var end = html.indexOf('<')\n    result.push({\n      type: 'text',\n      content: end === -1 ? html : html.substring(0, end),\n    })\n  }\n\n  html.replace(tagRE, function (tag, index) {\n    if (inComponent) {\n      if (tag !== '</' + current.name + '>') {\n        return\n      } else {\n        inComponent = false\n      }\n    }\n    const isOpen = tag.charAt(1) !== '/'\n    const isComment = tag.startsWith('<!--')\n    const start = index + tag.length\n    const nextChar = html.charAt(start)\n    let parent\n\n    if (isComment) {\n      const comment = parseTag(tag)\n\n      // if we're at root, push new base node\n      if (level < 0) {\n        result.push(comment)\n        return result\n      }\n      parent = arr[level]\n      parent.children.push(comment)\n      return result\n    }\n\n    if (isOpen) {\n      level++\n\n      current = parseTag(tag)\n      if (current.type === 'tag' && options.components[current.name]) {\n        current.type = 'component'\n        inComponent = true\n      }\n\n      if (\n        !current.voidElement &&\n        !inComponent &&\n        nextChar &&\n        nextChar !== '<'\n      ) {\n        current.children.push({\n          type: 'text',\n          content: html.slice(start, html.indexOf('<', start)),\n        })\n      }\n\n      // if we're at root, push new base node\n      if (level === 0) {\n        result.push(current)\n      }\n\n      parent = arr[level - 1]\n\n      if (parent) {\n        parent.children.push(current)\n      }\n\n      arr[level] = current\n    }\n\n    if (!isOpen || current.voidElement) {\n      if (\n        level > -1 &&\n        (current.voidElement || current.name === tag.slice(2, -1))\n      ) {\n        level--\n        // move current up a level to match the end tag\n        current = level === -1 ? result : arr[level]\n      }\n      if (!inComponent && nextChar !== '<' && nextChar) {\n        // trailing text node\n        // if we're at the root, push a base text node. otherwise add as\n        // a child to the current node.\n        parent = level === -1 ? result : arr[level].children\n\n        // calculate correct end of the content slice in case there's\n        // no tag after the text node.\n        const end = html.indexOf('<', start)\n        let content = html.slice(start, end === -1 ? undefined : end)\n        // if a node is nothing but whitespace, collapse it as the spec states:\n        // https://www.w3.org/TR/html4/struct/text.html#h-9.1\n        if (whitespaceRE.test(content)) {\n          content = ' '\n        }\n        // don't add whitespace-only text nodes if they would be trailing text nodes\n        // or if they would be leading whitespace-only text nodes:\n        //  * end > -1 indicates this is not a trailing text node\n        //  * leading node is when level is -1 and parent has length 0\n        if ((end > -1 && level + parent.length >= 0) || content !== ' ') {\n          parent.push({\n            type: 'text',\n            content: content,\n          })\n        }\n      }\n    }\n  })\n\n  return result\n}\n", "function attrString(attrs) {\n  const buff = []\n  for (let key in attrs) {\n    buff.push(key + '=\"' + attrs[key] + '\"')\n  }\n  if (!buff.length) {\n    return ''\n  }\n  return ' ' + buff.join(' ')\n}\n\nfunction stringify(buff, doc) {\n  switch (doc.type) {\n    case 'text':\n      return buff + doc.content\n    case 'tag':\n      buff +=\n        '<' +\n        doc.name +\n        (doc.attrs ? attrString(doc.attrs) : '') +\n        (doc.voidElement ? '/>' : '>')\n      if (doc.voidElement) {\n        return buff\n      }\n      return buff + doc.children.reduce(stringify, '') + '</' + doc.name + '>'\n    case 'comment':\n      buff += '<!--' + doc.comment + '-->'\n      return buff\n  }\n}\n\nexport default function (doc) {\n  return doc.reduce(function (token, rootEl) {\n    return token + stringify('', rootEl)\n  }, '')\n}\n", "import parse from './parse'\nimport stringify from './stringify'\n\nexport default {\n  parse,\n  stringify,\n}\n"], "names": ["attrRE", "stringify", "tag", "res", "type", "name", "voidElement", "attrs", "children", "tagMatch", "match", "lookup", "char<PERSON>t", "length", "startsWith", "endIndex", "indexOf", "comment", "slice", "reg", "RegExp", "result", "exec", "trim", "attr", "arr", "split", "lastIndex", "substring", "tagRE", "whitespaceRE", "empty", "Object", "create", "buff", "doc", "content", "key", "push", "join", "attrString", "reduce", "parse", "html", "options", "components", "current", "level", "inComponent", "end", "replace", "index", "parent", "isOpen", "isComment", "start", "nextChar", "parseTag", "undefined", "test", "token", "rootEl"], "mappings": ";;;;;AACA,IAAMA,IAAS;AAAA,SAESC,EAAUC,CAAAA;IAChC,IAAMC,IAAM;QACVC,MAAM;QACNC,MAAM;QACNC,aAAAA,CAAa;QACbC,OAAO,CAAA;QACPC,UAAU,EAAA;IAAA,GAGNC,IAAWP,EAAIQ,KAAAA,CAAM;IAC3B,IAAID,KAAAA,CACFN,EAAIE,IAAAA,GAAOI,CAAAA,CAAS,EAAA,EAAA,2IAElBE,UAAAA,CAAOF,CAAAA,CAAS,EAAA,CAAA,IACe,QAA/BP,EAAIU,MAAAA,CAAOV,EAAIW,MAAAA,GAAS,EAAA,KAAA,CAExBV,EAAIG,WAAAA,GAAAA,CAAc,CAAA,GAIhBH,EAAIE,IAAAA,CAAKS,UAAAA,CAAW,MAAA,GAAQ;QAC9B,IAAMC,IAAWb,EAAIc,OAAAA,CAAQ;QAC7B,OAAO;YACLZ,MAAM;YACNa,SAAAA,CAAuB,MAAdF,IAAkBb,EAAIgB,KAAAA,CAAM,GAAGH,KAAY;QAAA;IAAA;IAO1D,IAFA,IAAMI,IAAM,IAAIC,OAAOpB,IACnBqB,IAAS,MAII,SAAA,CAFfA,IAASF,EAAIG,IAAAA,CAAKpB,EAAAA,GAMlB,IAAKmB,CAAAA,CAAO,EAAA,CAAGE,IAAAA,IAIf,IAAIF,CAAAA,CAAO,EAAA,EAAI;QACb,IAAMG,IAAOH,CAAAA,CAAO,EAAA,CAAGE,IAAAA,IACnBE,IAAM;YAACD;YAAM;SAAA;QAEbA,EAAKR,OAAAA,CAAQ,OAAA,CAAQ,KAAA,CACvBS,IAAMD,EAAKE,KAAAA,CAAM,IAAA,GAGnBvB,EAAII,KAAAA,CAAMkB,CAAAA,CAAI,EAAA,CAAA,GAAMA,CAAAA,CAAI,EAAA,EACxBN,EAAIQ,SAAAA;IAAAA,OACKN,CAAAA,CAAO,EAAA,IAAA,CAChBlB,EAAII,KAAAA,CAAMc,CAAAA,CAAO,EAAA,CAAA,GAAMA,CAAAA,CAAO,EAAA,CAAGE,IAAAA,GAAOK,SAAAA,CAAU,GAAGP,CAAAA,CAAO,EAAA,CAAGR,MAAAA,GAAS,EAAA;IAI5E,OAAOV;AAAAA;AC1DT,IAAM0B,IAAQ,mDACRC,IAAe,SAGfC,IAAQC,OAAOC,MAAAA,CAAO;ACK5B,SAAShC,EAAUiC,CAAAA,EAAMC,CAAAA;IACvB,OAAQA,EAAI/B,IAAAA;QACV,KAAK;YACH,OAAO8B,IAAOC,EAAIC,OAAAA;QACpB,KAAK;YAMH,OALAF,KACE,MACAC,EAAI9B,IAAAA,GAAAA,CACH8B,EAAI5B,KAAAA,GAnBb,SAAoBA,CAAAA;gBAClB,IAAM2B,IAAO,EAAA;gBACb,IAAK,IAAIG,KAAO9B,EACd2B,EAAKI,IAAAA,CAAKD,IAAM,OAAO9B,CAAAA,CAAM8B,EAAAA,GAAO;gBAEtC,OAAKH,EAAKrB,MAAAA,GAGH,MAAMqB,EAAKK,IAAAA,CAAK,OAFd;YAAA,CAaUC,CAAWL,EAAI5B,KAAAA,IAAS,EAAA,IAAA,CACpC4B,EAAI7B,WAAAA,GAAc,OAAO,GAAA,GACxB6B,EAAI7B,WAAAA,GACC4B,IAEFA,IAAOC,EAAI3B,QAAAA,CAASiC,MAAAA,CAAOxC,GAAW,MAAM,OAAOkC,EAAI9B,IAAAA,GAAO;QACvE,KAAK;YAEH,OADA6B,IAAQ,YAASC,EAAIlB,OAAAA,GAAU;IAAA;AAAA;AAAA,IAAA,ICvBtB;IACbyB,OFIF,SAA8BC,CAAAA,EAAMC,CAAAA;QAClCA,KAAAA,CAAYA,IAAU,CAAA,CAAA,GACtBA,EAAQC,UAAAA,IAAAA,CAAeD,EAAQC,UAAAA,GAAad,CAAAA;QAC5C,IAEIe,GAFEzB,IAAS,EAAA,EACTI,IAAM,EAAA,EAERsB,IAAAA,CAAS,GACTC,IAAAA,CAAc;QAGlB,IAA0B,MAAtBL,EAAK3B,OAAAA,CAAQ,MAAY;YAC3B,IAAIiC,IAAMN,EAAK3B,OAAAA,CAAQ;YACvBK,EAAOiB,IAAAA,CAAK;gBACVlC,MAAM;gBACNgC,SAAAA,CAAkB,MAATa,IAAaN,IAAOA,EAAKf,SAAAA,CAAU,GAAGqB;YAAAA;QAAAA;QAwGnD,OApGAN,EAAKO,OAAAA,CAAQrB,GAAO,SAAU3B,CAAAA,EAAKiD,CAAAA;YACjC,IAAIH,GAAa;gBACf,IAAI9C,MAAQ,OAAO4C,EAAQzC,IAAAA,GAAO,KAChC;gBAEA2C,IAAAA,CAAc;YAAA;YAGlB,IAIII,GAJEC,IAA2B,QAAlBnD,EAAIU,MAAAA,CAAO,IACpB0C,IAAYpD,EAAIY,UAAAA,CAAW,YAC3ByC,IAAQJ,IAAQjD,EAAIW,MAAAA,EACpB2C,IAAWb,EAAK/B,MAAAA,CAAO2C;YAG7B,IAAID,GAAW;gBACb,IAAMrC,IAAUwC,EAASvD;gBAGzB,OAAI6C,IAAQ,IAAA,CACV1B,EAAOiB,IAAAA,CAAKrB,IACLI,CAAAA,IAAAA,CAAAA,CAET+B,IAAS3B,CAAAA,CAAIsB,EAAAA,EACNvC,QAAAA,CAAS8B,IAAAA,CAAKrB,IACdI,CAAAA;YAAAA;YAsCT,IAnCIgC,KAAAA,CACFN,KAGqB,UAAA,CADrBD,IAAUW,EAASvD,EAAAA,EACPE,IAAAA,IAAkBwC,EAAQC,UAAAA,CAAWC,EAAQzC,IAAAA,CAAAA,IAAAA,CACvDyC,EAAQ1C,IAAAA,GAAO,aACf4C,IAAAA,CAAc,CAAA,GAIbF,EAAQxC,WAAAA,IACR0C,KAAAA,CACDQ,KACa,QAAbA,KAEAV,EAAQtC,QAAAA,CAAS8B,IAAAA,CAAK;gBACpBlC,MAAM;gBACNgC,SAASO,EAAKzB,KAAAA,CAAMqC,GAAOZ,EAAK3B,OAAAA,CAAQ,KAAKuC;YAAAA,IAKnC,MAAVR,KACF1B,EAAOiB,IAAAA,CAAKQ,IAAAA,CAGdM,IAAS3B,CAAAA,CAAIsB,IAAQ,EAAA,KAGnBK,EAAO5C,QAAAA,CAAS8B,IAAAA,CAAKQ,IAGvBrB,CAAAA,CAAIsB,EAAAA,GAASD,CAAAA,GAAAA,CAAAA,CAGVO,KAAUP,EAAQxC,WAAAA,KAAAA,CAEnByC,IAAAA,CAAS,KAAA,CACRD,EAAQxC,WAAAA,IAAewC,EAAQzC,IAAAA,KAASH,EAAIgB,KAAAA,CAAM,GAAA,CAAI,EAAA,KAAA,CAEvD6B,KAEAD,IAAAA,CAAqB,MAAXC,IAAe1B,IAASI,CAAAA,CAAIsB,EAAAA,GAAAA,CAEnCC,KAA4B,QAAbQ,KAAoBA,CAAAA,GAAU;gBAIhDJ,IAAAA,CAAoB,MAAXL,IAAe1B,IAASI,CAAAA,CAAIsB,EAAAA,CAAOvC,QAAAA;gBAI5C,IAAMyC,IAAMN,EAAK3B,OAAAA,CAAQ,KAAKuC,IAC1BnB,IAAUO,EAAKzB,KAAAA,CAAMqC,GAAAA,CAAgB,MAATN,IAAAA,KAAaS,IAAYT;gBAGrDnB,EAAa6B,IAAAA,CAAKvB,MAAAA,CACpBA,IAAU,GAAA,GAAA,CAMPa,IAAAA,CAAO,KAAKF,IAAQK,EAAOvC,MAAAA,IAAU,KAAkB,QAAZuB,CAAAA,KAC9CgB,EAAOd,IAAAA,CAAK;oBACVlC,MAAM;oBACNgC,SAASA;gBAAAA;YAAAA;QAAAA,IAOZf;IAAAA;IEzHPpB,WAAAA,SD0BuBkC,CAAAA;QACvB,OAAOA,EAAIM,MAAAA,CAAO,SAAUmB,CAAAA,EAAOC,CAAAA;YACjC,OAAOD,IAAQ3D,EAAU,IAAI4D;QAAAA,GAC5B;IAAA;AAAA;uCAAA", "ignoreList": [0, 1, 2, 3], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/utils.js"], "sourcesContent": ["export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;"], "names": [], "mappings": ";;;;;;;;;;AAAO,MAAM,OAAO,CAAC,MAAM,MAAM,KAAK;IACpC,MAAM,OAAO;QAAC;QAAK;YACjB;YACA,GAAI,QAAQ,CAAC,CAAC;QAChB;KAAE;IACF,IAAI,MAAM,UAAU,QAAQ,SAAS;QACnC,OAAO,KAAK,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ,mBAAmB;IACvE;IACA,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC7D,IAAI,MAAM,UAAU,QAAQ,MAAM;QAChC,KAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI;IAC/B,OAAO,IAAI,SAAS,MAAM;QACxB,QAAQ,IAAI,IAAI;IAClB;AACF;AACA,MAAM,gBAAgB,CAAC;AAChB,MAAM,WAAW,CAAC,MAAM,MAAM,KAAK;IACxC,IAAI,SAAS,QAAQ,aAAa,CAAC,IAAI,EAAE;IACzC,IAAI,SAAS,MAAM,aAAa,CAAC,IAAI,GAAG,IAAI;IAC5C,KAAK,MAAM,MAAM,KAAK;AACxB;AACA,MAAM,YAAY,CAAC,MAAM,KAAO;QAC9B,IAAI,KAAK,aAAa,EAAE;YACtB;QACF,OAAO;YACL,MAAM,cAAc;gBAClB,WAAW;oBACT,KAAK,GAAG,CAAC,eAAe;gBAC1B,GAAG;gBACH;YACF;YACA,KAAK,EAAE,CAAC,eAAe;QACzB;IACF;AACO,MAAM,iBAAiB,CAAC,MAAM,IAAI;IACvC,KAAK,cAAc,CAAC,IAAI,UAAU,MAAM;AAC1C;AACO,MAAM,gBAAgB,CAAC,MAAM,KAAK,IAAI;IAC3C,IAAI,SAAS,KAAK,KAAK;QAAC;KAAG;IAC3B,IAAI,KAAK,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,eAAe,MAAM,IAAI;IACpG,GAAG,OAAO,CAAC,CAAA;QACT,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3D;IACA,KAAK,aAAa,CAAC,KAAK,UAAU,MAAM;AAC1C;AACO,MAAM,qBAAqB,CAAC,IAAI,MAAM,UAAU,CAAC,CAAC;IACvD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;QAC7C,SAAS,MAAM,gBAAgB,0CAA0C;YACvE,WAAW,KAAK,SAAS;QAC3B;QACA,OAAO;IACT;IACA,OAAO,KAAK,kBAAkB,CAAC,IAAI;QACjC,KAAK,QAAQ,GAAG;QAChB,UAAU,CAAC,cAAc;YACvB,IAAI,QAAQ,QAAQ,EAAE,QAAQ,sBAAsB,CAAC,KAAK,aAAa,QAAQ,CAAC,gBAAgB,CAAC,OAAO,IAAI,aAAa,oBAAoB,IAAI,CAAC,eAAe,aAAa,oBAAoB,EAAE,KAAK,OAAO;QAClN;IACF;AACF;AACO,MAAM,iBAAiB,CAAA,YAAa,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,CAAC,SAAS,cAAc,UAAU,MAAM,GAAG,IAAI,YAAY,SAAS;AACnJ,MAAM,WAAW,CAAA,MAAO,OAAO,QAAQ;AACvC,MAAM,WAAW,CAAA,MAAO,OAAO,QAAQ,YAAY,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/unescape.js"], "sourcesContent": ["const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB;AACxB,MAAM,eAAe;IACnB,SAAS;IACT,SAAS;IACT,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,YAAY;IACZ,WAAW;IACX,UAAU;IACV,SAAS;AACX;AACA,MAAM,qBAAqB,CAAA,IAAK,YAAY,CAAC,EAAE;AACxC,MAAM,WAAW,CAAA,OAAQ,KAAK,OAAO,CAAC,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/defaults.js"], "sourcesContent": ["import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;"], "names": [], "mappings": ";;;;AAAA;;AACA,IAAI,iBAAiB;IACnB,UAAU;IACV,eAAe;IACf,qBAAqB;IACrB,4BAA4B;IAC5B,oBAAoB;IACpB,4BAA4B;QAAC;QAAM;QAAU;QAAK;KAAI;IACtD,aAAa;IACb,UAAA,0JAAA,CAAA,WAAQ;AACV;AACO,MAAM,cAAc,CAAC,UAAU,CAAC,CAAC;IACtC,iBAAiB;QACf,GAAG,cAAc;QACjB,GAAG,OAAO;IACZ;AACF;AACO,MAAM,cAAc,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/i18nInstance.js"], "sourcesContent": ["let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;"], "names": [], "mappings": ";;;;AAAA,IAAI;AACG,MAAM,UAAU,CAAA;IACrB,eAAe;AACjB;AACO,MAAM,UAAU,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!isObject(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM,cAAc,CAAC,MAAM;IACzB,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,OAAO,KAAK,KAAK,EAAE,YAAY,KAAK,QAAQ;IAClD,IAAI,aAAa,OAAO,KAAK,MAAM,GAAG;IACtC,OAAO,CAAC,CAAC;AACX;AACA,MAAM,cAAc,CAAA;IAClB,IAAI,CAAC,MAAM,OAAO,EAAE;IACpB,MAAM,WAAW,KAAK,KAAK,EAAE,YAAY,KAAK,QAAQ;IACtD,OAAO,KAAK,KAAK,EAAE,oBAAoB,WAAW,YAAY;AAChE;AACA,MAAM,wBAAwB,CAAA,WAAY,MAAM,OAAO,CAAC,aAAa,SAAS,KAAK,CAAC,qMAAA,CAAA,iBAAc;AAClG,MAAM,aAAa,CAAA,OAAQ,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK;AAC9D,MAAM,aAAa,CAAC,QAAQ;IAC1B,MAAM,YAAY;QAChB,GAAG,MAAM;IACX;IACA,UAAU,KAAK,GAAG,OAAO,MAAM,CAAC,OAAO,KAAK,EAAE,OAAO,KAAK;IAC1D,OAAO;AACT;AACO,MAAM,gBAAgB,CAAC,UAAU,aAAa,MAAM;IACzD,IAAI,CAAC,UAAU,OAAO;IACtB,IAAI,aAAa;IACjB,MAAM,gBAAgB,WAAW;IACjC,MAAM,YAAY,aAAa,6BAA6B,YAAY,0BAA0B,IAAI,EAAE,GAAG,EAAE;IAC7G,cAAc,OAAO,CAAC,CAAC,OAAO;QAC5B,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACnB,cAAc,GAAG,OAAO;YACxB;QACF;QACA,IAAI,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;YACzB,MAAM,EACJ,KAAK,EACL,IAAI,EACL,GAAG;YACJ,MAAM,kBAAkB,OAAO,IAAI,CAAC,OAAO,MAAM;YACjD,MAAM,kBAAkB,UAAU,OAAO,CAAC,QAAQ,CAAC;YACnD,MAAM,gBAAgB,MAAM,QAAQ;YACpC,IAAI,CAAC,iBAAiB,mBAAmB,CAAC,iBAAiB;gBACzD,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;gBAC1B;YACF;YACA,IAAI,CAAC,iBAAiB,CAAC,CAAC,mBAAmB,eAAe,KAAK,MAAM,iBAAiB,EAAE;gBACtF,cAAc,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,WAAW,CAAC,CAAC;gBAC/C;YACF;YACA,IAAI,mBAAmB,oBAAoB,KAAK,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;gBACvE,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;gBACnD;YACF;YACA,MAAM,UAAU,cAAc,eAAe,aAAa,MAAM;YAChE,cAAc,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE,EAAE,WAAW,CAAC,CAAC;YACzD;QACF;QACA,IAAI,UAAU,MAAM;YAClB,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,oBAAoB,CAAC,+BAA+B,CAAC,EAAE;gBAChE;YACF;YACA;QACF;QACA,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACnB,MAAM,EACJ,MAAM,EACN,GAAG,OACJ,GAAG;YACJ,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,MAAM,QAAQ,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,EAAE;gBACxD,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC;gBAC5B;YACF;YACA,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,qBAAqB,CAAC,sFAAsF,CAAC,EAAE;gBACxH;gBACA;YACF;YACA;QACF;QACA,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,qBAAqB,CAAC,sGAAsG,CAAC,EAAE;YACxI;YACA;QACF;IACF;IACA,OAAO;AACT;AACA,MAAM,cAAc,CAAC,UAAU,oBAAoB,cAAc,MAAM,aAAa,eAAe;IACjG,IAAI,iBAAiB,IAAI,OAAO,EAAE;IAClC,MAAM,YAAY,YAAY,0BAA0B,IAAI,EAAE;IAC9D,MAAM,gCAAgC,gBAAgB,IAAI,OAAO,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;IACnH,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,gBAAgB,OAAO;QAAC;KAAa;IAChH,MAAM,OAAO,sBAAsB,CAAC;IACpC,MAAM,UAAU,CAAA;QACd,MAAM,gBAAgB,WAAW;QACjC,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACrB,IAAI,YAAY,QAAQ,QAAQ,YAAY;iBAAa,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,OAAO,MAAM,CAAC,MAAM;QAC9H;IACF;IACA,QAAQ;IACR,MAAM,MAAM,0LAAA,CAAA,UAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,IAAI,CAAC;IAC/C,MAAM,OAAO;QACX,GAAG,IAAI;QACP,GAAG,aAAa;IAClB;IACA,MAAM,cAAc,CAAC,OAAO,MAAM;QAChC,MAAM,SAAS,YAAY;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,KAAK,QAAQ,EAAE;QACrD,OAAO,sBAAsB,WAAW,eAAe,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,oBAAoB,SAAS;IACnH;IACA,MAAM,oBAAoB,CAAC,OAAO,OAAO,KAAK,GAAG;QAC/C,IAAI,MAAM,KAAK,EAAE;YACf,MAAM,QAAQ,GAAG;YACjB,IAAI,IAAI,CAAC,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAC3B,KAAK;YACP,GAAG,SAAS,YAAY;QAC1B,OAAO;YACL,IAAI,IAAI,IAAI,qMAAA,CAAA,WAAQ,CAAC,GAAG,CAAC;gBAAC;aAAM,EAAE,CAAA;gBAChC,MAAM,QAAQ;oBACZ,GAAG,EAAE,KAAK;gBACZ;gBACA,OAAO,MAAM,iBAAiB;gBAC9B,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,IAAI,EAAE;oBAC3B,GAAG,KAAK;oBACR,KAAK;oBACL,KAAK,EAAE,KAAK,CAAC,GAAG,IAAI,EAAE,GAAG;gBAC3B,GAAG,SAAS,OAAO;YACrB;QACF;IACF;IACA,MAAM,SAAS,CAAC,WAAW,SAAS;QAClC,MAAM,aAAa,WAAW;QAC9B,MAAM,WAAW,WAAW;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,MAAM;YACjC,MAAM,qBAAqB,KAAK,QAAQ,EAAE,CAAC,EAAE,EAAE,WAAW,KAAK,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,QAAQ;YAC9I,IAAI,KAAK,IAAI,KAAK,OAAO;gBACvB,IAAI,MAAM,UAAU,CAAC,SAAS,KAAK,IAAI,EAAE,IAAI;gBAC7C,IAAI,CAAC,OAAO,oBAAoB,MAAM,kBAAkB,CAAC,KAAK,IAAI,CAAC;gBACnE,IAAI,cAAc,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,aAAa,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC;gBACzE,IAAI,CAAC,KAAK,MAAM,CAAC;gBACjB,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,MAAM,KAAK,IAAI,WAAW;oBAC9D,OAAO,KAAK,KAAK;gBACnB,GAAG,OAAO;gBACV,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE;gBACjC,MAAM,iCAAiC,aAAa,YAAY,MAAM,SAAS,CAAC,KAAK,WAAW;gBAChG,MAAM,uBAAuB,iCAAiC,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,MAAM,KAAK,IAAI,CAAC;gBACjG,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO,cAAc,CAAC,IAAI,CAAC,oBAAoB,KAAK,IAAI;gBACjH,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;oBACnB,MAAM,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,MAAM,KAAK,QAAQ;oBAC/E,IAAI,IAAI,CAAC;gBACX,OAAO,IAAI,YAAY,UAAU,gCAAgC;oBAC/D,MAAM,QAAQ,YAAY,OAAO,MAAM;oBACvC,kBAAkB,OAAO,OAAO,KAAK;gBACvC,OAAO,IAAI,sBAAsB;oBAC/B,MAAM,QAAQ,OAAO,YAAY,KAAK,QAAQ,EAAE;oBAChD,kBAAkB,OAAO,OAAO,KAAK;gBACvC,OAAO,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,IAAI,IAAI;oBAC9C,IAAI,kBAAkB;wBACpB,MAAM,QAAQ,YAAY,OAAO,MAAM;wBACvC,kBAAkB,OAAO,OAAO,KAAK,GAAG,KAAK,WAAW;oBAC1D,OAAO,IAAI,YAAY,0BAA0B,IAAI,UAAU,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG;wBACtF,IAAI,KAAK,WAAW,EAAE;4BACpB,IAAI,IAAI,CAAC,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE;gCAChC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,GAAG;4BAC1B;wBACF,OAAO;4BACL,MAAM,QAAQ,OAAO,YAAY,KAAK,QAAQ,EAAE;4BAChD,IAAI,IAAI,CAAC,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE;gCAChC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,GAAG;4BAC1B,GAAG;wBACL;oBACF,OAAO,IAAI,KAAK,WAAW,EAAE;wBAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;oBAC7B,OAAO;wBACL,MAAM,QAAQ,OAAO,YAAY,KAAK,QAAQ,EAAE;wBAChD,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;oBAClD;gBACF,OAAO,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,WAAW;oBACxC,MAAM,UAAU,KAAK,QAAQ,CAAC,EAAE,GAAG,qBAAqB;oBACxD,IAAI,SAAS,IAAI,IAAI,CAAC;gBACxB,OAAO;oBACL,kBAAkB,OAAO,oBAAoB,KAAK,GAAG,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC;gBACtF;YACF,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC/B,MAAM,gBAAgB,YAAY,kBAAkB;gBACpD,MAAM,UAAU,iBAAiB,YAAY,QAAQ,CAAC,KAAK,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,MAAM,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,MAAM,KAAK,QAAQ;gBAC3M,IAAI,eAAe;oBACjB,IAAI,IAAI,CAAC,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;wBACpC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,GAAG;oBAC1B,GAAG;gBACL,OAAO;oBACL,IAAI,IAAI,CAAC;gBACX;YACF;YACA,OAAO;QACT,GAAG,EAAE;IACP;IACA,MAAM,SAAS,OAAO;QAAC;YACrB,OAAO;YACP,UAAU,YAAY,EAAE;QAC1B;KAAE,EAAE,KAAK,WAAW,YAAY,EAAE;IAClC,OAAO,YAAY,MAAM,CAAC,EAAE;AAC9B;AACA,MAAM,oBAAoB,CAAC,WAAW,OAAO;IAC3C,MAAM,eAAe,UAAU,GAAG,IAAI;IACtC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QACnC,KAAK;IACP;IACA,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,QAAQ,IAAI,YAAY,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,KAAK,YAAY,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG;QAC1H,OAAO;IACT;IACA,SAAS;QACP,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,qMAAA,CAAA,WAAQ,EAAE,MAAM;IACvC;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;QAClC,KAAK;IACP;AACF;AACA,MAAM,0BAA0B,CAAC,YAAY,cAAgB,WAAW,GAAG,CAAC,CAAC,GAAG,QAAU,kBAAkB,GAAG,OAAO;AACtH,MAAM,2BAA2B,CAAC,YAAY;IAC5C,MAAM,eAAe,CAAC;IACtB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;QAC9B,OAAO,MAAM,CAAC,cAAc;YAC1B,CAAC,EAAE,EAAE,kBAAkB,UAAU,CAAC,EAAE,EAAE,GAAG;QAC3C;IACF;IACA,OAAO;AACT;AACA,MAAM,qBAAqB,CAAC,YAAY,aAAa,MAAM;IACzD,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI,MAAM,OAAO,CAAC,aAAa;QAC7B,OAAO,wBAAwB,YAAY;IAC7C;IACA,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;QACxB,OAAO,yBAAyB,YAAY;IAC9C;IACA,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,4BAA4B,CAAC,sDAAsD,CAAC,EAAE;QACnG;IACF;IACA,OAAO;AACT;AACA,MAAM,kBAAkB,CAAA;IACtB,IAAI,CAAC,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO;IAC9B,IAAI,MAAM,OAAO,CAAC,SAAS,OAAO;IAClC,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,OAAO,OAAO,KAAK,CAAC,OAAO,UAAU,CAAC,OAAO;AAC/F;AACO,SAAS,MAAM,EACpB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,WAAW,CAAC,CAAC,EACb,MAAM,EACN,QAAQ,EACR,UAAU,EACV,EAAE,EACF,MAAM,aAAa,EACnB,GAAG,UAAU,EACb,cAAc,EACd,GAAG,iBACJ;IACC,MAAM,OAAO,iBAAiB,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACpC,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,uBAAuB,CAAC,uEAAuE,CAAC,EAAE;YAC/G;QACF;QACA,OAAO;IACT;IACA,MAAM,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,IAAK,CAAC;IACpD,MAAM,sBAAsB;QAC1B,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,GAAG;QAChB,GAAG,KAAK,OAAO,EAAE,KAAK;IACxB;IACA,IAAI,aAAa,MAAM,EAAE,EAAE,IAAI,KAAK,OAAO,EAAE;IAC7C,aAAa,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QAAC;KAAW,GAAG,cAAc;QAAC;KAAc;IAChF,MAAM,eAAe,cAAc,UAAU,qBAAqB,MAAM;IACxE,MAAM,eAAe,YAAY,gBAAgB,oBAAoB,mBAAmB,IAAI;IAC5F,MAAM,EACJ,YAAY,EACb,GAAG;IACJ,MAAM,MAAM,WAAW,CAAC,eAAe,aAAa,gBAAgB,gBAAgB,gBAAgB,YAAY;IAChH,IAAI,KAAK,OAAO,EAAE,eAAe,kBAAkB;QACjD,SAAS,UAAU,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,IAAI;YAClD,GAAG,MAAM;YACT,GAAG,KAAK,OAAO,CAAC,aAAa,CAAC,gBAAgB;QAChD,IAAI;YACF,GAAG,KAAK,OAAO,CAAC,aAAa,CAAC,gBAAgB;QAChD;IACF;IACA,MAAM,wBAAwB,UAAU,UAAU,aAAa,CAAC,KAAK,OAAO,EAAE,eAAe,gBAAgB,CAAC,WAAW,SAAS,aAAa,GAAG;QAChJ,eAAe;YACb,GAAG,SAAS,aAAa;YACzB,QAAQ;YACR,QAAQ;QACV;IACF;IACA,MAAM,gBAAgB;QACpB,GAAG,QAAQ;QACX,SAAS,WAAW,SAAS,OAAO;QACpC;QACA,GAAG,MAAM;QACT,GAAG,qBAAqB;QACxB;QACA,IAAI;IACN;IACA,MAAM,cAAc,MAAM,EAAE,KAAK,iBAAiB;IAClD,MAAM,sBAAsB,mBAAmB,YAAY,aAAa,MAAM;IAC9E,IAAI,kBAAkB,uBAAuB;IAC7C,IAAI,gBAAgB;IACpB,IAAI,gBAAgB,sBAAsB;QACxC,gBAAgB;QAChB,kBAAkB;IACpB;IACA,MAAM,UAAU,YAAY,iBAAiB,eAAe,aAAa,MAAM,qBAAqB,eAAe;IACnH,MAAM,cAAc,UAAU,oBAAoB,kBAAkB;IACpE,OAAO,cAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,iBAAiB,WAAW;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/initReactI18next.js"], "sourcesContent": ["import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,mBAAmB;IAC9B,MAAM;IACN,MAAK,QAAQ;QACX,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE,SAAS,OAAO,CAAC,KAAK;QAClC,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/context.js"], "sourcesContent": ["import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAChC,MAAM;IACX,aAAc;QACZ,IAAI,CAAC,cAAc,GAAG,CAAC;IACzB;IACA,kBAAkB,UAAU,EAAE;QAC5B,WAAW,OAAO,CAAC,CAAA;YACjB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG;QAC1D;IACF;IACA,oBAAoB;QAClB,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc;IACxC;AACF;AACO,MAAM,sBAAsB,CAAA,eAAgB,OAAM;QACvD,MAAM,yBAAyB,AAAC,MAAM,aAAa,eAAe,GAAG,QAAS,CAAC;QAC/E,MAAM,mBAAmB;QACzB,OAAO;YACL,GAAG,sBAAsB;YACzB,GAAG,gBAAgB;QACrB;IACF;AACO,MAAM,kBAAkB;IAC7B,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACnB,MAAM,aAAa,KAAK,gBAAgB,EAAE,uBAAuB,EAAE;IACnE,MAAM,MAAM,CAAC;IACb,MAAM,mBAAmB,CAAC;IAC1B,KAAK,SAAS,CAAC,OAAO,CAAC,CAAA;QACrB,gBAAgB,CAAC,EAAE,GAAG,CAAC;QACvB,WAAW,OAAO,CAAC,CAAA;YACjB,gBAAgB,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,iBAAiB,CAAC,GAAG,OAAO,CAAC;QAC9D;IACF;IACA,IAAI,gBAAgB,GAAG;IACvB,IAAI,eAAe,GAAG,KAAK,QAAQ;IACnC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/Trans.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEO,SAAS,MAAM,EACpB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,WAAW,CAAC,CAAC,EACb,MAAM,EACN,QAAQ,EACR,UAAU,EACV,EAAE,EACF,MAAM,aAAa,EACnB,GAAG,UAAU,EACb,cAAc,EACd,GAAG,iBACJ;IACC,MAAM,EACJ,MAAM,eAAe,EACrB,WAAW,oBAAoB,EAChC,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,yKAAA,CAAA,cAAW,KAAK,CAAC;IAChC,MAAM,OAAO,iBAAiB,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACvD,MAAM,IAAI,cAAc,MAAM,EAAE,KAAK;IACrC,OAAO,CAAA,GAAA,qKAAA,CAAA,QAAmB,AAAD,EAAE;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,MAAM,GAAG,MAAM,wBAAwB,MAAM,SAAS;QAC1D;QACA,GAAG;QACH;QACA,GAAG,eAAe;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/useTranslation.js"], "sourcesContent": ["import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;AACA,MAAM,cAAc,CAAC,OAAO;IAC1B,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG;IACvC,GAAG;QAAC;QAAO;KAAO;IAClB,OAAO,IAAI,OAAO;AACpB;AACA,MAAM,aAAa,CAAC,MAAM,UAAU,WAAW,YAAc,KAAK,SAAS,CAAC,UAAU,WAAW;AACjG,MAAM,eAAe,CAAC,MAAM,UAAU,WAAW,YAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,WAAW,MAAM,UAAU,WAAW,YAAY;QAAC;QAAM;QAAU;QAAW;KAAU;AAC5J,MAAM,iBAAiB,CAAC,IAAI,QAAQ,CAAC,CAAC;IAC3C,MAAM,EACJ,MAAM,aAAa,EACpB,GAAG;IACJ,MAAM,EACJ,MAAM,eAAe,EACrB,WAAW,oBAAoB,EAChC,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,yKAAA,CAAA,cAAW,KAAK,CAAC;IAChC,MAAM,OAAO,iBAAiB,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACvD,IAAI,QAAQ,CAAC,KAAK,gBAAgB,EAAE,KAAK,gBAAgB,GAAG,IAAI,yKAAA,CAAA,mBAAgB;IAChF,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,uBAAuB;QACtC,MAAM,YAAY,CAAC,GAAG;YACpB,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB,OAAO;YACzC,IAAI,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,YAAY,GAAG,OAAO,mBAAmB,YAAY;YACrH,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,GAAG;QAC9C;QACA,MAAM,cAAc;YAAC;YAAW,CAAC;YAAG;SAAM;QAC1C,YAAY,CAAC,GAAG;QAChB,YAAY,IAAI,GAAG,CAAC;QACpB,YAAY,KAAK,GAAG;QACpB,OAAO;IACT;IACA,IAAI,KAAK,OAAO,CAAC,KAAK,EAAE,MAAM,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,qBAAqB;IAClE,MAAM,cAAc;QAClB,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,GAAG;QAChB,GAAG,KAAK,OAAO,CAAC,KAAK;QACrB,GAAG,KAAK;IACV;IACA,MAAM,EACJ,WAAW,EACX,SAAS,EACV,GAAG;IACJ,IAAI,aAAa,MAAM,wBAAwB,KAAK,OAAO,EAAE;IAC7D,aAAa,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QAAC;KAAW,GAAG,cAAc;QAAC;KAAc;IAChF,KAAK,gBAAgB,CAAC,iBAAiB,GAAG;IAC1C,MAAM,QAAQ,CAAC,KAAK,aAAa,IAAI,KAAK,oBAAoB,KAAK,WAAW,KAAK,CAAC,CAAA,IAAK,CAAA,GAAA,uJAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,MAAM;IACrH,MAAM,WAAW,aAAa,MAAM,MAAM,GAAG,IAAI,MAAM,YAAY,MAAM,KAAK,aAAa,aAAa,UAAU,CAAC,EAAE,EAAE;IACvH,MAAM,OAAO,IAAM;IACnB,MAAM,UAAU,IAAM,WAAW,MAAM,MAAM,GAAG,IAAI,MAAM,YAAY,MAAM,KAAK,aAAa,aAAa,UAAU,CAAC,EAAE,EAAE;IAC1H,MAAM,CAAC,GAAG,KAAK,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,IAAI,WAAW,WAAW,IAAI;IAC9B,IAAI,MAAM,GAAG,EAAE,WAAW,GAAG,MAAM,GAAG,GAAG,UAAU;IACnD,MAAM,mBAAmB,YAAY;IACrC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EACJ,QAAQ,EACR,aAAa,EACd,GAAG;QACJ,UAAU,OAAO,GAAG;QACpB,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,IAAI,MAAM,GAAG,EAAE;gBACb,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,GAAG,EAAE,YAAY;oBACzC,IAAI,UAAU,OAAO,EAAE,KAAK;gBAC9B;YACF,OAAO;gBACL,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;oBAC/B,IAAI,UAAU,OAAO,EAAE,KAAK;gBAC9B;YACF;QACF;QACA,IAAI,SAAS,oBAAoB,qBAAqB,YAAY,UAAU,OAAO,EAAE;YACnF,KAAK;QACP;QACA,MAAM,aAAa;YACjB,IAAI,UAAU,OAAO,EAAE,KAAK;QAC9B;QACA,IAAI,UAAU,MAAM,GAAG,UAAU;QACjC,IAAI,eAAe,MAAM,MAAM,GAAG,eAAe;QACjD,OAAO;YACL,UAAU,OAAO,GAAG;YACpB,IAAI,MAAM,UAAU,MAAM,KAAK,QAAQ,CAAA,IAAK,KAAK,GAAG,CAAC,GAAG;YACxD,IAAI,iBAAiB,MAAM,cAAc,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG;QACrF;IACF,GAAG;QAAC;QAAM;KAAS;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,IAAI,OAAO;YAC9B,KAAK;QACP;IACF,GAAG;QAAC;QAAM;QAAW;KAAM;IAC3B,MAAM,MAAM;QAAC;QAAG;QAAM;KAAM;IAC5B,IAAI,CAAC,GAAG;IACR,IAAI,IAAI,GAAG;IACX,IAAI,KAAK,GAAG;IACZ,IAAI,OAAO,OAAO;IAClB,IAAI,CAAC,SAAS,CAAC,aAAa,OAAO;IACnC,MAAM,IAAI,QAAQ,CAAA;QAChB,IAAI,MAAM,GAAG,EAAE;YACb,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,GAAG,EAAE,YAAY,IAAM;QACnD,OAAO;YACL,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,IAAM;QACzC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/withTranslation.js"], "sourcesContent": ["import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = useTranslation(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return createElement(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,kBAAkB,CAAC,IAAI,UAAU,CAAC,CAAC,GAAK,SAAS,OAAO,gBAAgB;QACnF,SAAS,uBAAuB,EAC9B,YAAY,EACZ,GAAG,MACJ;YACC,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;gBAC1C,GAAG,IAAI;gBACP,WAAW,QAAQ,SAAS;YAC9B;YACA,MAAM,gBAAgB;gBACpB,GAAG,IAAI;gBACP;gBACA;gBACA,QAAQ;YACV;YACA,IAAI,QAAQ,OAAO,IAAI,cAAc;gBACnC,cAAc,GAAG,GAAG;YACtB,OAAO,IAAI,CAAC,QAAQ,OAAO,IAAI,cAAc;gBAC3C,cAAc,YAAY,GAAG;YAC/B;YACA,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB;QACzC;QACA,uBAAuB,WAAW,GAAG,CAAC,uBAAuB,EAAE,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,CAAC,CAAC;QAClG,uBAAuB,gBAAgB,GAAG;QAC1C,MAAM,aAAa,CAAC,OAAO,MAAQ,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,wBAAwB,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;gBAChG,cAAc;YAChB;QACA,OAAO,QAAQ,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAe,AAAD,EAAE,cAAc;IACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import { useTranslation } from './useTranslation.js';\nexport const Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAC,EAC1B,EAAE,EACF,QAAQ,EACR,GAAG,SACJ;IACC,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;IAC5C,OAAO,SAAS,GAAG;QACjB;QACA,KAAK,KAAK,QAAQ;IACpB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/I18nextProvider.js"], "sourcesContent": ["import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AACO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,SAAS,EACT,QAAQ,EACT;IACC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC3B;YACA;QACF,CAAC,GAAG;QAAC;QAAM;KAAU;IACrB,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,yKAAA,CAAA,cAAW,CAAC,QAAQ,EAAE;QACzC;IACF,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/useSSR.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context.js';\nexport const useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;;;AACO,MAAM,SAAS,CAAC,kBAAkB,iBAAiB,QAAQ,CAAC,CAAC;IAClE,MAAM,EACJ,MAAM,aAAa,EACpB,GAAG;IACJ,MAAM,EACJ,MAAM,eAAe,EACtB,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,yKAAA,CAAA,cAAW,KAAK,CAAC;IAChC,MAAM,OAAO,iBAAiB,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACvD,IAAI,KAAK,OAAO,EAAE,SAAS;IAC3B,IAAI,oBAAoB,CAAC,KAAK,oBAAoB,EAAE;QAClD,KAAK,QAAQ,CAAC,aAAa,CAAC,IAAI,GAAG;QACnC,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,MAAM,CAAC,kBAAkB,MAAM,CAAC,CAAC,KAAK;YAC7D,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;gBAChC,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC;YACpC;YACA,OAAO;QACT,GAAG,KAAK,OAAO,CAAC,EAAE;QAClB,KAAK,oBAAoB,GAAG;QAC5B,KAAK,aAAa,GAAG;IACvB;IACA,IAAI,mBAAmB,CAAC,KAAK,uBAAuB,EAAE;QACpD,KAAK,cAAc,CAAC;QACpB,KAAK,uBAAuB,GAAG;IACjC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AACO,MAAM,UAAU,IAAM,SAAS,OAAO,gBAAgB;QAC3D,SAAS,eAAe,EACtB,gBAAgB,EAChB,eAAe,EACf,GAAG,MACJ;YACC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;YACzB,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB;gBACrC,GAAG,IAAI;YACT;QACF;QACA,eAAe,eAAe,GAAG,CAAA,GAAA,yKAAA,CAAA,sBAAmB,AAAD,EAAE;QACrD,eAAe,WAAW,GAAG,CAAC,eAAe,EAAE,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,CAAC,CAAC;QAClF,eAAe,gBAAgB,GAAG;QAClC,OAAO;IACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/react-i18next/dist/es/index.js"], "sourcesContent": ["export { Trans } from './Trans.js';\nexport { Trans as TransWithoutContext } from './TransWithoutContext.js';\nexport { useTranslation } from './useTranslation.js';\nexport { withTranslation } from './withTranslation.js';\nexport { Translation } from './Translation.js';\nexport { I18nextProvider } from './I18nextProvider.js';\nexport { withSSR } from './withSSR.js';\nexport { useSSR } from './useSSR.js';\nexport { initReactI18next } from './initReactI18next.js';\nexport { setDefaults, getDefaults } from './defaults.js';\nexport { setI18n, getI18n } from './i18nInstance.js';\nexport { I18nContext, composeInitialProps, getInitialProps } from './context.js';\nexport const date = () => '';\nexport const time = () => '';\nexport const number = () => '';\nexport const select = () => '';\nexport const plural = () => '';\nexport const selectOrdinal = () => '';"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACO,MAAM,OAAO,IAAM;AACnB,MAAM,OAAO,IAAM;AACnB,MAAM,SAAS,IAAM;AACrB,MAAM,SAAS,IAAM;AACrB,MAAM,SAAS,IAAM;AACrB,MAAM,gBAAgB,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      const keyEndIndex = /{.*}/.test(match[1]) ? match[1].lastIndexOf('}') + 1 : match[1].indexOf(this.formatSeparator);\n      if (keyEndIndex !== -1) {\n        formatters = match[1].slice(keyEndIndex).split(this.formatSeparator).map(elem => elem.trim()).filter(Boolean);\n        match[1] = match[1].slice(0, keyEndIndex);\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (formatters.length) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      const usingLegacyFormatFunction = this.options.interpolation.format && this.options.interpolation.format !== defOpts.interpolation.format;\n      if (usingLegacyFormatFunction) {\n        this.logger.warn(`init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting`);\n      }\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        if (s.formatter.init) s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    if (Intl.Locale) {\n      const l = new Intl.Locale(lng);\n      if (l && l.getTextInfo) {\n        const ti = l.getTextInfo();\n        if (ti && ti.direction) return ti.direction;\n      }\n    }\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    if (lng.toLowerCase().indexOf('-latn') > 1) return 'ltr';\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,MAAM,WAAW,CAAA,MAAO,OAAO,QAAQ;AACvC,MAAM,QAAQ;IACZ,IAAI;IACJ,IAAI;IACJ,MAAM,UAAU,IAAI,QAAQ,CAAC,SAAS;QACpC,MAAM;QACN,MAAM;IACR;IACA,QAAQ,OAAO,GAAG;IAClB,QAAQ,MAAM,GAAG;IACjB,OAAO;AACT;AACA,MAAM,aAAa,CAAA;IACjB,IAAI,UAAU,MAAM,OAAO;IAC3B,OAAO,KAAK;AACd;AACA,MAAM,OAAO,CAAC,GAAG,GAAG;IAClB,EAAE,OAAO,CAAC,CAAA;QACR,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACvB;AACF;AACA,MAAM,4BAA4B;AAClC,MAAM,WAAW,CAAA,MAAO,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,OAAO,CAAC,2BAA2B,OAAO;AACvG,MAAM,uBAAuB,CAAA,SAAU,CAAC,UAAU,SAAS;AAC3D,MAAM,gBAAgB,CAAC,QAAQ,MAAM;IACnC,MAAM,QAAQ,CAAC,SAAS,QAAQ,OAAO,KAAK,KAAK,CAAC;IAClD,IAAI,aAAa;IACjB,MAAO,aAAa,MAAM,MAAM,GAAG,EAAG;QACpC,IAAI,qBAAqB,SAAS,OAAO,CAAC;QAC1C,MAAM,MAAM,SAAS,KAAK,CAAC,WAAW;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,IAAI;QAC7C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,MAAM;YACrD,SAAS,MAAM,CAAC,IAAI;QACtB,OAAO;YACL,SAAS,CAAC;QACZ;QACA,EAAE;IACJ;IACA,IAAI,qBAAqB,SAAS,OAAO,CAAC;IAC1C,OAAO;QACL,KAAK;QACL,GAAG,SAAS,KAAK,CAAC,WAAW;IAC/B;AACF;AACA,MAAM,UAAU,CAAC,QAAQ,MAAM;IAC7B,MAAM,EACJ,GAAG,EACH,CAAC,EACF,GAAG,cAAc,QAAQ,MAAM;IAChC,IAAI,QAAQ,aAAa,KAAK,MAAM,KAAK,GAAG;QAC1C,GAAG,CAAC,EAAE,GAAG;QACT;IACF;IACA,IAAI,IAAI,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAC7B,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;IACpC,IAAI,OAAO,cAAc,QAAQ,GAAG;IACpC,MAAO,KAAK,GAAG,KAAK,aAAa,EAAE,MAAM,CAAE;QACzC,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG;QAC7B,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG;QAC1B,OAAO,cAAc,QAAQ,GAAG;QAChC,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,aAAa;YAClE,KAAK,GAAG,GAAG;QACb;IACF;IACA,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG;AAC/B;AACA,MAAM,WAAW,CAAC,QAAQ,MAAM,UAAU;IACxC,MAAM,EACJ,GAAG,EACH,CAAC,EACF,GAAG,cAAc,QAAQ,MAAM;IAChC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,EAAE;IACrB,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;AACd;AACA,MAAM,UAAU,CAAC,QAAQ;IACvB,MAAM,EACJ,GAAG,EACH,CAAC,EACF,GAAG,cAAc,QAAQ;IAC1B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO;IAC1D,OAAO,GAAG,CAAC,EAAE;AACf;AACA,MAAM,sBAAsB,CAAC,MAAM,aAAa;IAC9C,MAAM,QAAQ,QAAQ,MAAM;IAC5B,IAAI,UAAU,WAAW;QACvB,OAAO;IACT;IACA,OAAO,QAAQ,aAAa;AAC9B;AACA,MAAM,aAAa,CAAC,QAAQ,QAAQ;IAClC,IAAK,MAAM,QAAQ,OAAQ;QACzB,IAAI,SAAS,eAAe,SAAS,eAAe;YAClD,IAAI,QAAQ,QAAQ;gBAClB,IAAI,SAAS,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,YAAY,UAAU,SAAS,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,YAAY,QAAQ;oBACxH,IAAI,WAAW,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;gBAC5C,OAAO;oBACL,WAAW,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;gBACzC;YACF,OAAO;gBACL,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;YAC7B;QACF;IACF;IACA,OAAO;AACT;AACA,MAAM,cAAc,CAAA,MAAO,IAAI,OAAO,CAAC,uCAAuC;AAC9E,IAAI,aAAa;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AACA,MAAM,SAAS,CAAA;IACb,IAAI,SAAS,OAAO;QAClB,OAAO,KAAK,OAAO,CAAC,cAAc,CAAA,IAAK,UAAU,CAAC,EAAE;IACtD;IACA,OAAO;AACT;AACA,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE;IACvB;IACA,UAAU,OAAO,EAAE;QACjB,MAAM,kBAAkB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3C,IAAI,oBAAoB,WAAW;YACjC,OAAO;QACT;QACA,MAAM,YAAY,IAAI,OAAO;QAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC7C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;QAC9C;QACA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS;QAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,OAAO;IACT;AACF;AACA,MAAM,QAAQ;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AACvC,MAAM,iCAAiC,IAAI,YAAY;AACvD,MAAM,sBAAsB,CAAC,KAAK,aAAa;IAC7C,cAAc,eAAe;IAC7B,eAAe,gBAAgB;IAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IAAK,YAAY,OAAO,CAAC,KAAK,KAAK,aAAa,OAAO,CAAC,KAAK;IAChG,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,MAAM,IAAI,+BAA+B,SAAS,CAAC,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAA,IAAK,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACjH,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,KAAK,IAAI,OAAO,CAAC;QACvB,IAAI,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM;YAC3C,UAAU;QACZ;IACF;IACA,OAAO;AACT;AACA,MAAM,WAAW,CAAC,KAAK,MAAM,eAAe,GAAG;IAC7C,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,GAAG,CAAC,KAAK,EAAE;QACb,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,OAAO,OAAO;QAC7D,OAAO,GAAG,CAAC,KAAK;IAClB;IACA,MAAM,SAAS,KAAK,KAAK,CAAC;IAC1B,IAAI,UAAU;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAG;QAClC,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO;QACT;QACA,IAAI;QACJ,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACtC,IAAI,MAAM,GAAG;gBACX,YAAY;YACd;YACA,YAAY,MAAM,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC,SAAS;YACxB,IAAI,SAAS,WAAW;gBACtB,IAAI;oBAAC;oBAAU;oBAAU;iBAAU,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,KAAK,IAAI,OAAO,MAAM,GAAG,GAAG;oBACtF;gBACF;gBACA,KAAK,IAAI,IAAI;gBACb;YACF;QACF;QACA,UAAU;IACZ;IACA,OAAO;AACT;AACA,MAAM,iBAAiB,CAAA,OAAQ,MAAM,QAAQ,KAAK;AAElD,MAAM,gBAAgB;IACpB,MAAM;IACN,KAAI,IAAI;QACN,IAAI,CAAC,MAAM,CAAC,OAAO;IACrB;IACA,MAAK,IAAI;QACP,IAAI,CAAC,MAAM,CAAC,QAAQ;IACtB;IACA,OAAM,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS;IACvB;IACA,QAAO,IAAI,EAAE,IAAI;QACf,SAAS,CAAC,KAAK,EAAE,QAAQ,SAAS;IACpC;AACF;AACA,MAAM;IACJ,YAAY,cAAc,EAAE,WAAU,CAAC,CAAC,CAAE;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB;IAC5B;IACA,KAAK,cAAc,EAAE,WAAU,CAAC,CAAC,EAAE;QACjC,IAAI,CAAC,MAAM,GAAG,SAAQ,MAAM,IAAI;QAChC,IAAI,CAAC,MAAM,GAAG,kBAAkB;QAChC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG,SAAQ,KAAK;IAC5B;IACA,IAAI,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,IAAI;IACvC;IACA,KAAK,GAAG,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,QAAQ,IAAI;IACxC;IACA,MAAM,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,SAAS;IACrC;IACA,UAAU,GAAG,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,QAAQ,wBAAwB;IAC5D;IACA,QAAQ,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;QACpC,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;QACrC,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;QACrE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B;IACA,OAAO,UAAU,EAAE;QACjB,OAAO,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;YAC7B,GAAG;gBACD,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YACzC,CAAC;YACD,GAAG,IAAI,CAAC,OAAO;QACjB;IACF;IACA,MAAM,QAAO,EAAE;QACb,WAAU,YAAW,IAAI,CAAC,OAAO;QACjC,SAAQ,MAAM,GAAG,SAAQ,MAAM,IAAI,IAAI,CAAC,MAAM;QAC9C,OAAO,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;IACjC;AACF;AACA,IAAI,aAAa,IAAI;AAErB,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,CAAC;IACpB;IACA,GAAG,MAAM,EAAE,QAAQ,EAAE;QACnB,OAAO,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI;YACxD,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa;YAC5D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,eAAe;QACrD;QACA,OAAO,IAAI;IACb;IACA,IAAI,KAAK,EAAE,QAAQ,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QAC5B,IAAI,CAAC,UAAU;YACb,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;YAC5B;QACF;QACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;IAC/B;IACA,KAAK,KAAK,EAAE,GAAG,IAAI,EAAE;QACnB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACzB,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO;YACvD,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,cAAc;gBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;oBACtC,YAAY;gBACd;YACF;QACF;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACvB,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;YACrD,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,cAAc;gBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;oBACtC,SAAS,KAAK,CAAC,UAAU;wBAAC;2BAAU;qBAAK;gBAC3C;YACF;QACF;IACF;AACF;AAEA,MAAM,sBAAsB;IAC1B,YAAY,IAAI,EAAE,WAAU;QAC1B,IAAI;YAAC;SAAc;QACnB,WAAW;IACb,CAAC,CAAE;QACD,KAAK;QACL,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,WAAW;YAC3C,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;QAC9B;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,KAAK,WAAW;YAClD,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG;QACrC;IACF;IACA,cAAc,EAAE,EAAE;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG;YACnC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;QACvB;IACF;IACA,iBAAiB,EAAE,EAAE;QACnB,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;QACtC,IAAI,QAAQ,CAAC,GAAG;YACd,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO;QAChC;IACF;IACA,YAAY,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,WAAU,CAAC,CAAC,EAAE;QACtC,MAAM,eAAe,SAAQ,YAAY,KAAK,YAAY,SAAQ,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QAC1G,MAAM,sBAAsB,SAAQ,mBAAmB,KAAK,YAAY,SAAQ,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;QACtI,IAAI;QACJ,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG;YACzB,OAAO,IAAI,KAAK,CAAC;QACnB,OAAO;YACL,OAAO;gBAAC;gBAAK;aAAG;YAChB,IAAI,KAAK;gBACP,IAAI,MAAM,OAAO,CAAC,MAAM;oBACtB,KAAK,IAAI,IAAI;gBACf,OAAO,IAAI,SAAS,QAAQ,cAAc;oBACxC,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC;gBACzB,OAAO;oBACL,KAAK,IAAI,CAAC;gBACZ;YACF;QACF;QACA,MAAM,SAAS,QAAQ,IAAI,CAAC,IAAI,EAAE;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG;YACnD,MAAM,IAAI,CAAC,EAAE;YACb,KAAK,IAAI,CAAC,EAAE;YACZ,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;QAC3B;QACA,IAAI,UAAU,CAAC,uBAAuB,CAAC,SAAS,MAAM,OAAO;QAC7D,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK;IAC/C;IACA,YAAY,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,WAAU;QACzC,QAAQ;IACV,CAAC,EAAE;QACD,MAAM,eAAe,SAAQ,YAAY,KAAK,YAAY,SAAQ,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QAC1G,IAAI,OAAO;YAAC;YAAK;SAAG;QACpB,IAAI,KAAK,OAAO,KAAK,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,gBAAgB;QACrE,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG;YACzB,OAAO,IAAI,KAAK,CAAC;YACjB,QAAQ;YACR,KAAK,IAAI,CAAC,EAAE;QACd;QACA,IAAI,CAAC,aAAa,CAAC;QACnB,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;QACzB,IAAI,CAAC,SAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI,KAAK;IACxD;IACA,aAAa,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,WAAU;QACzC,QAAQ;IACV,CAAC,EAAE;QACD,IAAK,MAAM,KAAK,UAAW;YACzB,IAAI,SAAS,SAAS,CAAC,EAAE,KAAK,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,SAAS,CAAC,EAAE,EAAE;gBACpG,QAAQ;YACV;QACF;QACA,IAAI,CAAC,SAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI;IACnD;IACA,kBAAkB,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,WAAU;QAC/D,QAAQ;QACR,UAAU;IACZ,CAAC,EAAE;QACD,IAAI,OAAO;YAAC;YAAK;SAAG;QACpB,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG;YACzB,OAAO,IAAI,KAAK,CAAC;YACjB,OAAO;YACP,YAAY;YACZ,KAAK,IAAI,CAAC,EAAE;QACd;QACA,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,OAAO,QAAQ,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;QACxC,IAAI,CAAC,SAAQ,QAAQ,EAAE,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAC7D,IAAI,MAAM;YACR,WAAW,MAAM,WAAW;QAC9B,OAAO;YACL,OAAO;gBACL,GAAG,IAAI;gBACP,GAAG,SAAS;YACd;QACF;QACA,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;QACzB,IAAI,CAAC,SAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI;IACnD;IACA,qBAAqB,GAAG,EAAE,EAAE,EAAE;QAC5B,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,KAAK;YACnC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;QAC3B;QACA,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK;IAC5B;IACA,kBAAkB,GAAG,EAAE,EAAE,EAAE;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,QAAQ;IACvC;IACA,kBAAkB,GAAG,EAAE,EAAE,EAAE;QACzB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IAC/B;IACA,kBAAkB,GAAG,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACvB;IACA,4BAA4B,GAAG,EAAE;QAC/B,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC;QACpC,MAAM,IAAI,QAAQ,OAAO,IAAI,CAAC,SAAS,EAAE;QACzC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,IAAK,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,GAAG;IAChE;IACA,SAAS;QACP,OAAO,IAAI,CAAC,IAAI;IAClB;AACF;AAEA,IAAI,gBAAgB;IAClB,YAAY,CAAC;IACb,kBAAiB,MAAM;QACrB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,GAAG;IACjC;IACA,QAAO,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,QAAO,EAAE,UAAU;QAChD,WAAW,OAAO,CAAC,CAAA;YACjB,QAAQ,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,OAAO,KAAK,UAAS,eAAe;QAClF;QACA,OAAO;IACT;AACF;AAEA,MAAM,mBAAmB,CAAC;AAC1B,MAAM,uBAAuB,CAAA,MAAO,CAAC,SAAS,QAAQ,OAAO,QAAQ,aAAa,OAAO,QAAQ;AACjG,MAAM,mBAAmB;IACvB,YAAY,QAAQ,EAAE,WAAU,CAAC,CAAC,CAAE;QAClC,KAAK;QACL,KAAK;YAAC;YAAiB;YAAiB;YAAkB;YAAgB;YAAoB;YAAc;SAAQ,EAAE,UAAU,IAAI;QACpI,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,WAAW;YAC3C,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;QAC9B;QACA,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;IAClC;IACA,eAAe,GAAG,EAAE;QAClB,IAAI,KAAK,IAAI,CAAC,QAAQ,GAAG;IAC3B;IACA,OAAO,GAAG,EAAE,IAAI;QACd,eAAe,CAAC;IAClB,CAAC,EAAE;QACD,MAAM,MAAM;YACV,GAAG,CAAC;QACN;QACA,IAAI,OAAO,MAAM,OAAO;QACxB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK;QACnC,OAAO,UAAU,QAAQ;IAC3B;IACA,eAAe,GAAG,EAAE,GAAG,EAAE;QACvB,IAAI,cAAc,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QAC5F,IAAI,gBAAgB,WAAW,cAAc;QAC7C,MAAM,eAAe,IAAI,YAAY,KAAK,YAAY,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QAClG,IAAI,aAAa,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE;QACvD,MAAM,uBAAuB,eAAe,IAAI,OAAO,CAAC,eAAe,CAAC;QACxE,MAAM,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,oBAAoB,KAAK,aAAa;QAC9L,IAAI,wBAAwB,CAAC,sBAAsB;YACjD,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa;YACnD,IAAI,KAAK,EAAE,MAAM,GAAG,GAAG;gBACrB,OAAO;oBACL;oBACA,YAAY,SAAS,cAAc;wBAAC;qBAAW,GAAG;gBACpD;YACF;YACA,MAAM,QAAQ,IAAI,KAAK,CAAC;YACxB,IAAI,gBAAgB,gBAAgB,gBAAgB,gBAAgB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,aAAa,MAAM,KAAK;YACpI,MAAM,MAAM,IAAI,CAAC;QACnB;QACA,OAAO;YACL;YACA,YAAY,SAAS,cAAc;gBAAC;aAAW,GAAG;QACpD;IACF;IACA,UAAU,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE;QAC1B,IAAI,MAAM,OAAO,MAAM,WAAW;YAChC,GAAG,CAAC;QACN,IAAI;QACJ,IAAI,OAAO,QAAQ,YAAY,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE;YAC5E,MAAM,IAAI,CAAC,OAAO,CAAC,gCAAgC,CAAC;QACtD;QACA,IAAI,OAAO,YAAY,UAAU,MAAM;YACrC,GAAG,GAAG;QACR;QACA,IAAI,CAAC,KAAK,MAAM,CAAC;QACjB,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,OAAO;YAAC,OAAO;SAAM;QAC/C,MAAM,gBAAgB,IAAI,aAAa,KAAK,YAAY,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QACtG,MAAM,eAAe,IAAI,YAAY,KAAK,YAAY,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QAClG,MAAM,EACJ,GAAG,EACH,UAAU,EACX,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,EAAE;QAC/C,MAAM,YAAY,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QACnD,IAAI,cAAc,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QAC5F,IAAI,gBAAgB,WAAW,cAAc;QAC7C,MAAM,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ;QACpC,MAAM,0BAA0B,IAAI,uBAAuB,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB;QACnG,IAAI,KAAK,kBAAkB,UAAU;YACnC,IAAI,yBAAyB;gBAC3B,IAAI,eAAe;oBACjB,OAAO;wBACL,KAAK,GAAG,YAAY,cAAc,KAAK;wBACvC,SAAS;wBACT,cAAc;wBACd,SAAS;wBACT,QAAQ;wBACR,YAAY,IAAI,CAAC,oBAAoB,CAAC;oBACxC;gBACF;gBACA,OAAO,GAAG,YAAY,cAAc,KAAK;YAC3C;YACA,IAAI,eAAe;gBACjB,OAAO;oBACL,KAAK;oBACL,SAAS;oBACT,cAAc;oBACd,SAAS;oBACT,QAAQ;oBACR,YAAY,IAAI,CAAC,oBAAoB,CAAC;gBACxC;YACF;YACA,OAAO;QACT;QACA,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM;QACpC,IAAI,MAAM,UAAU;QACpB,MAAM,aAAa,UAAU,WAAW;QACxC,MAAM,kBAAkB,UAAU,gBAAgB;QAClD,MAAM,WAAW;YAAC;YAAmB;YAAqB;SAAkB;QAC5E,MAAM,aAAa,IAAI,UAAU,KAAK,YAAY,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;QAC1F,MAAM,6BAA6B,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc;QACrF,MAAM,sBAAsB,IAAI,KAAK,KAAK,aAAa,CAAC,SAAS,IAAI,KAAK;QAC1E,MAAM,kBAAkB,WAAW,eAAe,CAAC;QACnD,MAAM,qBAAqB,sBAAsB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,IAAI,KAAK,EAAE,OAAO;QACtG,MAAM,oCAAoC,IAAI,OAAO,IAAI,sBAAsB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,IAAI,KAAK,EAAE;YAC3H,SAAS;QACX,KAAK;QACL,MAAM,wBAAwB,uBAAuB,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK;QACnF,MAAM,eAAe,yBAAyB,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,oBAAoB,CAAC,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,mCAAmC,CAAC,IAAI,IAAI,YAAY;QAC/N,IAAI,gBAAgB;QACpB,IAAI,8BAA8B,CAAC,OAAO,iBAAiB;YACzD,gBAAgB;QAClB;QACA,MAAM,iBAAiB,qBAAqB;QAC5C,MAAM,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;QAChD,IAAI,8BAA8B,iBAAiB,kBAAkB,SAAS,OAAO,CAAC,WAAW,KAAK,CAAC,CAAC,SAAS,eAAe,MAAM,OAAO,CAAC,cAAc,GAAG;YAC7J,IAAI,CAAC,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;gBACrD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB;gBACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,YAAY,eAAe;oBAC3G,GAAG,GAAG;oBACN,IAAI;gBACN,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,wCAAwC,CAAC;gBAC5E,IAAI,eAAe;oBACjB,SAAS,GAAG,GAAG;oBACf,SAAS,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;oBAChD,OAAO;gBACT;gBACA,OAAO;YACT;YACA,IAAI,cAAc;gBAChB,MAAM,iBAAiB,MAAM,OAAO,CAAC;gBACrC,MAAM,OAAO,iBAAiB,EAAE,GAAG,CAAC;gBACpC,MAAM,cAAc,iBAAiB,kBAAkB;gBACvD,IAAK,MAAM,KAAK,cAAe;oBAC7B,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,IAAI;wBAC1D,MAAM,UAAU,GAAG,cAAc,eAAe,GAAG;wBACnD,IAAI,mBAAmB,CAAC,KAAK;4BAC3B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS;gCAChC,GAAG,GAAG;gCACN,cAAc,qBAAqB,gBAAgB,YAAY,CAAC,EAAE,GAAG;gCACrE,GAAG;oCACD,YAAY;oCACZ,IAAI;gCACN,CAAC;4BACH;wBACF,OAAO;4BACL,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS;gCAChC,GAAG,GAAG;gCACN,GAAG;oCACD,YAAY;oCACZ,IAAI;gCACN,CAAC;4BACH;wBACF;wBACA,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE;oBACrD;gBACF;gBACA,MAAM;YACR;QACF,OAAO,IAAI,8BAA8B,SAAS,eAAe,MAAM,OAAO,CAAC,MAAM;YACnF,MAAM,IAAI,IAAI,CAAC;YACf,IAAI,KAAK,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,MAAM,KAAK;QACxD,OAAO;YACL,IAAI,cAAc;YAClB,IAAI,UAAU;YACd,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,iBAAiB;gBAC/C,cAAc;gBACd,MAAM;YACR;YACA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;gBAC5B,UAAU;gBACV,MAAM;YACR;YACA,MAAM,iCAAiC,IAAI,8BAA8B,IAAI,IAAI,CAAC,OAAO,CAAC,8BAA8B;YACxH,MAAM,gBAAgB,kCAAkC,UAAU,YAAY;YAC9E,MAAM,gBAAgB,mBAAmB,iBAAiB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC3F,IAAI,WAAW,eAAe,eAAe;gBAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,cAAc,cAAc,KAAK,WAAW,KAAK,gBAAgB,eAAe;gBAChH,IAAI,cAAc;oBAChB,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;wBAC3B,GAAG,GAAG;wBACN,cAAc;oBAChB;oBACA,IAAI,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACrC;gBACA,IAAI,OAAO,EAAE;gBACb,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ;gBAC3G,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,cAAc,gBAAgB,YAAY,CAAC,EAAE,EAAE;oBAChF,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;wBAC5C,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE;oBAC3B;gBACF,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,OAAO;oBAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ;gBACvE,OAAO;oBACL,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ;gBACpC;gBACA,MAAM,OAAO,CAAC,GAAG,GAAG;oBAClB,MAAM,oBAAoB,mBAAmB,yBAAyB,MAAM,uBAAuB;oBACnG,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;wBAClC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,WAAW,GAAG,mBAAmB,eAAe;oBACpF,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,aAAa;wBAC7C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,mBAAmB,eAAe;oBACvF;oBACA,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,WAAW,GAAG;gBAC3C;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;oBAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,qBAAqB;wBAC1D,KAAK,OAAO,CAAC,CAAA;4BACX,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU;4BAC3D,IAAI,yBAAyB,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,SAAS,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,GAAG;gCAClJ,SAAS,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;4BACrD;4BACA,SAAS,OAAO,CAAC,CAAA;gCACf,KAAK;oCAAC;iCAAS,EAAE,MAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI;4BACjE;wBACF;oBACF,OAAO;wBACL,KAAK,MAAM,KAAK;oBAClB;gBACF;YACF;YACA,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,MAAM,KAAK,UAAU;YACvD,IAAI,WAAW,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;gBACtE,MAAM,GAAG,YAAY,cAAc,KAAK;YAC1C;YACA,IAAI,CAAC,WAAW,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;gBACnE,MAAM,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAAG,GAAG,YAAY,cAAc,KAAK,GAAG,KAAK,cAAc,MAAM,WAAW;YAChK;QACF;QACA,IAAI,eAAe;YACjB,SAAS,GAAG,GAAG;YACf,SAAS,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAChD,OAAO;QACT;QACA,OAAO;IACT;IACA,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE;QAClD,IAAI,IAAI,CAAC,UAAU,EAAE,OAAO;YAC1B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK;gBAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC9C,GAAG,GAAG;YACR,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,OAAO,EAAE;gBAClF;YACF;QACF,OAAO,IAAI,CAAC,IAAI,iBAAiB,EAAE;YACjC,IAAI,IAAI,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBAC5C,GAAG,GAAG;gBACN,GAAG;oBACD,eAAe;wBACb,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;wBAC7B,GAAG,IAAI,aAAa;oBACtB;gBACF,CAAC;YACH;YACA,MAAM,kBAAkB,SAAS,QAAQ,CAAC,KAAK,eAAe,oBAAoB,YAAY,IAAI,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe;YAC5K,IAAI;YACJ,IAAI,iBAAiB;gBACnB,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa;gBACpD,UAAU,MAAM,GAAG,MAAM;YAC3B;YACA,IAAI,OAAO,IAAI,OAAO,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG;YACjE,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,EAAE,OAAO;gBACtD,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC9C,GAAG,IAAI;YACT;YACA,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAE;YAC7F,IAAI,iBAAiB;gBACnB,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa;gBACpD,MAAM,UAAU,MAAM,GAAG,MAAM;gBAC/B,IAAI,UAAU,SAAS,IAAI,IAAI,GAAG;YACpC;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,OAAO;YACrF,IAAI,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;gBAC5D,IAAI,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,OAAO,EAAE;oBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,0CAA0C,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,EAAE;oBACzF,OAAO;gBACT;gBACA,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM;YACjC,GAAG;YACH,IAAI,IAAI,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;QAChD;QACA,MAAM,cAAc,IAAI,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW;QAC/D,MAAM,qBAAqB,SAAS,eAAe;YAAC;SAAY,GAAG;QACnE,IAAI,OAAO,QAAQ,oBAAoB,UAAU,IAAI,kBAAkB,KAAK,OAAO;YACjF,MAAM,cAAc,MAAM,CAAC,oBAAoB,KAAK,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG;gBAC9G,cAAc;oBACZ,GAAG,QAAQ;oBACX,YAAY,IAAI,CAAC,oBAAoB,CAAC;gBACxC;gBACA,GAAG,GAAG;YACR,IAAI,KAAK,IAAI;QACf;QACA,OAAO;IACT;IACA,QAAQ,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,OAAO,OAAO;YAAC;SAAK;QACjC,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC/B,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG;YACzC,MAAM,MAAM,UAAU,GAAG;YACzB,UAAU;YACV,IAAI,aAAa,UAAU,UAAU;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,WAAW,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU;YACnF,MAAM,sBAAsB,IAAI,KAAK,KAAK,aAAa,CAAC,SAAS,IAAI,KAAK;YAC1E,MAAM,wBAAwB,uBAAuB,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK,KAAK;YACnF,MAAM,uBAAuB,IAAI,OAAO,KAAK,aAAa,CAAC,SAAS,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,OAAO,KAAK;YACxI,MAAM,QAAQ,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,WAAW;YACnH,WAAW,OAAO,CAAC,CAAA;gBACjB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ;gBAC/B,SAAS;gBACT,IAAI,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,mBAAmB,SAAS;oBACvH,gBAAgB,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG;oBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,iBAAiB,EAAE,MAAM,IAAI,CAAC,MAAM,mCAAmC,EAAE,OAAO,oBAAoB,CAAC,EAAE;gBAC1I;gBACA,MAAM,OAAO,CAAC,CAAA;oBACZ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ;oBAC/B,UAAU;oBACV,MAAM,YAAY;wBAAC;qBAAI;oBACvB,IAAI,IAAI,CAAC,UAAU,EAAE,eAAe;wBAClC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,KAAK,MAAM,IAAI;oBAC1D,OAAO;wBACL,IAAI;wBACJ,IAAI,qBAAqB,eAAe,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,IAAI,KAAK,EAAE;wBACvF,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;wBACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;wBAC7F,IAAI,qBAAqB;4BACvB,UAAU,IAAI,CAAC,MAAM;4BACrB,IAAI,IAAI,OAAO,IAAI,aAAa,OAAO,CAAC,mBAAmB,GAAG;gCAC5D,UAAU,IAAI,CAAC,MAAM,aAAa,OAAO,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,eAAe;4BACvF;4BACA,IAAI,uBAAuB;gCACzB,UAAU,IAAI,CAAC,MAAM;4BACvB;wBACF;wBACA,IAAI,sBAAsB;4BACxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,OAAO,EAAE;4BACzE,UAAU,IAAI,CAAC;4BACf,IAAI,qBAAqB;gCACvB,UAAU,IAAI,CAAC,aAAa;gCAC5B,IAAI,IAAI,OAAO,IAAI,aAAa,OAAO,CAAC,mBAAmB,GAAG;oCAC5D,UAAU,IAAI,CAAC,aAAa,aAAa,OAAO,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,eAAe;gCAC9F;gCACA,IAAI,uBAAuB;oCACzB,UAAU,IAAI,CAAC,aAAa;gCAC9B;4BACF;wBACF;oBACF;oBACA,IAAI;oBACJ,MAAO,cAAc,UAAU,GAAG,GAAI;wBACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ;4BAC9B,eAAe;4BACf,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,aAAa;wBAClD;oBACF;gBACF;YACF;QACF;QACA,OAAO;YACL,KAAK;YACL;YACA;YACA;YACA;QACF;IACF;IACA,cAAc,GAAG,EAAE;QACjB,OAAO,QAAQ,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,QAAQ,EAAE;IAC5H;IACA,YAAY,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,WAAU,CAAC,CAAC,EAAE;QACvC,IAAI,IAAI,CAAC,UAAU,EAAE,aAAa,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,IAAI,KAAK;QACpF,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,IAAI,KAAK;IACvD;IACA,qBAAqB,WAAU,CAAC,CAAC,EAAE;QACjC,MAAM,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAW;YAAO;YAAQ;YAAe;YAAM;YAAgB;YAAe;YAAiB;YAAiB;YAAc;YAAe;SAAgB;QACxN,MAAM,2BAA2B,SAAQ,OAAO,IAAI,CAAC,SAAS,SAAQ,OAAO;QAC7E,IAAI,OAAO,2BAA2B,SAAQ,OAAO,GAAG;QACxD,IAAI,4BAA4B,OAAO,SAAQ,KAAK,KAAK,aAAa;YACpE,KAAK,KAAK,GAAG,SAAQ,KAAK;QAC5B;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,EAAE;YAC/C,OAAO;gBACL,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC9C,GAAG,IAAI;YACT;QACF;QACA,IAAI,CAAC,0BAA0B;YAC7B,OAAO;gBACL,GAAG,IAAI;YACT;YACA,KAAK,MAAM,OAAO,YAAa;gBAC7B,OAAO,IAAI,CAAC,IAAI;YAClB;QACF;QACA,OAAO;IACT;IACA,OAAO,gBAAgB,QAAO,EAAE;QAC9B,MAAM,SAAS;QACf,IAAK,MAAM,UAAU,SAAS;YAC5B,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAS,WAAW,WAAW,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,KAAK,cAAc,QAAO,CAAC,OAAO,EAAE;gBAC3I,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF;AAEA,MAAM;IACJ,YAAY,QAAO,CAAE;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI;QACnD,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;IAClC;IACA,sBAAsB,IAAI,EAAE;QAC1B,OAAO,eAAe;QACtB,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,OAAO,GAAG,OAAO;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC;QACrB,IAAI,EAAE,MAAM,KAAK,GAAG,OAAO;QAC3B,EAAE,GAAG;QACL,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,WAAW,OAAO,KAAK,OAAO;QAClD,OAAO,IAAI,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC;IACxC;IACA,wBAAwB,IAAI,EAAE;QAC5B,OAAO,eAAe;QACtB,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,OAAO,GAAG,OAAO;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC;QACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;IACrC;IACA,mBAAmB,IAAI,EAAE;QACvB,IAAI,SAAS,SAAS,KAAK,OAAO,CAAC,OAAO,CAAC,GAAG;YAC5C,IAAI;YACJ,IAAI;gBACF,gBAAgB,KAAK,mBAAmB,CAAC,KAAK,CAAC,EAAE;YACnD,EAAE,OAAO,GAAG,CAAC;YACb,IAAI,iBAAiB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC9C,gBAAgB,cAAc,WAAW;YAC3C;YACA,IAAI,eAAe,OAAO;YAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC7B,OAAO,KAAK,WAAW;YACzB;YACA,OAAO;QACT;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,KAAK,WAAW,KAAK;IACpF;IACA,gBAAgB,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;YACjF,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC;QACA,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC;IAClG;IACA,sBAAsB,KAAK,EAAE;QAC3B,IAAI,CAAC,OAAO,OAAO;QACnB,IAAI;QACJ,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,OAAO;YACX,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,QAAQ;QAC/E;QACA,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YACxC,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,OAAO;gBACX,MAAM,YAAY,IAAI,CAAC,qBAAqB,CAAC;gBAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,OAAO,QAAQ;gBACpD,MAAM,UAAU,IAAI,CAAC,uBAAuB,CAAC;gBAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,OAAO,QAAQ;gBAClD,QAAQ,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;oBACtC,IAAI,iBAAiB,SAAS,OAAO;oBACrC,IAAI,aAAa,OAAO,CAAC,OAAO,KAAK,QAAQ,OAAO,CAAC,OAAO,GAAG;oBAC/D,IAAI,aAAa,OAAO,CAAC,OAAO,KAAK,QAAQ,OAAO,CAAC,OAAO,KAAK,aAAa,SAAS,CAAC,GAAG,aAAa,OAAO,CAAC,UAAU,SAAS,OAAO;oBAC1I,IAAI,aAAa,OAAO,CAAC,aAAa,KAAK,QAAQ,MAAM,GAAG,GAAG,OAAO;gBACxE;YACF;QACF;QACA,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;QACtE,OAAO;IACT;IACA,iBAAiB,SAAS,EAAE,IAAI,EAAE;QAChC,IAAI,CAAC,WAAW,OAAO,EAAE;QACzB,IAAI,OAAO,cAAc,YAAY,YAAY,UAAU;QAC3D,IAAI,SAAS,YAAY,YAAY;YAAC;SAAU;QAChD,IAAI,MAAM,OAAO,CAAC,YAAY,OAAO;QACrC,IAAI,CAAC,MAAM,OAAO,UAAU,OAAO,IAAI,EAAE;QACzC,IAAI,QAAQ,SAAS,CAAC,KAAK;QAC3B,IAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM;QAC/D,IAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM;QAC5D,IAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM;QACjE,IAAI,CAAC,OAAO,QAAQ,UAAU,OAAO;QACrC,OAAO,SAAS,EAAE;IACpB;IACA,mBAAmB,IAAI,EAAE,YAAY,EAAE;QACrC,MAAM,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,QAAQ,EAAE,GAAG,YAAY,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,EAAE;QAC5H,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,CAAA;YACd,IAAI,CAAC,GAAG;YACR,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC3B,MAAM,IAAI,CAAC;YACb,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,oDAAoD,EAAE,GAAG;YAC7E;QACF;QACA,IAAI,SAAS,SAAS,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG;YACxE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,gBAAgB,QAAQ,IAAI,CAAC,kBAAkB,CAAC;YAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,eAAe,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YACpH,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,eAAe,QAAQ,IAAI,CAAC,uBAAuB,CAAC;QAChF,OAAO,IAAI,SAAS,OAAO;YACzB,QAAQ,IAAI,CAAC,kBAAkB,CAAC;QAClC;QACA,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA,OAAO;IACT;AACF;AAEA,MAAM,gBAAgB;IACpB,MAAM;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;AACT;AACA,MAAM,YAAY;IAChB,QAAQ,CAAA,QAAS,UAAU,IAAI,QAAQ;IACvC,iBAAiB,IAAM,CAAC;YACtB,kBAAkB;gBAAC;gBAAO;aAAQ;QACpC,CAAC;AACH;AACA,MAAM;IACJ,YAAY,aAAa,EAAE,WAAU,CAAC,CAAC,CAAE;QACvC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC3B;IACA,QAAQ,GAAG,EAAE,GAAG,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,aAAa;QACX,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC3B;IACA,QAAQ,IAAI,EAAE,WAAU,CAAC,CAAC,EAAE;QAC1B,MAAM,cAAc,eAAe,SAAS,QAAQ,OAAO;QAC3D,MAAM,OAAO,SAAQ,OAAO,GAAG,YAAY;QAC3C,MAAM,WAAW,KAAK,SAAS,CAAC;YAC9B;YACA;QACF;QACA,IAAI,YAAY,IAAI,CAAC,gBAAgB,EAAE;YACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;QACxC;QACA,IAAI;QACJ,IAAI;YACF,OAAO,IAAI,KAAK,WAAW,CAAC,aAAa;gBACvC;YACF;QACF,EAAE,OAAO,KAAK;YACZ,IAAI,CAAC,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAClB,OAAO;YACT;YACA,IAAI,CAAC,KAAK,KAAK,CAAC,QAAQ,OAAO;YAC/B,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS;QAC/B;QACA,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG;QAClC,OAAO;IACT;IACA,YAAY,IAAI,EAAE,WAAU,CAAC,CAAC,EAAE;QAC9B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC9B,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;QACtC,OAAO,MAAM,kBAAkB,iBAAiB,SAAS;IAC3D;IACA,oBAAoB,IAAI,EAAE,GAAG,EAAE,WAAU,CAAC,CAAC,EAAE;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,UAAS,GAAG,CAAC,CAAA,SAAU,GAAG,MAAM,QAAQ;IACxE;IACA,YAAY,IAAI,EAAE,WAAU,CAAC,CAAC,EAAE;QAC9B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC9B,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;QACtC,IAAI,CAAC,MAAM,OAAO,EAAE;QACpB,OAAO,KAAK,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,iBAAiB,kBAAoB,aAAa,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAA,iBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,SAAQ,OAAO,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,KAAK,gBAAgB;IACvR;IACA,UAAU,IAAI,EAAE,KAAK,EAAE,WAAU,CAAC,CAAC,EAAE;QACnC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAChC,IAAI,MAAM;YACR,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,SAAQ,OAAO,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,KAAK,KAAK,MAAM,CAAC,QAAQ;QACjH;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,0BAA0B,EAAE,MAAM;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,OAAO;IACtC;AACF;AAEA,MAAM,uBAAuB,CAAC,MAAM,aAAa,KAAK,eAAe,GAAG,EAAE,sBAAsB,IAAI;IAClG,IAAI,OAAO,oBAAoB,MAAM,aAAa;IAClD,IAAI,CAAC,QAAQ,uBAAuB,SAAS,MAAM;QACjD,OAAO,SAAS,MAAM,KAAK;QAC3B,IAAI,SAAS,WAAW,OAAO,SAAS,aAAa,KAAK;IAC5D;IACA,OAAO;AACT;AACA,MAAM,YAAY,CAAA,MAAO,IAAI,OAAO,CAAC,OAAO;AAC5C,MAAM;IACJ,YAAY,WAAU,CAAC,CAAC,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG,UAAS,eAAe,UAAU,CAAC,CAAA,QAAS,KAAK;QAC/D,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,KAAK,WAAU,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC,SAAQ,aAAa,EAAE,SAAQ,aAAa,GAAG;YAClD,aAAa;QACf;QACA,MAAM,EACJ,QAAQ,QAAQ,EAChB,WAAW,EACX,mBAAmB,EACnB,MAAM,EACN,aAAa,EACb,MAAM,EACN,aAAa,EACb,eAAe,EACf,cAAc,EACd,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB,aAAa,EACb,oBAAoB,EACpB,uBAAuB,EACvB,WAAW,EACX,YAAY,EACb,GAAG,SAAQ,aAAa;QACzB,IAAI,CAAC,MAAM,GAAG,aAAa,YAAY,WAAW;QAClD,IAAI,CAAC,WAAW,GAAG,gBAAgB,YAAY,cAAc;QAC7D,IAAI,CAAC,mBAAmB,GAAG,wBAAwB,YAAY,sBAAsB;QACrF,IAAI,CAAC,MAAM,GAAG,SAAS,YAAY,UAAU,iBAAiB;QAC9D,IAAI,CAAC,MAAM,GAAG,SAAS,YAAY,UAAU,iBAAiB;QAC9D,IAAI,CAAC,eAAe,GAAG,mBAAmB;QAC1C,IAAI,CAAC,cAAc,GAAG,iBAAiB,KAAK,kBAAkB;QAC9D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,KAAK,kBAAkB;QACnE,IAAI,CAAC,aAAa,GAAG,gBAAgB,YAAY,iBAAiB,wBAAwB,YAAY;QACtG,IAAI,CAAC,aAAa,GAAG,gBAAgB,YAAY,iBAAiB,wBAAwB,YAAY;QACtG,IAAI,CAAC,uBAAuB,GAAG,2BAA2B;QAC1D,IAAI,CAAC,WAAW,GAAG,eAAe;QAClC,IAAI,CAAC,YAAY,GAAG,iBAAiB,YAAY,eAAe;QAChE,IAAI,CAAC,WAAW;IAClB;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;IAC1C;IACA,cAAc;QACZ,MAAM,mBAAmB,CAAC,gBAAgB;YACxC,IAAI,gBAAgB,WAAW,SAAS;gBACtC,eAAe,SAAS,GAAG;gBAC3B,OAAO;YACT;YACA,OAAO,IAAI,OAAO,SAAS;QAC7B;QACA,IAAI,CAAC,MAAM,GAAG,iBAAiB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;QAC/E,IAAI,CAAC,cAAc,GAAG,iBAAiB,IAAI,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE;QAC3I,IAAI,CAAC,aAAa,GAAG,iBAAiB,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE;IAC7G;IACA,YAAY,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,QAAO,EAAE;QACnC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,cAAc,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC;QAClH,MAAM,eAAe,CAAA;YACnB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,eAAe,IAAI,GAAG;gBACzC,MAAM,OAAO,qBAAqB,MAAM,aAAa,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB;gBACrH,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,WAAW,KAAK;oBAC3D,GAAG,QAAO;oBACV,GAAG,IAAI;oBACP,kBAAkB;gBACpB,KAAK;YACP;YACA,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe;YACxC,MAAM,IAAI,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,GAAG,KAAK;gBAClI,GAAG,QAAO;gBACV,GAAG,IAAI;gBACP,kBAAkB;YACpB;QACF;QACA,IAAI,CAAC,WAAW;QAChB,MAAM,8BAA8B,UAAS,+BAA+B,IAAI,CAAC,OAAO,CAAC,2BAA2B;QACpH,MAAM,kBAAkB,UAAS,eAAe,oBAAoB,YAAY,SAAQ,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe;QAClK,MAAM,QAAQ;YAAC;gBACb,OAAO,IAAI,CAAC,cAAc;gBAC1B,WAAW,CAAA,MAAO,UAAU;YAC9B;YAAG;gBACD,OAAO,IAAI,CAAC,MAAM;gBAClB,WAAW,CAAA,MAAO,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,UAAU;YAC/E;SAAE;QACF,MAAM,OAAO,CAAC,CAAA;YACZ,WAAW;YACX,MAAO,QAAQ,KAAK,KAAK,CAAC,IAAI,CAAC,KAAM;gBACnC,MAAM,aAAa,KAAK,CAAC,EAAE,CAAC,IAAI;gBAChC,QAAQ,aAAa;gBACrB,IAAI,UAAU,WAAW;oBACvB,IAAI,OAAO,gCAAgC,YAAY;wBACrD,MAAM,OAAO,4BAA4B,KAAK,OAAO;wBACrD,QAAQ,SAAS,QAAQ,OAAO;oBAClC,OAAO,IAAI,YAAW,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAS,aAAa;wBAC/E,QAAQ;oBACV,OAAO,IAAI,iBAAiB;wBAC1B,QAAQ,KAAK,CAAC,EAAE;wBAChB;oBACF,OAAO;wBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,mBAAmB,EAAE,KAAK;wBACpF,QAAQ;oBACV;gBACF,OAAO,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBACxD,QAAQ,WAAW;gBACrB;gBACA,MAAM,YAAY,KAAK,SAAS,CAAC;gBACjC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE;gBAC5B,IAAI,iBAAiB;oBACnB,KAAK,KAAK,CAAC,SAAS,IAAI,MAAM,MAAM;oBACpC,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM;gBACzC,OAAO;oBACL,KAAK,KAAK,CAAC,SAAS,GAAG;gBACzB;gBACA;gBACA,IAAI,YAAY,IAAI,CAAC,WAAW,EAAE;oBAChC;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA,KAAK,GAAG,EAAE,EAAE,EAAE,WAAU,CAAC,CAAC,EAAE;QAC1B,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,mBAAmB,CAAC,KAAK;YAC7B,MAAM,MAAM,IAAI,CAAC,uBAAuB;YACxC,IAAI,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO;YACjC,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC;YAC5C,IAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YAC9B,MAAM,CAAC,CAAC,EAAE;YACV,gBAAgB,IAAI,CAAC,WAAW,CAAC,eAAe;YAChD,MAAM,sBAAsB,cAAc,KAAK,CAAC;YAChD,MAAM,sBAAsB,cAAc,KAAK,CAAC;YAChD,IAAI,CAAC,qBAAqB,UAAU,CAAC,IAAI,MAAM,KAAK,CAAC,uBAAuB,oBAAoB,MAAM,GAAG,MAAM,GAAG;gBAChH,gBAAgB,cAAc,OAAO,CAAC,MAAM;YAC9C;YACA,IAAI;gBACF,gBAAgB,KAAK,KAAK,CAAC;gBAC3B,IAAI,kBAAkB,gBAAgB;oBACpC,GAAG,gBAAgB;oBACnB,GAAG,aAAa;gBAClB;YACF,EAAE,OAAO,GAAG;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,iDAAiD,EAAE,KAAK,EAAE;gBAC5E,OAAO,GAAG,MAAM,MAAM,eAAe;YACvC;YACA,IAAI,cAAc,YAAY,IAAI,cAAc,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,OAAO,cAAc,YAAY;YACzH,OAAO;QACT;QACA,MAAO,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAM;YAC3C,IAAI,aAAa,EAAE;YACnB,gBAAgB;gBACd,GAAG,QAAO;YACZ;YACA,gBAAgB,cAAc,OAAO,IAAI,CAAC,SAAS,cAAc,OAAO,IAAI,cAAc,OAAO,GAAG;YACpG,cAAc,kBAAkB,GAAG;YACnC,OAAO,cAAc,YAAY;YACjC,MAAM,cAAc,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe;YACjH,IAAI,gBAAgB,CAAC,GAAG;gBACtB,aAAa,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC;gBACrG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG;YAC/B;YACA,QAAQ,GAAG,iBAAiB,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,gBAAgB;YACxE,IAAI,SAAS,KAAK,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,QAAQ,OAAO;YAC1D,IAAI,CAAC,SAAS,QAAQ,QAAQ,WAAW;YACzC,IAAI,CAAC,OAAO;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK;gBACnE,QAAQ;YACV;YACA,IAAI,WAAW,MAAM,EAAE;gBACrB,QAAQ,WAAW,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,SAAQ,GAAG,EAAE;wBACjE,GAAG,QAAO;wBACV,kBAAkB,KAAK,CAAC,EAAE,CAAC,IAAI;oBACjC,IAAI,MAAM,IAAI;YAChB;YACA,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;QAC1B;QACA,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB,CAAA;IACrB,IAAI,aAAa,UAAU,WAAW,GAAG,IAAI;IAC7C,MAAM,gBAAgB,CAAC;IACvB,IAAI,UAAU,OAAO,CAAC,OAAO,CAAC,GAAG;QAC/B,MAAM,IAAI,UAAU,KAAK,CAAC;QAC1B,aAAa,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI;QACpC,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG;QAC/C,IAAI,eAAe,cAAc,OAAO,OAAO,CAAC,OAAO,GAAG;YACxD,IAAI,CAAC,cAAc,QAAQ,EAAE,cAAc,QAAQ,GAAG,OAAO,IAAI;QACnE,OAAO,IAAI,eAAe,kBAAkB,OAAO,OAAO,CAAC,OAAO,GAAG;YACnE,IAAI,CAAC,cAAc,KAAK,EAAE,cAAc,KAAK,GAAG,OAAO,IAAI;QAC7D,OAAO;YACL,MAAM,OAAO,OAAO,KAAK,CAAC;YAC1B,KAAK,OAAO,CAAC,CAAA;gBACX,IAAI,KAAK;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC;oBACjC,MAAM,MAAM,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,YAAY;oBACtD,MAAM,aAAa,IAAI,IAAI;oBAC3B,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,aAAa,CAAC,WAAW,GAAG;oBAC5D,IAAI,QAAQ,SAAS,aAAa,CAAC,WAAW,GAAG;oBACjD,IAAI,QAAQ,QAAQ,aAAa,CAAC,WAAW,GAAG;oBAChD,IAAI,CAAC,MAAM,MAAM,aAAa,CAAC,WAAW,GAAG,SAAS,KAAK;gBAC7D;YACF;QACF;IACF;IACA,OAAO;QACL;QACA;IACF;AACF;AACA,MAAM,wBAAwB,CAAA;IAC5B,MAAM,QAAQ,CAAC;IACf,OAAO,CAAC,GAAG,GAAG;QACZ,IAAI,cAAc;QAClB,IAAI,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,IAAI,EAAE,YAAY,CAAC,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE;YAC5G,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,EAAE,gBAAgB,CAAC,EAAE;YACxB;QACF;QACA,MAAM,MAAM,IAAI,KAAK,SAAS,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC,IAAI;QACpB,IAAI,CAAC,KAAK;YACR,MAAM,GAAG,eAAe,IAAI;YAC5B,KAAK,CAAC,IAAI,GAAG;QACf;QACA,OAAO,IAAI;IACb;AACF;AACA,MAAM,2BAA2B,CAAA,KAAM,CAAC,GAAG,GAAG,IAAM,GAAG,eAAe,IAAI,GAAG;AAC7E,MAAM;IACJ,YAAY,WAAU,CAAC,CAAC,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,KAAK,QAAQ,EAAE,WAAU;QACvB,eAAe,CAAC;IAClB,CAAC,EAAE;QACD,IAAI,CAAC,eAAe,GAAG,SAAQ,aAAa,CAAC,eAAe,IAAI;QAChE,MAAM,KAAK,SAAQ,mBAAmB,GAAG,wBAAwB;QACjE,IAAI,CAAC,OAAO,GAAG;YACb,QAAQ,GAAG,CAAC,KAAK;gBACf,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,KAAK;oBAC3C,GAAG,GAAG;gBACR;gBACA,OAAO,CAAA,MAAO,UAAU,MAAM,CAAC;YACjC;YACA,UAAU,GAAG,CAAC,KAAK;gBACjB,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,KAAK;oBAC3C,GAAG,GAAG;oBACN,OAAO;gBACT;gBACA,OAAO,CAAA,MAAO,UAAU,MAAM,CAAC;YACjC;YACA,UAAU,GAAG,CAAC,KAAK;gBACjB,MAAM,YAAY,IAAI,KAAK,cAAc,CAAC,KAAK;oBAC7C,GAAG,GAAG;gBACR;gBACA,OAAO,CAAA,MAAO,UAAU,MAAM,CAAC;YACjC;YACA,cAAc,GAAG,CAAC,KAAK;gBACrB,MAAM,YAAY,IAAI,KAAK,kBAAkB,CAAC,KAAK;oBACjD,GAAG,GAAG;gBACR;gBACA,OAAO,CAAA,MAAO,UAAU,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI;YACnD;YACA,MAAM,GAAG,CAAC,KAAK;gBACb,MAAM,YAAY,IAAI,KAAK,UAAU,CAAC,KAAK;oBACzC,GAAG,GAAG;gBACR;gBACA,OAAO,CAAA,MAAO,UAAU,MAAM,CAAC;YACjC;QACF;IACF;IACA,IAAI,IAAI,EAAE,EAAE,EAAE;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK,WAAW,GAAG,IAAI,GAAG,GAAG;IAC5C;IACA,UAAU,IAAI,EAAE,EAAE,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK,WAAW,GAAG,IAAI,GAAG,GAAG,sBAAsB;IAClE;IACA,OAAO,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,WAAU,CAAC,CAAC,EAAE;QACvC,MAAM,UAAU,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe;QACjD,IAAI,QAAQ,MAAM,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;YAC9H,MAAM,YAAY,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,OAAO,CAAC;YAC3D,OAAO,CAAC,EAAE,GAAG;gBAAC,OAAO,CAAC,EAAE;mBAAK,QAAQ,MAAM,CAAC,GAAG;aAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe;QACtF;QACA,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAC,KAAK;YAClC,MAAM,EACJ,UAAU,EACV,aAAa,EACd,GAAG,eAAe;YACnB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC5B,IAAI,YAAY;gBAChB,IAAI;oBACF,MAAM,aAAa,UAAS,cAAc,CAAC,SAAQ,gBAAgB,CAAC,IAAI,CAAC;oBACzE,MAAM,IAAI,WAAW,MAAM,IAAI,WAAW,GAAG,IAAI,SAAQ,MAAM,IAAI,SAAQ,GAAG,IAAI;oBAClF,YAAY,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,GAAG;wBAC3C,GAAG,aAAa;wBAChB,GAAG,QAAO;wBACV,GAAG,UAAU;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB;gBACA,OAAO;YACT,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,YAAY;YACnE;YACA,OAAO;QACT,GAAG;QACH,OAAO;IACT;AACF;AAEA,MAAM,gBAAgB,CAAC,GAAG;IACxB,IAAI,EAAE,OAAO,CAAC,KAAK,KAAK,WAAW;QACjC,OAAO,EAAE,OAAO,CAAC,KAAK;QACtB,EAAE,YAAY;IAChB;AACF;AACA,MAAM,kBAAkB;IACtB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAU,CAAC,CAAC,CAAE;QAClD,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,GAAG,SAAS,aAAa;QAC3C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,gBAAgB,GAAG,SAAQ,gBAAgB,IAAI;QACpD,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG,SAAQ,UAAU,IAAI,IAAI,SAAQ,UAAU,GAAG;QACjE,IAAI,CAAC,YAAY,GAAG,SAAQ,YAAY,IAAI,IAAI,SAAQ,YAAY,GAAG;QACvE,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,OAAO,EAAE,OAAO,UAAU,SAAQ,OAAO,EAAE;IAClD;IACA,UAAU,SAAS,EAAE,UAAU,EAAE,QAAO,EAAE,QAAQ,EAAE;QAClD,MAAM,SAAS,CAAC;QAChB,MAAM,UAAU,CAAC;QACjB,MAAM,kBAAkB,CAAC;QACzB,MAAM,mBAAmB,CAAC;QAC1B,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI,mBAAmB;YACvB,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,IAAI;gBAC3B,IAAI,CAAC,SAAQ,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,KAAK;oBAC5D,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;gBACrB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;qBAAU,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG;oBAClE,IAAI,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO,CAAC,KAAK,GAAG;gBACnD,OAAO;oBACL,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;oBACnB,mBAAmB;oBACnB,IAAI,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO,CAAC,KAAK,GAAG;oBACjD,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,MAAM,CAAC,KAAK,GAAG;oBAC/C,IAAI,gBAAgB,CAAC,GAAG,KAAK,WAAW,gBAAgB,CAAC,GAAG,GAAG;gBACjE;YACF;YACA,IAAI,CAAC,kBAAkB,eAAe,CAAC,IAAI,GAAG;QAChD;QACA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,EAAE;YAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACd;gBACA,cAAc,OAAO,IAAI,CAAC,SAAS,MAAM;gBACzC,QAAQ,CAAC;gBACT,QAAQ,EAAE;gBACV;YACF;QACF;QACA,OAAO;YACL,QAAQ,OAAO,IAAI,CAAC;YACpB,SAAS,OAAO,IAAI,CAAC;YACrB,iBAAiB,OAAO,IAAI,CAAC;YAC7B,kBAAkB,OAAO,IAAI,CAAC;QAChC;IACF;IACA,OAAO,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;QACtB,MAAM,IAAI,KAAK,KAAK,CAAC;QACrB,MAAM,MAAM,CAAC,CAAC,EAAE;QAChB,MAAM,KAAK,CAAC,CAAC,EAAE;QACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,iBAAiB,KAAK,IAAI;QAC7C,IAAI,CAAC,OAAO,MAAM;YAChB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,IAAI,MAAM,WAAW,WAAW;gBAChE,UAAU;YACZ;QACF;QACA,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI;QAC9B,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACpC,MAAM,SAAS,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACjB,SAAS,EAAE,MAAM,EAAE;gBAAC;aAAI,EAAE;YAC1B,cAAc,GAAG;YACjB,IAAI,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,EAAE,YAAY,KAAK,KAAK,CAAC,EAAE,IAAI,EAAE;gBACnC,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;oBAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;oBAC7B,MAAM,aAAa,EAAE,MAAM,CAAC,EAAE;oBAC9B,IAAI,WAAW,MAAM,EAAE;wBACrB,WAAW,OAAO,CAAC,CAAA;4BACjB,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;wBACjD;oBACF;gBACF;gBACA,EAAE,IAAI,GAAG;gBACT,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE;oBACnB,EAAE,QAAQ,CAAC,EAAE,MAAM;gBACrB,OAAO;oBACL,EAAE,QAAQ;gBACZ;YACF;QACF;QACA,IAAI,CAAC,IAAI,CAAC,UAAU;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI;IAC7C;IACA,KAAK,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE;QACnE,IAAI,CAAC,IAAI,MAAM,EAAE,OAAO,SAAS,MAAM,CAAC;QACxC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA;QACF;QACA,IAAI,CAAC,YAAY;QACjB,MAAM,WAAW,CAAC,KAAK;YACrB,IAAI,CAAC,YAAY;YACjB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;gBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,QAAQ;YAChF;YACA,IAAI,OAAO,QAAQ,QAAQ,IAAI,CAAC,UAAU,EAAE;gBAC1C,WAAW;oBACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,QAAQ,QAAQ,GAAG,OAAO,GAAG;gBAC7D,GAAG;gBACH;YACF;YACA,SAAS,KAAK;QAChB;QACA,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QACjD,IAAI,GAAG,MAAM,KAAK,GAAG;YACnB,IAAI;gBACF,MAAM,IAAI,GAAG,KAAK;gBAClB,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,YAAY;oBACrC,EAAE,IAAI,CAAC,CAAA,OAAQ,SAAS,MAAM,OAAO,KAAK,CAAC;gBAC7C,OAAO;oBACL,SAAS,MAAM;gBACjB;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX;YACA;QACF;QACA,OAAO,GAAG,KAAK,IAAI;IACrB;IACA,eAAe,SAAS,EAAE,UAAU,EAAE,WAAU,CAAC,CAAC,EAAE,QAAQ,EAAE;QAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,OAAO,YAAY;QACrB;QACA,IAAI,SAAS,YAAY,YAAY,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QAC3E,IAAI,SAAS,aAAa,aAAa;YAAC;SAAW;QACnD,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,YAAY,UAAS;QAC9D,IAAI,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,OAAO,OAAO,CAAC,MAAM,EAAE;YAC5B,OAAO;QACT;QACA,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,CAAC,OAAO,CAAC;QACf;IACF;IACA,KAAK,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;QACpC,IAAI,CAAC,cAAc,CAAC,WAAW,YAAY,CAAC,GAAG;IACjD;IACA,OAAO,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;QACtC,IAAI,CAAC,cAAc,CAAC,WAAW,YAAY;YACzC,QAAQ;QACV,GAAG;IACL;IACA,QAAQ,IAAI,EAAE,SAAS,EAAE,EAAE;QACzB,MAAM,IAAI,KAAK,KAAK,CAAC;QACrB,MAAM,MAAM,CAAC,CAAC,EAAE;QAChB,MAAM,KAAK,CAAC,CAAC,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,WAAW,WAAW,CAAC,KAAK;YACrD,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,kBAAkB,EAAE,GAAG,cAAc,EAAE,IAAI,OAAO,CAAC,EAAE;YACzF,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,iBAAiB,EAAE,GAAG,cAAc,EAAE,KAAK,EAAE;YACzF,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;QACzB;IACF;IACA,YAAY,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAU,CAAC,CAAC,EAAE,MAAM,KAAO,CAAC,EAAE;QAC5F,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,mBAAmB,YAAY;YACpG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,oBAAoB,EAAE,UAAU,oBAAoB,CAAC,EAAE;YACjG;QACF;QACA,IAAI,QAAQ,aAAa,QAAQ,QAAQ,QAAQ,IAAI;QACrD,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ;YACxB,MAAM,OAAO;gBACX,GAAG,QAAO;gBACV;YACF;YACA,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;YAChD,IAAI,GAAG,MAAM,GAAG,GAAG;gBACjB,IAAI;oBACF,IAAI;oBACJ,IAAI,GAAG,MAAM,KAAK,GAAG;wBACnB,IAAI,GAAG,WAAW,WAAW,KAAK,eAAe;oBACnD,OAAO;wBACL,IAAI,GAAG,WAAW,WAAW,KAAK;oBACpC;oBACA,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,YAAY;wBACrC,EAAE,IAAI,CAAC,CAAA,OAAQ,IAAI,MAAM,OAAO,KAAK,CAAC;oBACxC,OAAO;wBACL,IAAI,MAAM;oBACZ;gBACF,EAAE,OAAO,KAAK;oBACZ,IAAI;gBACN;YACF,OAAO;gBACL,GAAG,WAAW,WAAW,KAAK,eAAe,KAAK;YACpD;QACF;QACA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,KAAK;IACvD;AACF;AAEA,MAAM,MAAM,IAAM,CAAC;QACjB,OAAO;QACP,WAAW;QACX,IAAI;YAAC;SAAc;QACnB,WAAW;YAAC;SAAc;QAC1B,aAAa;YAAC;SAAM;QACpB,YAAY;QACZ,eAAe;QACf,0BAA0B;QAC1B,MAAM;QACN,SAAS;QACT,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,yBAAyB;QACzB,aAAa;QACb,eAAe;QACf,eAAe;QACf,oBAAoB;QACpB,mBAAmB;QACnB,6BAA6B;QAC7B,aAAa;QACb,yBAAyB;QACzB,YAAY;QACZ,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,uBAAuB;QACvB,wBAAwB;QACxB,6BAA6B;QAC7B,yBAAyB;QACzB,kCAAkC,CAAA;YAChC,IAAI,MAAM,CAAC;YACX,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU,MAAM,IAAI,CAAC,EAAE;YAC9C,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE;YACjD,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE;YACjD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9D,MAAM,WAAU,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE;gBAClC,OAAO,IAAI,CAAC,UAAS,OAAO,CAAC,CAAA;oBAC3B,GAAG,CAAC,IAAI,GAAG,QAAO,CAAC,IAAI;gBACzB;YACF;YACA,OAAO;QACT;QACA,eAAe;YACb,aAAa;YACb,QAAQ,CAAA,QAAS;YACjB,QAAQ;YACR,QAAQ;YACR,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,yBAAyB;YACzB,aAAa;YACb,iBAAiB;QACnB;QACA,qBAAqB;IACvB,CAAC;AACD,MAAM,mBAAmB,CAAA;IACvB,IAAI,SAAS,SAAQ,EAAE,GAAG,SAAQ,EAAE,GAAG;QAAC,SAAQ,EAAE;KAAC;IACnD,IAAI,SAAS,SAAQ,WAAW,GAAG,SAAQ,WAAW,GAAG;QAAC,SAAQ,WAAW;KAAC;IAC9E,IAAI,SAAS,SAAQ,UAAU,GAAG,SAAQ,UAAU,GAAG;QAAC,SAAQ,UAAU;KAAC;IAC3E,IAAI,SAAQ,aAAa,EAAE,UAAU,YAAY,GAAG;QAClD,SAAQ,aAAa,GAAG,SAAQ,aAAa,CAAC,MAAM,CAAC;YAAC;SAAS;IACjE;IACA,IAAI,OAAO,SAAQ,aAAa,KAAK,WAAW,SAAQ,SAAS,GAAG,SAAQ,aAAa;IACzF,OAAO;AACT;AAEA,MAAM,OAAO,KAAO;AACpB,MAAM,sBAAsB,CAAA;IAC1B,MAAM,OAAO,OAAO,mBAAmB,CAAC,OAAO,cAAc,CAAC;IAC9D,KAAK,OAAO,CAAC,CAAA;QACX,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY;YACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7B;IACF;AACF;AACA,MAAM,aAAa;IACjB,YAAY,WAAU,CAAC,CAAC,EAAE,QAAQ,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,OAAO,GAAG,iBAAiB;QAChC,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE;QACd;QACA,oBAAoB,IAAI;QACxB,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,SAAQ,OAAO,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC3B,IAAI,CAAC,IAAI,CAAC,UAAS;gBACnB,OAAO,IAAI;YACb;YACA,WAAW;gBACT,IAAI,CAAC,IAAI,CAAC,UAAS;YACrB,GAAG;QACL;IACF;IACA,KAAK,WAAU,CAAC,CAAC,EAAE,QAAQ,EAAE;QAC3B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,OAAO,aAAY,YAAY;YACjC,WAAW;YACX,WAAU,CAAC;QACb;QACA,IAAI,SAAQ,SAAS,IAAI,QAAQ,SAAQ,EAAE,EAAE;YAC3C,IAAI,SAAS,SAAQ,EAAE,GAAG;gBACxB,SAAQ,SAAS,GAAG,SAAQ,EAAE;YAChC,OAAO,IAAI,SAAQ,EAAE,CAAC,OAAO,CAAC,iBAAiB,GAAG;gBAChD,SAAQ,SAAS,GAAG,SAAQ,EAAE,CAAC,EAAE;YACnC;QACF;QACA,MAAM,UAAU;QAChB,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,OAAO;YACV,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,iBAAiB,SAAQ;QAC9B;QACA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YAC3B,GAAG,QAAQ,aAAa;YACxB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QAC/B;QACA,IAAI,SAAQ,YAAY,KAAK,WAAW;YACtC,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,SAAQ,YAAY;QAC7D;QACA,IAAI,SAAQ,WAAW,KAAK,WAAW;YACrC,IAAI,CAAC,OAAO,CAAC,sBAAsB,GAAG,SAAQ,WAAW;QAC3D;QACA,MAAM,sBAAsB,CAAA;YAC1B,IAAI,CAAC,eAAe,OAAO;YAC3B,IAAI,OAAO,kBAAkB,YAAY,OAAO,IAAI;YACpD,OAAO;QACT;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvB,WAAW,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;YACxE,OAAO;gBACL,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO;YACpC;YACA,IAAI;YACJ,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;YACpC,OAAO;gBACL,YAAY;YACd;YACA,MAAM,KAAK,IAAI,aAAa,IAAI,CAAC,OAAO;YACxC,IAAI,CAAC,KAAK,GAAG,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO;YACnE,MAAM,IAAI,IAAI,CAAC,QAAQ;YACvB,EAAE,MAAM,GAAG;YACX,EAAE,aAAa,GAAG,IAAI,CAAC,KAAK;YAC5B,EAAE,aAAa,GAAG;YAClB,EAAE,cAAc,GAAG,IAAI,eAAe,IAAI;gBACxC,SAAS,IAAI,CAAC,OAAO,CAAC,eAAe;gBACrC,sBAAsB,IAAI,CAAC,OAAO,CAAC,oBAAoB;YACzD;YACA,MAAM,4BAA4B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,aAAa,CAAC,MAAM;YACzI,IAAI,2BAA2B;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,0IAA0I,CAAC;YAC/J;YACA,IAAI,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,aAAa,CAAC,MAAM,GAAG;gBAC3H,EAAE,SAAS,GAAG,oBAAoB;gBAClC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS;YACzE;YACA,EAAE,YAAY,GAAG,IAAI,aAAa,IAAI,CAAC,OAAO;YAC9C,EAAE,KAAK,GAAG;gBACR,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;YACvD;YACA,EAAE,gBAAgB,GAAG,IAAI,UAAU,oBAAoB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YAC9G,EAAE,gBAAgB,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG;gBACpC,IAAI,CAAC,IAAI,CAAC,UAAU;YACtB;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBACjC,EAAE,gBAAgB,GAAG,oBAAoB,IAAI,CAAC,OAAO,CAAC,gBAAgB;gBACtE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,EAAE,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO;YAC9F;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC3B,EAAE,UAAU,GAAG,oBAAoB,IAAI,CAAC,OAAO,CAAC,UAAU;gBAC1D,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;YAC/C;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO;YAC5D,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG;gBACjC,IAAI,CAAC,IAAI,CAAC,UAAU;YACtB;YACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;gBAC5B,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI;YACzB;QACF;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM;QAC/C,IAAI,CAAC,UAAU,WAAW;QAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACpF,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;YACnF,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE;QACzE;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACxD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB;QACA,MAAM,WAAW;YAAC;YAAe;YAAqB;YAAqB;SAAoB;QAC/F,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,OAAS,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;QACpD;QACA,MAAM,kBAAkB;YAAC;YAAe;YAAgB;YAAqB;SAAuB;QACpG,gBAAgB,OAAO,CAAC,CAAA;YACtB,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG;gBACjB,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;gBACtB,OAAO,IAAI;YACb;QACF;QACA,MAAM,WAAW;QACjB,MAAM,OAAO;YACX,MAAM,SAAS,CAAC,KAAK;gBACnB,IAAI,CAAC,cAAc,GAAG;gBACtB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACvE,IAAI,CAAC,aAAa,GAAG;gBACrB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,OAAO;gBACtE,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO;gBACrC,SAAS,OAAO,CAAC;gBACjB,SAAS,KAAK;YAChB;YACA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,OAAO,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;YAC/E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;QACxC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACrD;QACF,OAAO;YACL,WAAW,MAAM;QACnB;QACA,OAAO;IACT;IACA,cAAc,QAAQ,EAAE,WAAW,IAAI,EAAE;QACvC,IAAI,eAAe;QACnB,MAAM,UAAU,SAAS,YAAY,WAAW,IAAI,CAAC,QAAQ;QAC7D,IAAI,OAAO,aAAa,YAAY,eAAe;QACnD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;YACnE,IAAI,SAAS,kBAAkB,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO;YAChH,MAAM,SAAS,EAAE;YACjB,MAAM,SAAS,CAAA;gBACb,IAAI,CAAC,KAAK;gBACV,IAAI,QAAQ,UAAU;gBACtB,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC;gBAC5D,KAAK,OAAO,CAAC,CAAA;oBACX,IAAI,MAAM,UAAU;oBACpB,IAAI,OAAO,OAAO,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC;gBACzC;YACF;YACA,IAAI,CAAC,SAAS;gBACZ,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;gBACvF,UAAU,OAAO,CAAC,CAAA,IAAK,OAAO;YAChC,OAAO;gBACL,OAAO;YACT;YACA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAA,IAAK,OAAO;YAC5C,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAA;gBAC3D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ;gBACzF,aAAa;YACf;QACF,OAAO;YACL,aAAa;QACf;IACF;IACA,gBAAgB,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;QAClC,MAAM,WAAW;QACjB,IAAI,OAAO,SAAS,YAAY;YAC9B,WAAW;YACX,OAAO;QACT;QACA,IAAI,OAAO,OAAO,YAAY;YAC5B,WAAW;YACX,KAAK;QACP;QACA,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,SAAS;QAChC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE;QAC7B,IAAI,CAAC,UAAU,WAAW;QAC1B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,IAAI,CAAA;YAC9C,SAAS,OAAO;YAChB,SAAS;QACX;QACA,OAAO;IACT;IACA,IAAI,MAAM,EAAE;QACV,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAC7B,IAAI,CAAC,OAAO,IAAI,EAAE,MAAM,IAAI,MAAM;QAClC,IAAI,OAAO,IAAI,KAAK,WAAW;YAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;QACzB;QACA,IAAI,OAAO,IAAI,KAAK,YAAY,OAAO,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,EAAE;YACzE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACxB;QACA,IAAI,OAAO,IAAI,KAAK,oBAAoB;YACtC,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QAClC;QACA,IAAI,OAAO,IAAI,KAAK,cAAc;YAChC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG;QAC5B;QACA,IAAI,OAAO,IAAI,KAAK,iBAAiB;YACnC,cAAc,gBAAgB,CAAC;QACjC;QACA,IAAI,OAAO,IAAI,KAAK,aAAa;YAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QAC3B;QACA,IAAI,OAAO,IAAI,KAAK,YAAY;YAC9B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC7B;QACA,OAAO,IAAI;IACb;IACA,oBAAoB,CAAC,EAAE;QACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;QAC3B,IAAI;YAAC;YAAU;SAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG;QACvC,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAM;YACjD,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG;YACpC,IAAI;gBAAC;gBAAU;aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG;YAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,YAAY;gBACrD,IAAI,CAAC,gBAAgB,GAAG;gBACxB;YACF;QACF;QACA,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,IAAI;YACxG,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACzB;IACF;IACA,eAAe,GAAG,EAAE,QAAQ,EAAE;QAC5B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,MAAM,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,oBAAoB;QAC9B,MAAM,cAAc,CAAA;YAClB,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC;YAChE,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QACA,MAAM,OAAO,CAAC,KAAK;YACjB,IAAI,GAAG;gBACL,IAAI,IAAI,CAAC,oBAAoB,KAAK,KAAK;oBACrC,YAAY;oBACZ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;oBAC/B,IAAI,CAAC,oBAAoB,GAAG;oBAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB;oBAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB;gBACrC;YACF,OAAO;gBACL,IAAI,CAAC,oBAAoB,GAAG;YAC9B;YACA,SAAS,OAAO,CAAC,CAAC,GAAG,OAAS,IAAI,CAAC,CAAC,IAAI;YACxC,IAAI,UAAU,SAAS,KAAK,CAAC,GAAG,OAAS,IAAI,CAAC,CAAC,IAAI;QACrD;QACA,MAAM,SAAS,CAAA;YACb,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,EAAE;YAC9D,MAAM,KAAK,SAAS,QAAQ,OAAO,QAAQ,IAAI,CAAC,EAAE;YAClD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,SAAS,QAAQ;gBAAC;aAAK,GAAG;YACxI,IAAI,GAAG;gBACL,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,YAAY;gBACd;gBACA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;gBAC9D,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,oBAAoB;YACtD;YACA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAA;gBACpB,KAAK,KAAK;YACZ;QACF;QACA,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACnF,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM;QAC9C,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE;YACzF,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;gBACtD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/C,OAAO;gBACL,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACxC;QACF,OAAO;YACL,OAAO;QACT;QACA,OAAO;IACT;IACA,UAAU,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE;QAC5B,MAAM,SAAS,CAAC,KAAK,MAAM,GAAG;YAC5B,IAAI;YACJ,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,gCAAgC,CAAC;oBAAC;oBAAK;iBAAK,CAAC,MAAM,CAAC;YACvE,OAAO;gBACL,IAAI;oBACF,GAAG,IAAI;gBACT;YACF;YACA,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,OAAO,GAAG;YAC3B,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,OAAO,IAAI;YAC9B,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,OAAO,EAAE;YACxB,IAAI,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,EAAE,SAAS,IAAI,aAAa,OAAO,SAAS;YAClF,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI;YAClD,IAAI;YACJ,IAAI,EAAE,SAAS,IAAI,MAAM,OAAO,CAAC,MAAM;gBACrC,YAAY,IAAI,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,SAAS,GAAG,eAAe,GAAG;YAC9D,OAAO;gBACL,YAAY,EAAE,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,eAAe,KAAK,GAAG;YACpE;YACA,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW;QAC3B;QACA,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,GAAG;QACf,OAAO;YACL,OAAO,IAAI,GAAG;QAChB;QACA,OAAO,EAAE,GAAG;QACZ,OAAO,SAAS,GAAG;QACnB,OAAO;IACT;IACA,EAAE,GAAG,IAAI,EAAE;QACT,OAAO,IAAI,CAAC,UAAU,EAAE,aAAa;IACvC;IACA,OAAO,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,UAAU,EAAE,UAAU;IACpC;IACA,oBAAoB,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;IAC3B;IACA,mBAAmB,EAAE,EAAE,WAAU,CAAC,CAAC,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mDAAmD,IAAI,CAAC,SAAS;YAClF,OAAO;QACT;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8DAA8D,IAAI,CAAC,SAAS;YAC7F,OAAO;QACT;QACA,MAAM,MAAM,SAAQ,GAAG,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;QACrE,MAAM,cAAc,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;QAC9D,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;QACzD,IAAI,IAAI,WAAW,OAAO,UAAU,OAAO;QAC3C,MAAM,iBAAiB,CAAC,GAAG;YACzB,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;YACnE,OAAO,cAAc,CAAC,KAAK,cAAc,KAAK,cAAc;QAC9D;QACA,IAAI,SAAQ,QAAQ,EAAE;YACpB,MAAM,YAAY,SAAQ,QAAQ,CAAC,IAAI,EAAE;YACzC,IAAI,cAAc,WAAW,OAAO;QACtC;QACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,KAAK,OAAO;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,OAAO;QACvH,IAAI,eAAe,KAAK,OAAO,CAAC,CAAC,eAAe,eAAe,SAAS,GAAG,GAAG,OAAO;QACrF,OAAO;IACT;IACA,eAAe,EAAE,EAAE,QAAQ,EAAE;QAC3B,MAAM,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YACpB,IAAI,UAAU;YACd,OAAO,QAAQ,OAAO;QACxB;QACA,IAAI,SAAS,KAAK,KAAK;YAAC;SAAG;QAC3B,GAAG,OAAO,CAAC,CAAA;YACT,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;QAC3D;QACA,IAAI,CAAC,aAAa,CAAC,CAAA;YACjB,SAAS,OAAO;YAChB,IAAI,UAAU,SAAS;QACzB;QACA,OAAO;IACT;IACA,cAAc,IAAI,EAAE,QAAQ,EAAE;QAC5B,MAAM,WAAW;QACjB,IAAI,SAAS,OAAO,OAAO;YAAC;SAAK;QACjC,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;QAC5C,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,MAAO,UAAU,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC;QAC7G,IAAI,CAAC,QAAQ,MAAM,EAAE;YACnB,IAAI,UAAU;YACd,OAAO,QAAQ,OAAO;QACxB;QACA,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,MAAM,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,CAAA;YACjB,SAAS,OAAO;YAChB,IAAI,UAAU,SAAS;QACzB;QACA,OAAO;IACT;IACA,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ;QACxG,IAAI,CAAC,KAAK,OAAO;QACjB,IAAI,KAAK,MAAM,EAAE;YACf,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC;YAC1B,IAAI,KAAK,EAAE,WAAW,EAAE;gBACtB,MAAM,KAAK,EAAE,WAAW;gBACxB,IAAI,MAAM,GAAG,SAAS,EAAE,OAAO,GAAG,SAAS;YAC7C;QACF;QACA,MAAM,UAAU;YAAC;YAAM;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAM;YAAM;YAAM;YAAO;YAAO;YAAO;YAAO;YAAO;YAAM;YAAM;YAAO;YAAO;YAAO;YAAM;YAAM;YAAO;YAAO;YAAO;YAAM;YAAO;YAAO;YAAO;YAAO;YAAM;YAAO;SAAM;QACxb,MAAM,gBAAgB,IAAI,CAAC,QAAQ,EAAE,iBAAiB,IAAI,aAAa;QACvE,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG,OAAO;QACnD,OAAO,QAAQ,OAAO,CAAC,cAAc,uBAAuB,CAAC,QAAQ,CAAC,KAAK,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,QAAQ;IAC9H;IACA,OAAO,eAAe,WAAU,CAAC,CAAC,EAAE,QAAQ,EAAE;QAC5C,OAAO,IAAI,KAAK,UAAS;IAC3B;IACA,cAAc,WAAU,CAAC,CAAC,EAAE,WAAW,IAAI,EAAE;QAC3C,MAAM,oBAAoB,SAAQ,iBAAiB;QACnD,IAAI,mBAAmB,OAAO,SAAQ,iBAAiB;QACvD,MAAM,gBAAgB;YACpB,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,QAAO;YACV,GAAG;gBACD,SAAS;YACX,CAAC;QACH;QACA,MAAM,QAAQ,IAAI,KAAK;QACvB,IAAI,SAAQ,KAAK,KAAK,aAAa,SAAQ,MAAM,KAAK,WAAW;YAC/D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;QACpC;QACA,MAAM,gBAAgB;YAAC;YAAS;YAAY;SAAW;QACvD,cAAc,OAAO,CAAC,CAAA;YACpB,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACpB;QACA,MAAM,QAAQ,GAAG;YACf,GAAG,IAAI,CAAC,QAAQ;QAClB;QACA,MAAM,QAAQ,CAAC,KAAK,GAAG;YACrB,oBAAoB,MAAM,kBAAkB,CAAC,IAAI,CAAC;QACpD;QACA,IAAI,mBAAmB;YACrB,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,MAAM;gBAC5D,IAAI,CAAC,EAAE,GAAG;oBACR,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACvB;gBACA,IAAI,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK;oBAC1C,GAAG,CAAC,EAAE,GAAG;wBACP,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE;oBACf;oBACA,OAAO;gBACT,GAAG,IAAI,CAAC,EAAE;gBACV,OAAO;YACT,GAAG,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,cAAc,YAAY;YAC5C,MAAM,QAAQ,CAAC,aAAa,GAAG,MAAM,KAAK;QAC5C;QACA,MAAM,UAAU,GAAG,IAAI,WAAW,MAAM,QAAQ,EAAE;QAClD,MAAM,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG;YAClC,MAAM,IAAI,CAAC,UAAU;QACvB;QACA,MAAM,IAAI,CAAC,eAAe;QAC1B,MAAM,UAAU,CAAC,OAAO,GAAG;QAC3B,MAAM,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,GAAG;YACjD,oBAAoB,MAAM,kBAAkB,CAAC,IAAI,CAAC;QACpD;QACA,OAAO;IACT;IACA,SAAS;QACP,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,OAAO,IAAI,CAAC,KAAK;YACjB,UAAU,IAAI,CAAC,QAAQ;YACvB,WAAW,IAAI,CAAC,SAAS;YACzB,kBAAkB,IAAI,CAAC,gBAAgB;QACzC;IACF;AACF;AACA,MAAM,WAAW,KAAK,cAAc;AACpC,SAAS,cAAc,GAAG,KAAK,cAAc;AAE7C,MAAM,iBAAiB,SAAS,cAAc;AAC9C,MAAM,MAAM,SAAS,GAAG;AACxB,MAAM,OAAO,SAAS,IAAI;AAC1B,MAAM,gBAAgB,SAAS,aAAa;AAC5C,MAAM,kBAAkB,SAAS,eAAe;AAChD,MAAM,MAAM,SAAS,GAAG;AACxB,MAAM,iBAAiB,SAAS,cAAc;AAC9C,MAAM,YAAY,SAAS,SAAS;AACpC,MAAM,IAAI,SAAS,CAAC;AACpB,MAAM,SAAS,SAAS,MAAM;AAC9B,MAAM,sBAAsB,SAAS,mBAAmB;AACxD,MAAM,qBAAqB,SAAS,kBAAkB;AACtD,MAAM,iBAAiB,SAAS,cAAc;AAC9C,MAAM,gBAAgB,SAAS,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, value, cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name, domain) {\n    this.create(name, '', -1, domain);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hash = {\n  name: 'hash',\n  // Deconstruct the options object and extract the lookupHash property and the lookupFromHashIndex property\n  lookup(_ref) {\n    let {\n      lookupHash,\n      lookupFromHashIndex\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      const {\n        hash\n      } = window.location;\n      if (hash && hash.length > 2) {\n        const query = hash.substring(1);\n        if (lookupHash) {\n          const params = query.split('&');\n          for (let i = 0; i < params.length; i++) {\n            const pos = params[i].indexOf('=');\n            if (pos > 0) {\n              const key = params[i].substring(0, pos);\n              if (key === lookupHash) {\n                found = params[i].substring(pos + 1);\n              }\n            }\n          }\n        }\n        if (found) return found;\n        if (!found && lookupFromHashIndex > -1) {\n          const language = hash.match(/\\/([a-zA-Z-]*)/g);\n          if (!Array.isArray(language)) return undefined;\n          const index = typeof lookupFromHashIndex === 'number' ? lookupFromHashIndex : 0;\n          return language[index]?.replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n    this.addDetector(hash);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,EACJ,KAAK,EACL,OAAO,EACR,GAAG,EAAE;AACN,SAAS,SAAS,GAAG;IACnB,QAAQ,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,CAAA;QACrC,IAAI,QAAQ;YACV,IAAK,MAAM,QAAQ,OAAQ;gBACzB,IAAI,GAAG,CAAC,KAAK,KAAK,WAAW,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;YACvD;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,OAAO,KAAK;IACnB,IAAI,OAAO,UAAU,UAAU,OAAO;IAEtC,6BAA6B;IAC7B,MAAM,cAAc;QAAC;QAAmB;QAAwB;QAAwB;QAA4B;QAAmB;QAAiB;QAAoB;QAAc;QAAe;QAAqB;QAAyB;QAAqB;KAAa;IACzR,OAAO,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAClD;AAEA,4CAA4C;AAC5C,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB,SAAU,IAAI,EAAE,GAAG;IACzC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAChF,MAAM;IACR;IACA,MAAM,MAAM;IACZ,MAAM,QAAQ,mBAAmB;IACjC,IAAI,MAAM,GAAG,KAAK,CAAC,EAAE,OAAO;IAC5B,IAAI,IAAI,MAAM,GAAG,GAAG;QAClB,MAAM,SAAS,IAAI,MAAM,GAAG;QAC5B,IAAI,OAAO,KAAK,CAAC,SAAS,MAAM,IAAI,MAAM;QAC1C,OAAO,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,SAAS;IAC1C;IACA,IAAI,IAAI,MAAM,EAAE;QACd,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,MAAM,GAAG;YACxC,MAAM,IAAI,UAAU;QACtB;QACA,OAAO,CAAC,SAAS,EAAE,IAAI,MAAM,EAAE;IACjC;IACA,IAAI,IAAI,IAAI,EAAE;QACZ,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,IAAI,GAAG;YACtC,MAAM,IAAI,UAAU;QACtB;QACA,OAAO,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE;IAC7B;IACA,IAAI,IAAI,OAAO,EAAE;QACf,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,KAAK,YAAY;YACjD,MAAM,IAAI,UAAU;QACtB;QACA,OAAO,CAAC,UAAU,EAAE,IAAI,OAAO,CAAC,WAAW,IAAI;IACjD;IACA,IAAI,IAAI,QAAQ,EAAE,OAAO;IACzB,IAAI,IAAI,MAAM,EAAE,OAAO;IACvB,IAAI,IAAI,QAAQ,EAAE;QAChB,MAAM,WAAW,OAAO,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,QAAQ;QAC7F,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IACA,IAAI,IAAI,WAAW,EAAE,OAAO;IAC5B,OAAO;AACT;AACA,MAAM,SAAS;IACb,QAAO,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM;QACjC,IAAI,gBAAgB,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YACtF,MAAM;YACN,UAAU;QACZ;QACA,IAAI,SAAS;YACX,cAAc,OAAO,GAAG,IAAI;YAC5B,cAAc,OAAO,CAAC,OAAO,CAAC,cAAc,OAAO,CAAC,OAAO,KAAK,UAAU,KAAK;QACjF;QACA,IAAI,QAAQ,cAAc,MAAM,GAAG;QACnC,SAAS,MAAM,GAAG,gBAAgB,MAAM,OAAO;IACjD;IACA,MAAK,IAAI;QACP,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC;QACzB,MAAM,KAAK,SAAS,MAAM,CAAC,KAAK,CAAC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;YAClC,IAAI,IAAI,EAAE,CAAC,EAAE;YACb,MAAO,EAAE,MAAM,CAAC,OAAO,IAAK,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,MAAM;YACvD,IAAI,EAAE,OAAO,CAAC,YAAY,GAAG,OAAO,EAAE,SAAS,CAAC,OAAO,MAAM,EAAE,EAAE,MAAM;QACzE;QACA,OAAO;IACT;IACA,QAAO,IAAI,EAAE,MAAM;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;IAC5B;AACF;AACA,IAAI,WAAW;IACb,MAAM;IACN,uEAAuE;IACvE,QAAO,IAAI;QACT,IAAI,EACF,YAAY,EACb,GAAG;QACJ,IAAI,gBAAgB,OAAO,aAAa,aAAa;YACnD,OAAO,OAAO,IAAI,CAAC,iBAAiB;QACtC;QACA,OAAO;IACT;IACA,yHAAyH;IACzH,mBAAkB,GAAG,EAAE,KAAK;QAC1B,IAAI,EACF,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,EACd,GAAG;QACJ,IAAI,gBAAgB,OAAO,aAAa,aAAa;YACnD,OAAO,MAAM,CAAC,cAAc,KAAK,eAAe,cAAc;QAChE;IACF;AACF;AAEA,IAAI,cAAc;IAChB,MAAM;IACN,4EAA4E;IAC5E,QAAO,IAAI;QACT,IAAI,EACF,iBAAiB,EAClB,GAAG;QACJ,IAAI;QACJ,IAAI,OAAO,WAAW,aAAa;YACjC,IAAI,EACF,MAAM,EACP,GAAG,OAAO,QAAQ;YACnB,IAAI,CAAC,OAAO,QAAQ,CAAC,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,QAAQ,OAAO,CAAC,GAAG;gBACtE,SAAS,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YACvE;YACA,MAAM,QAAQ,OAAO,SAAS,CAAC;YAC/B,MAAM,SAAS,MAAM,KAAK,CAAC;YAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,MAAM,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC;gBAC9B,IAAI,MAAM,GAAG;oBACX,MAAM,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;oBACnC,IAAI,QAAQ,mBAAmB;wBAC7B,QAAQ,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;oBACpC;gBACF;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA,IAAI,OAAO;IACT,MAAM;IACN,0GAA0G;IAC1G,QAAO,IAAI;QACT,IAAI,EACF,UAAU,EACV,mBAAmB,EACpB,GAAG;QACJ,IAAI;QACJ,IAAI,OAAO,WAAW,aAAa;YACjC,MAAM,EACJ,IAAI,EACL,GAAG,OAAO,QAAQ;YACnB,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,MAAM,QAAQ,KAAK,SAAS,CAAC;gBAC7B,IAAI,YAAY;oBACd,MAAM,SAAS,MAAM,KAAK,CAAC;oBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC;wBAC9B,IAAI,MAAM,GAAG;4BACX,MAAM,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;4BACnC,IAAI,QAAQ,YAAY;gCACtB,QAAQ,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;4BACpC;wBACF;oBACF;gBACF;gBACA,IAAI,OAAO,OAAO;gBAClB,IAAI,CAAC,SAAS,sBAAsB,CAAC,GAAG;oBACtC,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,OAAO;oBACrC,MAAM,QAAQ,OAAO,wBAAwB,WAAW,sBAAsB;oBAC9E,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK;gBACvC;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA,IAAI,yBAAyB;AAC7B,MAAM,wBAAwB;IAC5B,IAAI,2BAA2B,MAAM,OAAO;IAC5C,IAAI;QACF,yBAAyB,OAAO,WAAW,eAAe,OAAO,YAAY,KAAK;QAClF,IAAI,CAAC,wBAAwB;YAC3B,OAAO;QACT;QACA,MAAM,UAAU;QAChB,OAAO,YAAY,CAAC,OAAO,CAAC,SAAS;QACrC,OAAO,YAAY,CAAC,UAAU,CAAC;IACjC,EAAE,OAAO,GAAG;QACV,yBAAyB;IAC3B;IACA,OAAO;AACT;AACA,IAAI,eAAe;IACjB,MAAM;IACN,6EAA6E;IAC7E,QAAO,IAAI;QACT,IAAI,EACF,kBAAkB,EACnB,GAAG;QACJ,IAAI,sBAAsB,yBAAyB;YACjD,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC,uBAAuB,WAAW,gFAAgF;QACvJ;QACA,OAAO;IACT;IACA,6EAA6E;IAC7E,mBAAkB,GAAG,EAAE,KAAK;QAC1B,IAAI,EACF,kBAAkB,EACnB,GAAG;QACJ,IAAI,sBAAsB,yBAAyB;YACjD,OAAO,YAAY,CAAC,OAAO,CAAC,oBAAoB;QAClD;IACF;AACF;AAEA,IAAI,2BAA2B;AAC/B,MAAM,0BAA0B;IAC9B,IAAI,6BAA6B,MAAM,OAAO;IAC9C,IAAI;QACF,2BAA2B,OAAO,WAAW,eAAe,OAAO,cAAc,KAAK;QACtF,IAAI,CAAC,0BAA0B;YAC7B,OAAO;QACT;QACA,MAAM,UAAU;QAChB,OAAO,cAAc,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,cAAc,CAAC,UAAU,CAAC;IACnC,EAAE,OAAO,GAAG;QACV,2BAA2B;IAC7B;IACA,OAAO;AACT;AACA,IAAI,iBAAiB;IACnB,MAAM;IACN,QAAO,IAAI;QACT,IAAI,EACF,oBAAoB,EACrB,GAAG;QACJ,IAAI,wBAAwB,2BAA2B;YACrD,OAAO,OAAO,cAAc,CAAC,OAAO,CAAC,yBAAyB;QAChE;QACA,OAAO;IACT;IACA,mBAAkB,GAAG,EAAE,KAAK;QAC1B,IAAI,EACF,oBAAoB,EACrB,GAAG;QACJ,IAAI,wBAAwB,2BAA2B;YACrD,OAAO,cAAc,CAAC,OAAO,CAAC,sBAAsB;QACtD;IACF;AACF;AAEA,IAAI,cAAc;IAChB,MAAM;IACN,QAAO,OAAO;QACZ,MAAM,QAAQ,EAAE;QAChB,IAAI,OAAO,cAAc,aAAa;YACpC,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,QAAQ,EACT,GAAG;YACJ,IAAI,WAAW;gBACb,2EAA2E;gBAC3E,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;oBACzC,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE;gBACzB;YACF;YACA,IAAI,cAAc;gBAChB,MAAM,IAAI,CAAC;YACb;YACA,IAAI,UAAU;gBACZ,MAAM,IAAI,CAAC;YACb;QACF;QACA,OAAO,MAAM,MAAM,GAAG,IAAI,QAAQ;IACpC;AACF;AAEA,IAAI,UAAU;IACZ,MAAM;IACN,kEAAkE;IAClE,QAAO,IAAI;QACT,IAAI,EACF,OAAO,EACR,GAAG;QACJ,IAAI;QACJ,MAAM,kBAAkB,WAAW,CAAC,OAAO,aAAa,cAAc,SAAS,eAAe,GAAG,IAAI;QACrG,IAAI,mBAAmB,OAAO,gBAAgB,YAAY,KAAK,YAAY;YACzE,QAAQ,gBAAgB,YAAY,CAAC;QACvC;QACA,OAAO;IACT;AACF;AAEA,IAAI,OAAO;IACT,MAAM;IACN,8EAA8E;IAC9E,QAAO,IAAI;QACT,IAAI,EACF,mBAAmB,EACpB,GAAG;QACJ,IAAI,OAAO,WAAW,aAAa,OAAO;QAC1C,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,OAAO;QACrC,MAAM,QAAQ,OAAO,wBAAwB,WAAW,sBAAsB;QAC9E,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK;IACvC;AACF;AAEA,IAAI,YAAY;IACd,MAAM;IACN,QAAO,IAAI;QACT,IAAI,EACF,wBAAwB,EACzB,GAAG;QACJ,0CAA0C;QAC1C,MAAM,mCAAmC,OAAO,6BAA6B,WAAW,2BAA2B,IAAI;QACvH,kDAAkD;QAClD,4HAA4H;QAC5H,kEAAkE;QAClE,MAAM,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ,EAAE,UAAU,MAAM;QAEnF,+CAA+C;QAC/C,IAAI,CAAC,UAAU,OAAO;QACtB,+BAA+B;QAC/B,OAAO,QAAQ,CAAC,iCAAiC;IACnD;AACF;AAEA,2DAA2D;AAC3D,IAAI,aAAa;AACjB,IAAI;IACF,iDAAiD;IACjD,SAAS,MAAM;IACf,aAAa;AACb,oCAAoC;AACtC,EAAE,OAAO,GAAG,CAAC;AACb,MAAM,QAAQ;IAAC;IAAe;IAAU;IAAgB;IAAkB;IAAa;CAAU;AACjG,IAAI,CAAC,YAAY,MAAM,MAAM,CAAC,GAAG;AACjC,MAAM,cAAc,IAAM,CAAC;QACzB;QACA,mBAAmB;QACnB,cAAc;QACd,oBAAoB;QACpB,sBAAsB;QACtB,sBAAsB;QACtB,QAAQ;YAAC;SAAe;QACxB,iBAAiB;YAAC;SAAS;QAC3B,qBAAqB;QACrB,2BAA2B;QAE3B,yBAAyB,CAAA,IAAK;IAChC,CAAC;AACD,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QACnF,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,UAAU;IACtB;IACA,OAAO;QACL,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YACjF,eAAe,CAAC;QAClB;QACA,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QACnF,IAAI,cAAc,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QACvF,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG;QACrD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,KAAK,YAAY,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG;YAC1H,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,CAAA,IAAK,EAAE,OAAO,CAAC,KAAK;QAC7D;QAEA,0BAA0B;QAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB;QACvG,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC;IACnB;IACA,YAAY,QAAQ,EAAE;QACpB,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,GAAG;QAChC,OAAO,IAAI;IACb;IACA,SAAS;QACP,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;QAC3G,IAAI,WAAW,EAAE;QACjB,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;gBAChC,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;gBAC7D,IAAI,UAAU,OAAO,WAAW,UAAU,SAAS;oBAAC;iBAAO;gBAC3D,IAAI,QAAQ,WAAW,SAAS,MAAM,CAAC;YACzC;QACF;QACA,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,IAAI,GAAG,CAAC,CAAA,IAAK,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;QAC3H,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,qBAAqB,EAAE,OAAO,UAAU,sBAAsB;QAC9I,OAAO,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,MAAM,kCAAkC;IACrF;IACA,kBAAkB,GAAG,EAAE;QACrB,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;QACpG,IAAI,CAAC,QAAQ;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG;QACpF,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC,OAAO;QAC9F;IACF;AACF;AACA,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/i18next-resources-to-backend/dist/esm/index.js"], "sourcesContent": ["var resourcesToBackend = function resourcesToBackend(res) {\n  return {\n    type: 'backend',\n    init: function init(services, backendOptions, i18nextOptions) {},\n    read: function read(language, namespace, callback) {\n      if (typeof res === 'function') {\n        if (res.length < 3) {\n          try {\n            var r = res(language, namespace);\n            if (r && typeof r.then === 'function') {\n              r.then(function (data) {\n                return callback(null, data && data.default || data);\n              }).catch(callback);\n            } else {\n              callback(null, r);\n            }\n          } catch (err) {\n            callback(err);\n          }\n          return;\n        }\n        res(language, namespace, callback);\n        return;\n      }\n      callback(null, res && res[language] && res[language][namespace]);\n    }\n  };\n};\n\nexport { resourcesToBackend as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,qBAAqB,SAAS,mBAAmB,GAAG;IACtD,OAAO;QACL,MAAM;QACN,MAAM,SAAS,KAAK,QAAQ,EAAE,cAAc,EAAE,cAAc,GAAG;QAC/D,MAAM,SAAS,KAAK,QAAQ,EAAE,SAAS,EAAE,QAAQ;YAC/C,IAAI,OAAO,QAAQ,YAAY;gBAC7B,IAAI,IAAI,MAAM,GAAG,GAAG;oBAClB,IAAI;wBACF,IAAI,IAAI,IAAI,UAAU;wBACtB,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,YAAY;4BACrC,EAAE,IAAI,CAAC,SAAU,IAAI;gCACnB,OAAO,SAAS,MAAM,QAAQ,KAAK,OAAO,IAAI;4BAChD,GAAG,KAAK,CAAC;wBACX,OAAO;4BACL,SAAS,MAAM;wBACjB;oBACF,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX;oBACA;gBACF;gBACA,IAAI,UAAU,WAAW;gBACzB;YACF;YACA,SAAS,MAAM,OAAO,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU;QACjE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3962, "column": 0}, "map": {"version": 3, "file": "bundle-mjs.mjs", "sources": ["file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/class-group-utils.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/lru-cache.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/parse-class-name.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/sort-modifiers.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/config-utils.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/merge-classlist.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/tw-join.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/create-tailwind-merge.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/from-theme.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/validators.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/default-config.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/merge-configs.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/extend-tailwind-merge.ts", "file:///C:/Users/<USER>/Desktop/TheRateRace.io/node_modules/tailwind-merge/src/lib/tw-merge.ts"], "sourcesContent": ["import {\n    AnyClassGroupIds,\n    AnyConfig,\n    AnyThemeGroupIds,\n    ClassGroup,\n    ClassValidator,\n    Config,\n    ThemeGetter,\n    ThemeObject,\n} from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: AnyClassGroupIds\n}\n\ninterface ClassValidatorObject {\n    classGroupId: AnyClassGroupIds\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport const createClassGroupUtils = (config: AnyConfig) => {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config\n\n    const getClassGroupId = (className: string) => {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    const getConflictingClassGroupIds = (\n        classGroupId: AnyClassGroupIds,\n        hasPostfixModifier: boolean,\n    ) => {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nconst getGroupRecursive = (\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): AnyClassGroupIds | undefined => {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nconst getGroupIdForArbitraryProperty = (className: string) => {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport const createClassMap = (config: Config<AnyClassGroupIds, AnyThemeGroupIds>) => {\n    const { theme, classGroups } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    for (const classGroupId in classGroups) {\n        processClassesRecursively(classGroups[classGroupId]!, classMap, classGroupId, theme)\n    }\n\n    return classMap\n}\n\nconst processClassesRecursively = (\n    classGroup: ClassGroup<AnyThemeGroupIds>,\n    classPartObject: ClassPartObject,\n    classGroupId: AnyClassGroupIds,\n    theme: ThemeObject<AnyThemeGroupIds>,\n) => {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nconst getPart = (classPartObject: ClassPartObject, path: string) => {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nconst isThemeGetter = (func: ClassValidator | ThemeGetter): func is ThemeGetter =>\n    (func as ThemeGetter).isThemeGetter\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport const createLruCache = <Key, Value>(maxCacheSize: number): LruCache<Key, Value> => {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    const update = (key: Key, value: Value) => {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { AnyConfig, ParsedClassName } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\nconst MODIFIER_SEPARATOR = ':'\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length\n\nexport const createParseClassName = (config: AnyConfig) => {\n    const { prefix, experimentalParseClassName } = config\n\n    /**\n     * Parse class name into parts.\n     *\n     * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n     * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n     */\n    let parseClassName = (className: string): ParsedClassName => {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let parenDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            } else if (currentCharacter === '(') {\n                parenDepth++\n            } else if (currentCharacter === ')') {\n                parenDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier)\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            className.startsWith(fullPrefix)\n                ? parseClassNameOriginal(className.substring(fullPrefix.length))\n                : {\n                      isExternal: true,\n                      modifiers: [],\n                      hasImportantModifier: false,\n                      baseClassName: className,\n                      maybePostfixModifierPosition: undefined,\n                  }\n    }\n\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            experimentalParseClassName({ className, parseClassName: parseClassNameOriginal })\n    }\n\n    return parseClassName\n}\n\nconst stripImportantModifier = (baseClassName: string) => {\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1)\n    }\n\n    /**\n     * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n     * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n     */\n    if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1)\n    }\n\n    return baseClassName\n}\n", "import { AnyConfig } from './types'\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport const createSortModifiers = (config: AnyConfig) => {\n    const orderSensitiveModifiers = Object.fromEntries(\n        config.orderSensitiveModifiers.map((modifier) => [modifier, true]),\n    )\n\n    const sortModifiers = (modifiers: string[]) => {\n        if (modifiers.length <= 1) {\n            return modifiers\n        }\n\n        const sortedModifiers: string[] = []\n        let unsortedModifiers: string[] = []\n\n        modifiers.forEach((modifier) => {\n            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier]\n\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n                unsortedModifiers = []\n            } else {\n                unsortedModifiers.push(modifier)\n            }\n        })\n\n        sortedModifiers.push(...unsortedModifiers.sort())\n\n        return sortedModifiers\n    }\n\n    return sortModifiers\n}\n", "import { createClassGroupUtils } from './class-group-utils'\nimport { createLruCache } from './lru-cache'\nimport { createParseClassName } from './parse-class-name'\nimport { createSortModifiers } from './sort-modifiers'\nimport { AnyConfig } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport const createConfigUtils = (config: AnyConfig) => ({\n    cache: createLruCache<string, string>(config.cacheSize),\n    parseClassName: createParseClassName(config),\n    sortModifiers: createSortModifiers(config),\n    ...createClassGroupUtils(config),\n})\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER } from './parse-class-name'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport const mergeClassList = (classList: string, configUtils: ConfigUtils) => {\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } =\n        configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict: string[] = []\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX)\n\n    let result = ''\n\n    for (let index = classNames.length - 1; index >= 0; index -= 1) {\n        const originalClassName = classNames[index]!\n\n        const {\n            isExternal,\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        } = parseClassName(originalClassName)\n\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? ' ' + result : result)\n            continue\n        }\n\n        let hasPostfixModifier = !!maybePostfixModifierPosition\n        let classGroupId = getClassGroupId(\n            hasPostfixModifier\n                ? baseClassName.substring(0, maybePostfixModifierPosition)\n                : baseClassName,\n        )\n\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            classGroupId = getClassGroupId(baseClassName)\n\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            hasPostfixModifier = false\n        }\n\n        const variantModifier = sortModifiers(modifiers).join(':')\n\n        const modifierId = hasImportantModifier\n            ? variantModifier + IMPORTANT_MODIFIER\n            : variantModifier\n\n        const classId = modifierId + classGroupId\n\n        if (classGroupsInConflict.includes(classId)) {\n            // Tailwind class omitted due to conflict\n            continue\n        }\n\n        classGroupsInConflict.push(classId)\n\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier)\n        for (let i = 0; i < conflictGroups.length; ++i) {\n            const group = conflictGroups[i]!\n            classGroupsInConflict.push(modifierId + group)\n        }\n\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result)\n    }\n\n    return result\n}\n", "/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | 0n | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nconst toValue = (mix: ClassNameArray | string) => {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { AnyConfig } from './types'\n\ntype CreateConfigFirst = () => AnyConfig\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    createConfigFirst: CreateConfigFirst,\n    ...createConfigRest: CreateConfigSubsequent[]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const config = createConfigRest.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            createConfigFirst() as AnyConfig,\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { DefaultThemeGroupIds, <PERSON><PERSON><PERSON><PERSON>, ThemeGetter, ThemeObject } from './types'\n\nexport const fromTheme = <\n    AdditionalThemeGroupIds extends string = never,\n    DefaultThemeGroupIdsInner extends string = DefaultThemeGroupIds,\n>(key: NoInfer<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>): ThemeGetter => {\n    const themeGetter = (theme: ThemeObject<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>) =>\n        theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\nconst imageRegex =\n    /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/\n\nexport const isFraction = (value: string) => fractionRegex.test(value)\n\nexport const isNumber = (value: string) => !!value && !Number.isNaN(Number(value))\n\nexport const isInteger = (value: string) => !!value && Number.isInteger(Number(value))\n\nexport const isPercent = (value: string) => value.endsWith('%') && isNumber(value.slice(0, -1))\n\nexport const isTshirtSize = (value: string) => tshirtUnitRegex.test(value)\n\nexport const isAny = () => true\n\nconst isLengthOnly = (value: string) =>\n    // `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value)\n\nconst isNever = () => false\n\nconst isShadow = (value: string) => shadowRegex.test(value)\n\nconst isImage = (value: string) => imageRegex.test(value)\n\nexport const isAnyNonArbitrary = (value: string) =>\n    !isArbitraryValue(value) && !isArbitraryVariable(value)\n\nexport const isArbitrarySize = (value: string) => getIsArbitraryValue(value, isLabelSize, isNever)\n\nexport const isArbitraryValue = (value: string) => arbitraryValueRegex.test(value)\n\nexport const isArbitraryLength = (value: string) =>\n    getIsArbitraryValue(value, isLabelLength, isLengthOnly)\n\nexport const isArbitraryNumber = (value: string) =>\n    getIsArbitraryValue(value, isLabelNumber, isNumber)\n\nexport const isArbitraryPosition = (value: string) =>\n    getIsArbitraryValue(value, isLabelPosition, isNever)\n\nexport const isArbitraryImage = (value: string) => getIsArbitraryValue(value, isLabelImage, isImage)\n\nexport const isArbitraryShadow = (value: string) =>\n    getIsArbitraryValue(value, isLabelShadow, isShadow)\n\nexport const isArbitraryVariable = (value: string) => arbitraryVariableRegex.test(value)\n\nexport const isArbitraryVariableLength = (value: string) =>\n    getIsArbitraryVariable(value, isLabelLength)\n\nexport const isArbitraryVariableFamilyName = (value: string) =>\n    getIsArbitraryVariable(value, isLabelFamilyName)\n\nexport const isArbitraryVariablePosition = (value: string) =>\n    getIsArbitraryVariable(value, isLabelPosition)\n\nexport const isArbitraryVariableSize = (value: string) => getIsArbitraryVariable(value, isLabelSize)\n\nexport const isArbitraryVariableImage = (value: string) =>\n    getIsArbitraryVariable(value, isLabelImage)\n\nexport const isArbitraryVariableShadow = (value: string) =>\n    getIsArbitraryVariable(value, isLabelShadow, true)\n\n// Helpers\n\nconst getIsArbitraryValue = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    testValue: (value: string) => boolean,\n) => {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nconst getIsArbitraryVariable = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    shouldMatchNoLabel = false,\n) => {\n    const result = arbitraryVariableRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n        return shouldMatchNoLabel\n    }\n\n    return false\n}\n\n// Labels\n\nconst isLabelPosition = (label: string) => label === 'position' || label === 'percentage'\n\nconst isLabelImage = (label: string) => label === 'image' || label === 'url'\n\nconst isLabelSize = (label: string) => label === 'length' || label === 'size' || label === 'bg-size'\n\nconst isLabelLength = (label: string) => label === 'length'\n\nconst isLabelNumber = (label: string) => label === 'number'\n\nconst isLabelFamilyName = (label: string) => label === 'family-name'\n\nconst isLabelShadow = (label: string) => label === 'shadow'\n", "import { fromTheme } from './from-theme'\nimport { Config, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\nimport {\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport const getDefaultConfig = () => {\n    /**\n     * Theme getters for theme variable namespaces\n     * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n     */\n    /***/\n\n    const themeColor = fromTheme('color')\n    const themeFont = fromTheme('font')\n    const themeText = fromTheme('text')\n    const themeFontWeight = fromTheme('font-weight')\n    const themeTracking = fromTheme('tracking')\n    const themeLeading = fromTheme('leading')\n    const themeBreakpoint = fromTheme('breakpoint')\n    const themeContainer = fromTheme('container')\n    const themeSpacing = fromTheme('spacing')\n    const themeRadius = fromTheme('radius')\n    const themeShadow = fromTheme('shadow')\n    const themeInsetShadow = fromTheme('inset-shadow')\n    const themeTextShadow = fromTheme('text-shadow')\n    const themeDropShadow = fromTheme('drop-shadow')\n    const themeBlur = fromTheme('blur')\n    const themePerspective = fromTheme('perspective')\n    const themeAspect = fromTheme('aspect')\n    const themeEase = fromTheme('ease')\n    const themeAnimate = fromTheme('animate')\n\n    /**\n     * Helpers to avoid repeating the same scales\n     *\n     * We use functions that create a new array every time they're called instead of static arrays.\n     * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n     */\n    /***/\n\n    const scaleBreak = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const scalePosition = () =>\n        [\n            'center',\n            'top',\n            'bottom',\n            'left',\n            'right',\n            'top-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-top',\n            'top-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-top',\n            'bottom-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-bottom',\n            'bottom-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-bottom',\n        ] as const\n    const scalePositionWithArbitrary = () =>\n        [...scalePosition(), isArbitraryVariable, isArbitraryValue] as const\n    const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const scaleOverscroll = () => ['auto', 'contain', 'none'] as const\n    const scaleUnambiguousSpacing = () =>\n        [isArbitraryVariable, isArbitraryValue, themeSpacing] as const\n    const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()] as const\n    const scaleGridTemplateColsRows = () =>\n        [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridColRowStartAndEnd = () =>\n        [\n            'auto',\n            { span: ['full', isInteger, isArbitraryVariable, isArbitraryValue] },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleGridColRowStartOrEnd = () =>\n        [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridAutoColsRows = () =>\n        ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue] as const\n    const scaleAlignPrimaryAxis = () =>\n        [\n            'start',\n            'end',\n            'center',\n            'between',\n            'around',\n            'evenly',\n            'stretch',\n            'baseline',\n            'center-safe',\n            'end-safe',\n        ] as const\n    const scaleAlignSecondaryAxis = () =>\n        ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'] as const\n    const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()] as const\n    const scaleSizing = () =>\n        [\n            isFraction,\n            'auto',\n            'full',\n            'dvw',\n            'dvh',\n            'lvw',\n            'lvh',\n            'svw',\n            'svh',\n            'min',\n            'max',\n            'fit',\n            ...scaleUnambiguousSpacing(),\n        ] as const\n    const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue] as const\n    const scaleBgPosition = () =>\n        [\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            { position: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleBgRepeat = () => ['no-repeat', { repeat: ['', 'x', 'y', 'space', 'round'] }] as const\n    const scaleBgSize = () =>\n        [\n            'auto',\n            'cover',\n            'contain',\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            { size: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleGradientStopPosition = () =>\n        [isPercent, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleRadius = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            'full',\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleBorderWidth = () =>\n        ['', isNumber, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'] as const\n    const scaleBlendMode = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n        ] as const\n    const scaleMaskImagePosition = () =>\n        [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition] as const\n    const scaleBlur = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()] as const\n\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: ['spin', 'ping', 'pulse', 'bounce'],\n            aspect: ['video'],\n            blur: [isTshirtSize],\n            breakpoint: [isTshirtSize],\n            color: [isAny],\n            container: [isTshirtSize],\n            'drop-shadow': [isTshirtSize],\n            ease: ['in', 'out', 'in-out'],\n            font: [isAnyNonArbitrary],\n            'font-weight': [\n                'thin',\n                'extralight',\n                'light',\n                'normal',\n                'medium',\n                'semibold',\n                'bold',\n                'extrabold',\n                'black',\n            ],\n            'inset-shadow': [isTshirtSize],\n            leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n            perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n            radius: [isTshirtSize],\n            shadow: [isTshirtSize],\n            spacing: ['px', isNumber],\n            text: [isTshirtSize],\n            'text-shadow': [isTshirtSize],\n            tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest'],\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [\n                {\n                    aspect: [\n                        'auto',\n                        'square',\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect,\n                    ],\n                },\n            ],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             * @deprecated since Tailwind CSS v4.0.0\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [\n                { columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer] },\n            ],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': scaleBreak() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': scaleBreak() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Screen Reader Only\n             * @see https://tailwindcss.com/docs/display#screen-reader-only\n             */\n            sr: ['sr-only', 'not-sr-only'],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none', 'start', 'end'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none', 'start', 'end'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: scalePositionWithArbitrary() }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: scaleOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': scaleOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': scaleOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: scaleOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': scaleOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': scaleOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: scaleInset() }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': scaleInset() }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': scaleInset() }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: scaleInset() }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: scaleInset() }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: scaleInset() }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: scaleInset() }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: scaleInset() }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: scaleInset() }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [\n                {\n                    basis: [\n                        isFraction,\n                        'full',\n                        'auto',\n                        themeContainer,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['nowrap', 'wrap', 'wrap-reverse'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [\n                {\n                    order: [\n                        isInteger,\n                        'first',\n                        'last',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': scaleGridAutoColsRows() }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': scaleGridAutoColsRows() }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: scaleUnambiguousSpacing() }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': scaleUnambiguousSpacing() }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': scaleUnambiguousSpacing() }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: [...scaleAlignPrimaryAxis(), 'normal'] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': [...scaleAlignSecondaryAxis(), 'normal'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...scaleAlignPrimaryAxis()] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: [...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [\n                { self: ['auto', ...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] },\n            ],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': scaleAlignPrimaryAxis() }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': [...scaleAlignSecondaryAxis(), 'baseline'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: scaleUnambiguousSpacing() }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: scaleUnambiguousSpacing() }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: scaleUnambiguousSpacing() }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: scaleMargin() }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: scaleMargin() }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: scaleMargin() }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: scaleMargin() }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: scaleMargin() }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: scaleMargin() }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: scaleMargin() }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: scaleMargin() }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: scaleMargin() }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x': [{ 'space-x': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y': [{ 'space-y': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y-reverse': ['space-y-reverse'],\n\n            // --------------\n            // --- Sizing ---\n            // --------------\n\n            /**\n             * Size\n             * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n             */\n            size: [{ size: scaleSizing() }],\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: [themeContainer, 'screen', ...scaleSizing()] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [\n                {\n                    'min-w': [\n                        themeContainer,\n                        'screen',\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'none',\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        themeContainer,\n                        'screen',\n                        'none',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'prose',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        { screen: [themeBreakpoint] },\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: ['screen', 'lh', ...scaleSizing()] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['screen', 'lh', 'none', ...scaleSizing()] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': ['screen', 'lh', ...scaleSizing()] }],\n\n            // ------------------\n            // --- Typography ---\n            // ------------------\n\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [\n                { text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [{ font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber] }],\n            /**\n             * Font Stretch\n             * @see https://tailwindcss.com/docs/font-stretch\n             */\n            'font-stretch': [\n                {\n                    'font-stretch': [\n                        'ultra-condensed',\n                        'extra-condensed',\n                        'condensed',\n                        'semi-condensed',\n                        'normal',\n                        'semi-expanded',\n                        'expanded',\n                        'extra-expanded',\n                        'ultra-expanded',\n                        isPercent,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [{ tracking: [themeTracking, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [\n                { 'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber] },\n            ],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        themeLeading,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [\n                { list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://v3.tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: scaleColor() }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: scaleColor() }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...scaleLineStyle(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [\n                {\n                    decoration: [\n                        isNumber,\n                        'from-font',\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryLength,\n                    ],\n                },\n            ],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: scaleColor() }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [\n                { 'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Wrap\n             * @see https://tailwindcss.com/docs/text-wrap\n             */\n            'text-wrap': [{ text: ['wrap', 'nowrap', 'balance', 'pretty'] }],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: scaleUnambiguousSpacing() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Overflow Wrap\n             * @see https://tailwindcss.com/docs/overflow-wrap\n             */\n            wrap: [{ wrap: ['break-word', 'anywhere', 'normal'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: scaleBgPosition() }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: scaleBgRepeat() }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: scaleBgSize() }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        {\n                            linear: [\n                                { to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue,\n                            ],\n                            radial: ['', isArbitraryVariable, isArbitraryValue],\n                            conic: [isInteger, isArbitraryVariable, isArbitraryValue],\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: scaleColor() }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: scaleColor() }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: scaleColor() }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: scaleColor() }],\n\n            // ---------------\n            // --- Borders ---\n            // ---------------\n\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: scaleRadius() }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': scaleRadius() }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': scaleRadius() }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': scaleRadius() }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': scaleRadius() }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': scaleRadius() }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': scaleRadius() }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': scaleRadius() }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': scaleRadius() }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': scaleRadius() }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': scaleRadius() }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': scaleRadius() }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': scaleRadius() }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': scaleRadius() }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': scaleRadius() }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: scaleBorderWidth() }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': scaleBorderWidth() }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': scaleBorderWidth() }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': scaleBorderWidth() }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': scaleBorderWidth() }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': scaleBorderWidth() }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': scaleBorderWidth() }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': scaleBorderWidth() }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': scaleBorderWidth() }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x': [{ 'divide-x': scaleBorderWidth() }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y': [{ 'divide-y': scaleBorderWidth() }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n             */\n            'divide-style': [{ divide: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: scaleColor() }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': scaleColor() }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': scaleColor() }],\n            /**\n             * Border Color S\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-s': [{ 'border-s': scaleColor() }],\n            /**\n             * Border Color E\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-e': [{ 'border-e': scaleColor() }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': scaleColor() }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': scaleColor() }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': scaleColor() }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': scaleColor() }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: scaleColor() }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: [...scaleLineStyle(), 'none', 'hidden'] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [\n                { 'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [\n                { outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: scaleColor() }],\n\n            // ---------------\n            // --- Effects ---\n            // ---------------\n\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n             */\n            'shadow-color': [{ shadow: scaleColor() }],\n            /**\n             * Inset Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n             */\n            'inset-shadow': [\n                {\n                    'inset-shadow': [\n                        'none',\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Inset Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n             */\n            'inset-shadow-color': [{ 'inset-shadow': scaleColor() }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n             */\n            'ring-w': [{ ring: scaleBorderWidth() }],\n            /**\n             * Ring Width Inset\n             * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n             */\n            'ring-color': [{ ring: scaleColor() }],\n            /**\n             * Ring Offset Width\n             * @see https://v3.tailwindcss.com/docs/ring-offset-width\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-w': [{ 'ring-offset': [isNumber, isArbitraryLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://v3.tailwindcss.com/docs/ring-offset-color\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-color': [{ 'ring-offset': scaleColor() }],\n            /**\n             * Inset Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n             */\n            'inset-ring-w': [{ 'inset-ring': scaleBorderWidth() }],\n            /**\n             * Inset Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n             */\n            'inset-ring-color': [{ 'inset-ring': scaleColor() }],\n            /**\n             * Text Shadow\n             * @see https://tailwindcss.com/docs/text-shadow\n             */\n            'text-shadow': [\n                {\n                    'text-shadow': [\n                        'none',\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Text Shadow Color\n             * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n             */\n            'text-shadow-color': [{ 'text-shadow': scaleColor() }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter'] }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': scaleBlendMode() }],\n            /**\n             * Mask Clip\n             * @see https://tailwindcss.com/docs/mask-clip\n             */\n            'mask-clip': [\n                { 'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n                'mask-no-clip',\n            ],\n            /**\n             * Mask Composite\n             * @see https://tailwindcss.com/docs/mask-composite\n             */\n            'mask-composite': [{ mask: ['add', 'subtract', 'intersect', 'exclude'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image-linear-pos': [{ 'mask-linear': [isNumber] }],\n            'mask-image-linear-from-pos': [{ 'mask-linear-from': scaleMaskImagePosition() }],\n            'mask-image-linear-to-pos': [{ 'mask-linear-to': scaleMaskImagePosition() }],\n            'mask-image-linear-from-color': [{ 'mask-linear-from': scaleColor() }],\n            'mask-image-linear-to-color': [{ 'mask-linear-to': scaleColor() }],\n            'mask-image-t-from-pos': [{ 'mask-t-from': scaleMaskImagePosition() }],\n            'mask-image-t-to-pos': [{ 'mask-t-to': scaleMaskImagePosition() }],\n            'mask-image-t-from-color': [{ 'mask-t-from': scaleColor() }],\n            'mask-image-t-to-color': [{ 'mask-t-to': scaleColor() }],\n            'mask-image-r-from-pos': [{ 'mask-r-from': scaleMaskImagePosition() }],\n            'mask-image-r-to-pos': [{ 'mask-r-to': scaleMaskImagePosition() }],\n            'mask-image-r-from-color': [{ 'mask-r-from': scaleColor() }],\n            'mask-image-r-to-color': [{ 'mask-r-to': scaleColor() }],\n            'mask-image-b-from-pos': [{ 'mask-b-from': scaleMaskImagePosition() }],\n            'mask-image-b-to-pos': [{ 'mask-b-to': scaleMaskImagePosition() }],\n            'mask-image-b-from-color': [{ 'mask-b-from': scaleColor() }],\n            'mask-image-b-to-color': [{ 'mask-b-to': scaleColor() }],\n            'mask-image-l-from-pos': [{ 'mask-l-from': scaleMaskImagePosition() }],\n            'mask-image-l-to-pos': [{ 'mask-l-to': scaleMaskImagePosition() }],\n            'mask-image-l-from-color': [{ 'mask-l-from': scaleColor() }],\n            'mask-image-l-to-color': [{ 'mask-l-to': scaleColor() }],\n            'mask-image-x-from-pos': [{ 'mask-x-from': scaleMaskImagePosition() }],\n            'mask-image-x-to-pos': [{ 'mask-x-to': scaleMaskImagePosition() }],\n            'mask-image-x-from-color': [{ 'mask-x-from': scaleColor() }],\n            'mask-image-x-to-color': [{ 'mask-x-to': scaleColor() }],\n            'mask-image-y-from-pos': [{ 'mask-y-from': scaleMaskImagePosition() }],\n            'mask-image-y-to-pos': [{ 'mask-y-to': scaleMaskImagePosition() }],\n            'mask-image-y-from-color': [{ 'mask-y-from': scaleColor() }],\n            'mask-image-y-to-color': [{ 'mask-y-to': scaleColor() }],\n            'mask-image-radial': [{ 'mask-radial': [isArbitraryVariable, isArbitraryValue] }],\n            'mask-image-radial-from-pos': [{ 'mask-radial-from': scaleMaskImagePosition() }],\n            'mask-image-radial-to-pos': [{ 'mask-radial-to': scaleMaskImagePosition() }],\n            'mask-image-radial-from-color': [{ 'mask-radial-from': scaleColor() }],\n            'mask-image-radial-to-color': [{ 'mask-radial-to': scaleColor() }],\n            'mask-image-radial-shape': [{ 'mask-radial': ['circle', 'ellipse'] }],\n            'mask-image-radial-size': [\n                { 'mask-radial': [{ closest: ['side', 'corner'], farthest: ['side', 'corner'] }] },\n            ],\n            'mask-image-radial-pos': [{ 'mask-radial-at': scalePosition() }],\n            'mask-image-conic-pos': [{ 'mask-conic': [isNumber] }],\n            'mask-image-conic-from-pos': [{ 'mask-conic-from': scaleMaskImagePosition() }],\n            'mask-image-conic-to-pos': [{ 'mask-conic-to': scaleMaskImagePosition() }],\n            'mask-image-conic-from-color': [{ 'mask-conic-from': scaleColor() }],\n            'mask-image-conic-to-color': [{ 'mask-conic-to': scaleColor() }],\n            /**\n             * Mask Mode\n             * @see https://tailwindcss.com/docs/mask-mode\n             */\n            'mask-mode': [{ mask: ['alpha', 'luminance', 'match'] }],\n            /**\n             * Mask Origin\n             * @see https://tailwindcss.com/docs/mask-origin\n             */\n            'mask-origin': [\n                { 'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n            ],\n            /**\n             * Mask Position\n             * @see https://tailwindcss.com/docs/mask-position\n             */\n            'mask-position': [{ mask: scaleBgPosition() }],\n            /**\n             * Mask Repeat\n             * @see https://tailwindcss.com/docs/mask-repeat\n             */\n            'mask-repeat': [{ mask: scaleBgRepeat() }],\n            /**\n             * Mask Size\n             * @see https://tailwindcss.com/docs/mask-size\n             */\n            'mask-size': [{ mask: scaleBgSize() }],\n            /**\n             * Mask Type\n             * @see https://tailwindcss.com/docs/mask-type\n             */\n            'mask-type': [{ 'mask-type': ['alpha', 'luminance'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image': [{ mask: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // ---------------\n            // --- Filters ---\n            // ---------------\n\n            /**\n             * Filter\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: scaleBlur() }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [\n                {\n                    'drop-shadow': [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Drop Shadow Color\n             * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n             */\n            'drop-shadow-color': [{ 'drop-shadow': scaleColor() }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Backdrop Filter\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [\n                {\n                    'backdrop-filter': [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': scaleBlur() }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [\n                { 'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [\n                { 'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [\n                { 'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [\n                { 'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [\n                { 'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [\n                { 'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [\n                { 'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [\n                { 'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n\n            // --------------\n            // --- Tables ---\n            // --------------\n\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': scaleUnambiguousSpacing() }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n\n            /**\n             * Transition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        '',\n                        'all',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Behavior\n             * @see https://tailwindcss.com/docs/transition-behavior\n             */\n            'transition-behavior': [{ transition: ['normal', 'discrete'] }],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [\n                { ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n\n            /**\n             * Backface Visibility\n             * @see https://tailwindcss.com/docs/backface-visibility\n             */\n            backface: [{ backface: ['hidden', 'visible'] }],\n            /**\n             * Perspective\n             * @see https://tailwindcss.com/docs/perspective\n             */\n            perspective: [\n                { perspective: [themePerspective, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Perspective Origin\n             * @see https://tailwindcss.com/docs/perspective-origin\n             */\n            'perspective-origin': [{ 'perspective-origin': scalePositionWithArbitrary() }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: scaleRotate() }],\n            /**\n             * Rotate X\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-x': [{ 'rotate-x': scaleRotate() }],\n            /**\n             * Rotate Y\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-y': [{ 'rotate-y': scaleRotate() }],\n            /**\n             * Rotate Z\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-z': [{ 'rotate-z': scaleRotate() }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: scaleScale() }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': scaleScale() }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': scaleScale() }],\n            /**\n             * Scale Z\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-z': [{ 'scale-z': scaleScale() }],\n            /**\n             * Scale 3D\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-3d': ['scale-3d'],\n            /**\n             * Skew\n             * @see https://tailwindcss.com/docs/skew\n             */\n            skew: [{ skew: scaleSkew() }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': scaleSkew() }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': scaleSkew() }],\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [\n                { transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu'] },\n            ],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [{ origin: scalePositionWithArbitrary() }],\n            /**\n             * Transform Style\n             * @see https://tailwindcss.com/docs/transform-style\n             */\n            'transform-style': [{ transform: ['3d', 'flat'] }],\n            /**\n             * Translate\n             * @see https://tailwindcss.com/docs/translate\n             */\n            translate: [{ translate: scaleTranslate() }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': scaleTranslate() }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': scaleTranslate() }],\n            /**\n             * Translate Z\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-z': [{ 'translate-z': scaleTranslate() }],\n            /**\n             * Translate None\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-none': ['translate-none'],\n\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: scaleColor() }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: [{ appearance: ['none', 'auto'] }],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: scaleColor() }],\n            /**\n             * Color Scheme\n             * @see https://tailwindcss.com/docs/color-scheme\n             */\n            'color-scheme': [\n                { scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light'] },\n            ],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Field Sizing\n             * @see https://tailwindcss.com/docs/field-sizing\n             */\n            'field-sizing': [{ 'field-sizing': ['fixed', 'content'] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['auto', 'none'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', '', 'y', 'x'] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [{ touch: ['auto', 'none', 'manipulation'] }],\n            /**\n             * Touch Action X\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-x': [{ 'touch-pan': ['x', 'left', 'right'] }],\n            /**\n             * Touch Action Y\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-y': [{ 'touch-pan': ['y', 'up', 'down'] }],\n            /**\n             * Touch Action Pinch Zoom\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-pz': ['touch-pinch-zoom'],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                {\n                    'will-change': [\n                        'auto',\n                        'scroll',\n                        'contents',\n                        'transform',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n\n            // -----------\n            // --- SVG ---\n            // -----------\n\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: ['none', ...scaleColor()] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: ['none', ...scaleColor()] }],\n\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n\n            /**\n             * Forced Color Adjust\n             * @see https://tailwindcss.com/docs/forced-color-adjust\n             */\n            'forced-color-adjust': [{ 'forced-color-adjust': ['auto', 'none'] }],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            size: ['w', 'h'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            'line-clamp': ['display', 'overflow'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-x',\n                'border-w-y',\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-x',\n                'border-color-y',\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            translate: ['translate-x', 'translate-y', 'translate-none'],\n            'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n            touch: ['touch-x', 'touch-y', 'touch-pz'],\n            'touch-x': ['touch'],\n            'touch-y': ['touch'],\n            'touch-pz': ['touch'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n        orderSensitiveModifiers: [\n            '*',\n            '**',\n            'after',\n            'backdrop',\n            'before',\n            'details-content',\n            'file',\n            'first-letter',\n            'first-line',\n            'marker',\n            'placeholder',\n            'selection',\n        ],\n    } as const satisfies Config<DefaultClassGroupIds, DefaultThemeGroupIds>\n}\n", "import { AnyConfig, ConfigExtension, NoInfer } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport const mergeConfigs = <ClassGroupIds extends string, ThemeGroupIds extends string = never>(\n    baseConfig: AnyConfig,\n    {\n        cacheSize,\n        prefix,\n        experimentalParseClassName,\n        extend = {},\n        override = {},\n    }: ConfigExtension<ClassGroupIds, ThemeGroupIds>,\n) => {\n    overrideProperty(baseConfig, 'cacheSize', cacheSize)\n    overrideProperty(baseConfig, 'prefix', prefix)\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName)\n\n    overrideConfigProperties(baseConfig.theme, override.theme)\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups)\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups)\n    overrideConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        override.conflictingClassGroupModifiers,\n    )\n    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers)\n\n    mergeConfigProperties(baseConfig.theme, extend.theme)\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups)\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups)\n    mergeConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        extend.conflictingClassGroupModifiers,\n    )\n    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers')\n\n    return baseConfig\n}\n\nconst overrideProperty = <T extends object, K extends keyof T>(\n    baseObject: T,\n    overrideKey: K,\n    overrideValue: T[K] | undefined,\n) => {\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue\n    }\n}\n\nconst overrideConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    overrideObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (overrideObject) {\n        for (const key in overrideObject) {\n            overrideProperty(baseObject, key, overrideObject[key])\n        }\n    }\n}\n\nconst mergeConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    mergeObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (mergeObject) {\n        for (const key in mergeObject) {\n            mergeArrayProperties(baseObject, mergeObject, key)\n        }\n    }\n}\n\nconst mergeArrayProperties = <Key extends string>(\n    baseObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    mergeObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    key: Key,\n) => {\n    const mergeValue = mergeObject[key]\n\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { AnyConfig, ConfigExtension, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\n\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\n\nexport const extendTailwindMerge = <\n    AdditionalClassGroupIds extends string = never,\n    AdditionalThemeGroupIds extends string = never,\n>(\n    configExtension:\n        | ConfigExtension<\n              DefaultClassGroupIds | AdditionalClassGroupIds,\n              DefaultThemeGroupIds | AdditionalThemeGroupIds\n          >\n        | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) =>\n    typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "names": ["CLASS_PART_SEPARATOR", "createClassGroupUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "classGroups", "Map", "processClassesRecursively", "classGroup", "for<PERSON>ach", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "Object", "entries", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "value", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "startsWith", "isExternal", "endsWith", "createSortModifiers", "orderSensitiveModifiers", "fromEntries", "map", "modifier", "sortModifiers", "sortedModifiers", "unsortedModifiers", "isPositionSensitive", "sort", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "classGroupsInConflict", "classNames", "trim", "result", "originalClassName", "variantModifier", "modifierId", "classId", "includes", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "string", "arguments", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "reduce", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "callTailwindMerge", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "Number", "isNaN", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "span", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "position", "scaleBgRepeat", "repeat", "scaleBgSize", "size", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "animate", "aspect", "blur", "breakpoint", "color", "container", "ease", "font", "leading", "perspective", "radius", "shadow", "spacing", "text", "tracking", "columns", "box", "display", "sr", "float", "clear", "isolation", "object", "overflow", "overscroll", "inset", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "row", "gap", "justify", "content", "items", "baseline", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "list", "placeholder", "decoration", "indent", "align", "whitespace", "break", "wrap", "hyphens", "bg", "linear", "to", "radial", "conic", "from", "via", "rounded", "border", "divide", "outline", "ring", "opacity", "mask", "closest", "farthest", "filter", "brightness", "contrast", "grayscale", "invert", "saturate", "sepia", "table", "caption", "transition", "duration", "delay", "backface", "rotate", "scale", "skew", "transform", "origin", "translate", "accent", "appearance", "caret", "scheme", "cursor", "resize", "scroll", "snap", "touch", "select", "fill", "stroke", "mergeConfigs", "baseConfig", "extend", "override", "overrideProperty", "overrideConfigProperties", "mergeConfigProperties", "mergeArrayProperties", "baseObject", "override<PERSON><PERSON>", "overrideValue", "overrideObject", "mergeObject", "mergeValue", "concat", "extendTailwindMerge", "configExtension", "createConfig", "twMerge"], "mappings": ";;;;;;;;;;AAsBA,MAAMA,oBAAoB,GAAG,GAAG;AAEzB,MAAMC,qBAAqB,IAAIC,MAAiB,IAAI;IACvD,MAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC;IACvC,MAAM,EAAEG,sBAAsB,EAAEC,8BAAAA,EAAgC,GAAGJ,MAAM;IAEzE,MAAMK,eAAe,IAAIC,SAAiB,IAAI;QAC1C,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACV,oBAAoB,CAAC;;QAGxD,IAAIS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;YACjDF,UAAU,CAACG,KAAK,CAAE,CAAA;;QAGtB,OAAOC,iBAAiB,CAACJ,UAAU,EAAEN,QAAQ,CAAC,IAAIW,8BAA8B,CAACN,SAAS,CAAC;IAC9F,CAAA;IAED,MAAMO,2BAA2B,GAAGA,CAChCC,YAA8B,EAC9BC,kBAA2B,KAC3B;QACA,MAAMC,SAAS,GAAGb,sBAAsB,CAACW,YAAY,CAAC,IAAI,EAAE;QAE5D,IAAIC,kBAAkB,IAAIX,8BAA8B,CAACU,YAAY,CAAC,EAAE;YACpE,OAAO,CAAC;mBAAGE,SAAS,EAAE;mBAAGZ,8BAA8B,CAACU,YAAY,CAAE;aAAC;;QAG3E,OAAOE,SAAS;IACnB,CAAA;IAED,OAAO;QACHX,eAAe;QACfQ;IACH,CAAA;AACL,CAAC;AAED,MAAMF,iBAAiB,GAAGA,CACtBJ,UAAoB,EACpBU,eAAgC,KACF;IAC9B,IAAIV,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QACzB,OAAOQ,eAAe,CAACH,YAAY;;IAGvC,MAAMI,gBAAgB,GAAGX,UAAU,CAAC,CAAC,CAAE;IACvC,MAAMY,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC;IAC1E,MAAMI,2BAA2B,GAAGH,mBAAA,GAC9BR,iBAAiB,CAACJ,UAAU,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAA,GAC1DK,SAAS;IAEf,IAAIF,2BAA2B,EAAE;QAC7B,OAAOA,2BAA2B;;IAGtC,IAAIL,eAAe,CAACQ,UAAU,CAAChB,MAAM,KAAK,CAAC,EAAE;QACzC,OAAOe,SAAS;;IAGpB,MAAME,SAAS,GAAGnB,UAAU,CAACoB,IAAI,CAAC7B,oBAAoB,CAAC;IAEvD,OAAOmB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,CAAC,EAAEC,SAAAA,EAAW,GAAKA,SAAS,CAACH,SAAS,CAAC,CAAC,EAAEZ,YAAY;AACjG,CAAC;AAED,MAAMgB,sBAAsB,GAAG,YAAY;AAE3C,MAAMlB,8BAA8B,IAAIN,SAAiB,IAAI;IACzD,IAAIwB,sBAAsB,CAACC,IAAI,CAACzB,SAAS,CAAC,EAAE;QACxC,MAAM0B,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC3B,SAAS,CAAE,CAAC,CAAC,CAAC;QAC7E,MAAM4B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C;QAED,IAAIF,QAAQ,EAAE;;YAEV,OAAO,aAAa,GAAGA,QAAQ;;;AAG3C,CAAC;AAED;;CAEG,GACI,MAAMhC,cAAc,IAAIF,MAAkD,IAAI;IACjF,MAAM,EAAEqC,KAAK,EAAEC,WAAAA,EAAa,GAAGtC,MAAM;IACrC,MAAMC,QAAQ,GAAoB;QAC9BmB,QAAQ,EAAE,IAAImB,GAAG,CAA2B,CAAA;QAC5Cd,UAAU,EAAE,EAAA;IACf,CAAA;IAED,IAAK,MAAMX,YAAY,IAAIwB,WAAW,CAAE;QACpCE,yBAAyB,CAACF,WAAW,CAACxB,YAAY,CAAE,EAAEb,QAAQ,EAAEa,YAAY,EAAEuB,KAAK,CAAC;;IAGxF,OAAOpC,QAAQ;AACnB,CAAC;AAED,MAAMuC,yBAAyB,GAAGA,CAC9BC,UAAwC,EACxCxB,eAAgC,EAChCH,YAA8B,EAC9BuB,KAAoC,KACpC;IACAI,UAAU,CAACC,OAAO,EAAEC,eAAe,IAAI;QACnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;YACrC,MAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAG1B,eAAe,GAAG4B,OAAO,CAAC5B,eAAe,EAAE0B,eAAe,CAAC;YACxFC,qBAAqB,CAAC9B,YAAY,GAAGA,YAAY;YACjD;;QAGJ,IAAI,OAAO6B,eAAe,KAAK,UAAU,EAAE;YACvC,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;gBAChCH,yBAAyB,CACrBG,eAAe,CAACN,KAAK,CAAC,EACtBpB,eAAe,EACfH,YAAY,EACZuB,KAAK,CACR;gBACD;;YAGJpB,eAAe,CAACQ,UAAU,CAACsB,IAAI,CAAC;gBAC5BlB,SAAS,EAAEc,eAAe;gBAC1B7B;YACH,CAAA,CAAC;YAEF;;QAGJkC,MAAM,CAACC,OAAO,CAACN,eAAe,CAAC,CAACD,OAAO,CAAC,CAAC,CAACQ,GAAG,EAAET,UAAU,CAAC,KAAI;YAC1DD,yBAAyB,CACrBC,UAAU,EACVI,OAAO,CAAC5B,eAAe,EAAEiC,GAAG,CAAC,EAC7BpC,YAAY,EACZuB,KAAK,CACR;QACL,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMQ,OAAO,GAAGA,CAAC5B,eAAgC,EAAEkC,IAAY,KAAI;IAC/D,IAAIC,sBAAsB,GAAGnC,eAAe;IAE5CkC,IAAI,CAAC3C,KAAK,CAACV,oBAAoB,CAAC,CAAC4C,OAAO,EAAEW,QAAQ,IAAI;QAClD,IAAI,CAACD,sBAAsB,CAAChC,QAAQ,CAACkC,GAAG,CAACD,QAAQ,CAAC,EAAE;YAChDD,sBAAsB,CAAChC,QAAQ,CAACmC,GAAG,CAACF,QAAQ,EAAE;gBAC1CjC,QAAQ,EAAE,IAAImB,GAAG,CAAE,CAAA;gBACnBd,UAAU,EAAE,EAAA;YACf,CAAA,CAAC;;QAGN2B,sBAAsB,GAAGA,sBAAsB,CAAChC,QAAQ,CAACC,GAAG,CAACgC,QAAQ,CAAE;IAC3E,CAAC,CAAC;IAEF,OAAOD,sBAAsB;AACjC,CAAC;AAED,MAAMN,aAAa,IAAIU,IAAkC,GACpDA,IAAoB,CAACV,aAAa;AC9KvC,oJAAA;AACO,MAAMW,cAAc,IAAgBC,YAAoB,IAA0B;IACrF,IAAIA,YAAY,GAAG,CAAC,EAAE;QAClB,OAAO;YACHrC,GAAG,EAAEA,CAAA,GAAMG,SAAS;YACpB+B,GAAG,EAAEA,CAAA,IAAQ,CAAH;QACb,CAAA;;IAGL,IAAII,SAAS,GAAG,CAAC;IACjB,IAAIC,KAAK,GAAG,IAAIrB,GAAG,CAAc,CAAA;IACjC,IAAIsB,aAAa,GAAG,IAAItB,GAAG,CAAc,CAAA;IAEzC,MAAMuB,MAAM,GAAGA,CAACZ,GAAQ,EAAEa,KAAY,KAAI;QACtCH,KAAK,CAACL,GAAG,CAACL,GAAG,EAAEa,KAAK,CAAC;QACrBJ,SAAS,EAAE;QAEX,IAAIA,SAAS,GAAGD,YAAY,EAAE;YAC1BC,SAAS,GAAG,CAAC;YACbE,aAAa,GAAGD,KAAK;YACrBA,KAAK,GAAG,IAAIrB,GAAG,CAAE,CAAA;;IAExB,CAAA;IAED,OAAO;QACHlB,GAAGA,EAAC6B,GAAG,EAAA;YACH,IAAIa,KAAK,GAAGH,KAAK,CAACvC,GAAG,CAAC6B,GAAG,CAAC;YAE1B,IAAIa,KAAK,KAAKvC,SAAS,EAAE;gBACrB,OAAOuC,KAAK;;YAEhB,IAAI,CAACA,KAAK,GAAGF,aAAa,CAACxC,GAAG,CAAC6B,GAAG,CAAC,MAAM1B,SAAS,EAAE;gBAChDsC,MAAM,CAACZ,GAAG,EAAEa,KAAK,CAAC;gBAClB,OAAOA,KAAK;;QAEnB,CAAA;QACDR,GAAGA,EAACL,GAAG,EAAEa,KAAK,EAAA;YACV,IAAIH,KAAK,CAACN,GAAG,CAACJ,GAAG,CAAC,EAAE;gBAChBU,KAAK,CAACL,GAAG,CAACL,GAAG,EAAEa,KAAK,CAAC;mBAClB;gBACHD,MAAM,CAACZ,GAAG,EAAEa,KAAK,CAAC;;QAEzB;IACJ,CAAA;AACL,CAAC;ACjDM,MAAMC,kBAAkB,GAAG,GAAG;AACrC,MAAMC,kBAAkB,GAAG,GAAG;AAC9B,MAAMC,yBAAyB,GAAGD,kBAAkB,CAACxD,MAAM;AAEpD,MAAM0D,oBAAoB,IAAInE,MAAiB,IAAI;IACtD,MAAM,EAAEoE,MAAM,EAAEC,0BAAAA,EAA4B,GAAGrE,MAAM;IAErD;;;;;GAKG,GACH,IAAIsE,cAAc,IAAIhE,SAAiB,IAAqB;QACxD,MAAMiE,SAAS,GAAG,EAAE;QAEpB,IAAIC,YAAY,GAAG,CAAC;QACpB,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,aAAa,GAAG,CAAC;QACrB,IAAIC,uBAA2C;QAE/C,IAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGtE,SAAS,CAACG,MAAM,EAAEmE,KAAK,EAAE,CAAE;YACnD,IAAIC,gBAAgB,GAAGvE,SAAS,CAACsE,KAAK,CAAC;YAEvC,IAAIJ,YAAY,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,EAAE;gBACxC,IAAII,gBAAgB,KAAKZ,kBAAkB,EAAE;oBACzCM,SAAS,CAACxB,IAAI,CAACzC,SAAS,CAACiB,KAAK,CAACmD,aAAa,EAAEE,KAAK,CAAC,CAAC;oBACrDF,aAAa,GAAGE,KAAK,GAAGV,yBAAyB;oBACjD;;gBAGJ,IAAIW,gBAAgB,KAAK,GAAG,EAAE;oBAC1BF,uBAAuB,GAAGC,KAAK;oBAC/B;;;YAIR,IAAIC,gBAAgB,KAAK,GAAG,EAAE;gBAC1BL,YAAY,EAAE;mBACX,IAAIK,gBAAgB,KAAK,GAAG,EAAE;gBACjCL,YAAY,EAAE;mBACX,IAAIK,gBAAgB,KAAK,GAAG,EAAE;gBACjCJ,UAAU,EAAE;mBACT,IAAII,gBAAgB,KAAK,GAAG,EAAE;gBACjCJ,UAAU,EAAE;;;QAIpB,MAAMK,kCAAkC,GACpCP,SAAS,CAAC9D,MAAM,KAAK,CAAC,GAAGH,SAAS,GAAGA,SAAS,CAAC6B,SAAS,CAACuC,aAAa,CAAC;QAC3E,MAAMK,aAAa,GAAGC,sBAAsB,CAACF,kCAAkC,CAAC;QAChF,MAAMG,oBAAoB,GAAGF,aAAa,KAAKD,kCAAkC;QACjF,MAAMI,4BAA4B,GAC9BP,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAA,GAC/CC,uBAAuB,GAAGD,aAAA,GAC1BlD,SAAS;QAEnB,OAAO;YACH+C,SAAS;YACTU,oBAAoB;YACpBF,aAAa;YACbG;QACH,CAAA;IACJ,CAAA;IAED,IAAId,MAAM,EAAE;QACR,MAAMe,UAAU,GAAGf,MAAM,GAAGH,kBAAkB;QAC9C,MAAMmB,sBAAsB,GAAGd,cAAc;QAC7CA,cAAc,IAAIhE,SAAS,GACvBA,SAAS,CAAC+E,UAAU,CAACF,UAAU,CAAA,GACzBC,sBAAsB,CAAC9E,SAAS,CAAC6B,SAAS,CAACgD,UAAU,CAAC1E,MAAM,CAAC,CAAA,GAC7D;gBACI6E,UAAU,EAAE,IAAI;gBAChBf,SAAS,EAAE,EAAE;gBACbU,oBAAoB,EAAE,KAAK;gBAC3BF,aAAa,EAAEzE,SAAS;gBACxB4E,4BAA4B,EAAE1D;YACjC,CAAA;;IAGf,IAAI6C,0BAA0B,EAAE;QAC5B,MAAMe,sBAAsB,GAAGd,cAAc;QAC7CA,cAAc,IAAIhE,SAAS,GACvB+D,0BAA0B,CAAC;gBAAE/D,SAAS;gBAAEgE,cAAc,EAAEc;aAAwB,CAAC;;IAGzF,OAAOd,cAAc;AACzB,CAAC;AAED,MAAMU,sBAAsB,IAAID,aAAqB,IAAI;IACrD,IAAIA,aAAa,CAACQ,QAAQ,CAACvB,kBAAkB,CAAC,EAAE;QAC5C,OAAOe,aAAa,CAAC5C,SAAS,CAAC,CAAC,EAAE4C,aAAa,CAACtE,MAAM,GAAG,CAAC,CAAC;;IAG/D;;;GAGG,GACH,IAAIsE,aAAa,CAACM,UAAU,CAACrB,kBAAkB,CAAC,EAAE;QAC9C,OAAOe,aAAa,CAAC5C,SAAS,CAAC,CAAC,CAAC;;IAGrC,OAAO4C,aAAa;AACxB,CAAC;ACvGD;;;;CAIG,GACI,MAAMS,mBAAmB,IAAIxF,MAAiB,IAAI;IACrD,MAAMyF,uBAAuB,GAAGzC,MAAM,CAAC0C,WAAW,CAC9C1F,MAAM,CAACyF,uBAAuB,CAACE,GAAG,EAAEC,QAAQ,GAAK;YAACA,QAAQ;YAAE,IAAI;SAAC,CAAC,CACrE;IAED,MAAMC,aAAa,IAAItB,SAAmB,IAAI;QAC1C,IAAIA,SAAS,CAAC9D,MAAM,IAAI,CAAC,EAAE;YACvB,OAAO8D,SAAS;;QAGpB,MAAMuB,eAAe,GAAa,EAAE;QACpC,IAAIC,iBAAiB,GAAa,EAAE;QAEpCxB,SAAS,CAAC7B,OAAO,EAAEkD,QAAQ,IAAI;YAC3B,MAAMI,mBAAmB,GAAGJ,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIH,uBAAuB,CAACG,QAAQ,CAAC;YAEpF,IAAII,mBAAmB,EAAE;gBACrBF,eAAe,CAAC/C,IAAI,CAAC,GAAGgD,iBAAiB,CAACE,IAAI,CAAA,CAAE,EAAEL,QAAQ,CAAC;gBAC3DG,iBAAiB,GAAG,EAAE;mBACnB;gBACHA,iBAAiB,CAAChD,IAAI,CAAC6C,QAAQ,CAAC;;QAExC,CAAC,CAAC;QAEFE,eAAe,CAAC/C,IAAI,CAAC,GAAGgD,iBAAiB,CAACE,IAAI,CAAA,CAAE,CAAC;QAEjD,OAAOH,eAAe;IACzB,CAAA;IAED,OAAOD,aAAa;AACxB,CAAC;AC7BM,MAAMK,iBAAiB,IAAIlG,MAAiB,GAAA,CAAM;QACrD4D,KAAK,EAAEH,cAAc,CAAiBzD,MAAM,CAAC2D,SAAS,CAAC;QACvDW,cAAc,EAAEH,oBAAoB,CAACnE,MAAM,CAAC;QAC5C6F,aAAa,EAAEL,mBAAmB,CAACxF,MAAM,CAAC;QAC1C,GAAGD,qBAAqB,CAACC,MAAM,CAAA;IAClC,CAAA,CAAC;ACVF,MAAMmG,mBAAmB,GAAG,KAAK;AAE1B,MAAMC,cAAc,GAAGA,CAACC,SAAiB,EAAEC,WAAwB,KAAI;IAC1E,MAAM,EAAEhC,cAAc,EAAEjE,eAAe,EAAEQ,2BAA2B,EAAEgF,aAAAA,EAAe,GACjFS,WAAW;IAEf;;;;;;GAMG,GACH,MAAMC,qBAAqB,GAAa,EAAE;IAC1C,MAAMC,UAAU,GAAGH,SAAS,CAACI,IAAI,CAAA,CAAE,CAACjG,KAAK,CAAC2F,mBAAmB,CAAC;IAE9D,IAAIO,MAAM,GAAG,EAAE;IAEf,IAAK,IAAI9B,KAAK,GAAG4B,UAAU,CAAC/F,MAAM,GAAG,CAAC,EAAEmE,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,CAAE;QAC5D,MAAM+B,iBAAiB,GAAGH,UAAU,CAAC5B,KAAK,CAAE;QAE5C,MAAM,EACFU,UAAU,EACVf,SAAS,EACTU,oBAAoB,EACpBF,aAAa,EACbG,4BAAAA,EACH,GAAGZ,cAAc,CAACqC,iBAAiB,CAAC;QAErC,IAAIrB,UAAU,EAAE;YACZoB,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;YACxE;;QAGJ,IAAI3F,kBAAkB,GAAG,CAAC,CAACmE,4BAA4B;QACvD,IAAIpE,YAAY,GAAGT,eAAe,CAC9BU,kBAAA,GACMgE,aAAa,CAAC5C,SAAS,CAAC,CAAC,EAAE+C,4BAA4B,CAAA,GACvDH,aAAa,CACtB;QAED,IAAI,CAACjE,YAAY,EAAE;YACf,IAAI,CAACC,kBAAkB,EAAE;;gBAErB2F,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJ5F,YAAY,GAAGT,eAAe,CAAC0E,aAAa,CAAC;YAE7C,IAAI,CAACjE,YAAY,EAAE;;gBAEf4F,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJ3F,kBAAkB,GAAG,KAAK;;QAG9B,MAAM6F,eAAe,GAAGf,aAAa,CAACtB,SAAS,CAAC,CAAC5C,IAAI,CAAC,GAAG,CAAC;QAE1D,MAAMkF,UAAU,GAAG5B,oBAAA,GACb2B,eAAe,GAAG5C,kBAAA,GAClB4C,eAAe;QAErB,MAAME,OAAO,GAAGD,UAAU,GAAG/F,YAAY;QAEzC,IAAIyF,qBAAqB,CAACQ,QAAQ,CAACD,OAAO,CAAC,EAAE;YAEzC;;QAGJP,qBAAqB,CAACxD,IAAI,CAAC+D,OAAO,CAAC;QAEnC,MAAME,cAAc,GAAGnG,2BAA2B,CAACC,YAAY,EAAEC,kBAAkB,CAAC;QACpF,IAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACvG,MAAM,EAAE,EAAEwG,CAAC,CAAE;YAC5C,MAAMC,KAAK,GAAGF,cAAc,CAACC,CAAC,CAAE;YAChCV,qBAAqB,CAACxD,IAAI,CAAC8D,UAAU,GAAGK,KAAK,CAAC;;;QAIlDR,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;;IAG5E,OAAOA,MAAM;AACjB,CAAC;ACxFD;;;;;;;;CAQG,YAMaS,MAAMA,CAAA,EAAA;IAClB,IAAIvC,KAAK,GAAG,CAAC;IACb,IAAIwC,QAAwB;IAC5B,IAAIC,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,MAAO1C,KAAK,GAAG2C,SAAS,CAAC9G,MAAM,CAAE;QAC7B,IAAK2G,QAAQ,GAAGG,SAAS,CAAC3C,KAAK,EAAE,CAAC,EAAG;YACjC,IAAKyC,aAAa,GAAGG,OAAO,CAACJ,QAAQ,CAAC,EAAG;gBACrCE,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAInC,OAAOC,MAAM;AACjB;AAEA,MAAME,OAAO,IAAIC,GAA4B,IAAI;IAC7C,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACzB,OAAOA,GAAG;;IAGd,IAAIJ,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,IAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAAChH,MAAM,EAAEiH,CAAC,EAAE,CAAE;QACjC,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;YACR,IAAKL,aAAa,GAAGG,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;gBAC9DJ,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAKnC,OAAOC,MAAM;AACjB,CAAC;SCvCeK,mBAAmBA,CAC/BC,iBAAoC,EACpC,GAAGC,gBAA0C,EAAA;IAE7C,IAAIvB,WAAwB;IAC5B,IAAIwB,QAAqC;IACzC,IAAIC,QAAqC;IACzC,IAAIC,cAAc,GAAGC,iBAAiB;IAEtC,SAASA,iBAAiBA,CAAC5B,SAAiB,EAAA;QACxC,MAAMrG,MAAM,GAAG6H,gBAAgB,CAACK,MAAM,CAClC,CAACC,cAAc,EAAEC,mBAAmB,GAAKA,mBAAmB,CAACD,cAAc,CAAC,EAC5EP,iBAAiB,EAAe,CACnC;QAEDtB,WAAW,GAAGJ,iBAAiB,CAAClG,MAAM,CAAC;QACvC8H,QAAQ,GAAGxB,WAAW,CAAC1C,KAAK,CAACvC,GAAG;QAChC0G,QAAQ,GAAGzB,WAAW,CAAC1C,KAAK,CAACL,GAAG;QAChCyE,cAAc,GAAGK,aAAa;QAE9B,OAAOA,aAAa,CAAChC,SAAS,CAAC;;IAGnC,SAASgC,aAAaA,CAAChC,SAAiB,EAAA;QACpC,MAAMiC,YAAY,GAAGR,QAAQ,CAACzB,SAAS,CAAC;QAExC,IAAIiC,YAAY,EAAE;YACd,OAAOA,YAAY;;QAGvB,MAAM5B,MAAM,GAAGN,cAAc,CAACC,SAAS,EAAEC,WAAW,CAAC;QACrDyB,QAAQ,CAAC1B,SAAS,EAAEK,MAAM,CAAC;QAE3B,OAAOA,MAAM;;IAGjB,OAAO,SAAS6B,iBAAiBA,CAAA,EAAA;QAC7B,OAAOP,cAAc,CAACb,MAAM,CAACqB,KAAK,CAAC,IAAI,EAAEjB,SAAgB,CAAC,CAAC;IAC9D,CAAA;AACL;AC/Ca,MAAAkB,SAAS,IAGpBvF,GAAiE,IAAiB;IAChF,MAAMwF,WAAW,IAAIrG,KAAuE,GACxFA,KAAK,CAACa,GAAG,CAAC,IAAI,EAAE;IAEpBwF,WAAW,CAAC5F,aAAa,GAAG,IAAa;IAEzC,OAAO4F,WAAW;AACtB,CAAA;ACZA,MAAMC,mBAAmB,GAAG,6BAA6B;AACzD,MAAMC,sBAAsB,GAAG,6BAA6B;AAC5D,MAAMC,aAAa,GAAG,YAAY;AAClC,MAAMC,eAAe,GAAG,kCAAkC;AAC1D,MAAMC,eAAe,GACjB,2HAA2H;AAC/H,MAAMC,kBAAkB,GAAG,oDAAoD;AAC/E,iGAAA;AACA,MAAMC,WAAW,GAAG,iEAAiE;AACrF,MAAMC,UAAU,GACZ,8FAA8F;AAE3F,MAAMC,UAAU,IAAIpF,KAAa,GAAK8E,aAAa,CAAC9G,IAAI,CAACgC,KAAK,CAAC;AAE/D,MAAMqF,QAAQ,IAAIrF,KAAa,GAAK,CAAC,CAACA,KAAK,IAAI,CAACsF,MAAM,CAACC,KAAK,CAACD,MAAM,CAACtF,KAAK,CAAC,CAAC;AAE3E,MAAMwF,SAAS,IAAIxF,KAAa,GAAK,CAAC,CAACA,KAAK,IAAIsF,MAAM,CAACE,SAAS,CAACF,MAAM,CAACtF,KAAK,CAAC,CAAC;AAE/E,MAAMyF,SAAS,IAAIzF,KAAa,GAAKA,KAAK,CAACwB,QAAQ,CAAC,GAAG,CAAC,IAAI6D,QAAQ,CAACrF,KAAK,CAACxC,KAAK,CAAC,CAAC,EAAE,CAAE,CAAA,CAAC,CAAC;AAExF,MAAMkI,YAAY,IAAI1F,KAAa,GAAK+E,eAAe,CAAC/G,IAAI,CAACgC,KAAK,CAAC;AAEnE,MAAM2F,KAAK,GAAGA,CAAA,GAAM,IAAI;AAE/B,MAAMC,YAAY,IAAI5F,KAAa,GAC/B,uJAAA;IACA,kFAAA;IACA,qGAAA;IACAgF,eAAe,CAAChH,IAAI,CAACgC,KAAK,CAAC,IAAI,CAACiF,kBAAkB,CAACjH,IAAI,CAACgC,KAAK,CAAC;AAElE,MAAM6F,OAAO,GAAGA,CAAA,GAAM,KAAK;AAE3B,MAAMC,QAAQ,IAAI9F,KAAa,GAAKkF,WAAW,CAAClH,IAAI,CAACgC,KAAK,CAAC;AAE3D,MAAM+F,OAAO,IAAI/F,KAAa,GAAKmF,UAAU,CAACnH,IAAI,CAACgC,KAAK,CAAC;AAElD,MAAMgG,iBAAiB,IAAIhG,KAAa,GAC3C,CAACiG,gBAAgB,CAACjG,KAAK,CAAC,IAAI,CAACkG,mBAAmB,CAAClG,KAAK,CAAC;AAEpD,MAAMmG,eAAe,IAAInG,KAAa,GAAKoG,mBAAmB,CAACpG,KAAK,EAAEqG,WAAW,EAAER,OAAO,CAAC;AAE3F,MAAMI,gBAAgB,IAAIjG,KAAa,GAAK4E,mBAAmB,CAAC5G,IAAI,CAACgC,KAAK,CAAC;AAE3E,MAAMsG,iBAAiB,IAAItG,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAEuG,aAAa,EAAEX,YAAY,CAAC;AAEpD,MAAMY,iBAAiB,IAAIxG,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAEyG,aAAa,EAAEpB,QAAQ,CAAC;AAEhD,MAAMqB,mBAAmB,IAAI1G,KAAa,GAC7CoG,mBAAmB,CAACpG,KAAK,EAAE2G,eAAe,EAAEd,OAAO,CAAC;AAEjD,MAAMe,gBAAgB,IAAI5G,KAAa,GAAKoG,mBAAmB,CAACpG,KAAK,EAAE6G,YAAY,EAAEd,OAAO,CAAC;AAE7F,MAAMe,iBAAiB,IAAI9G,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAE+G,aAAa,EAAEjB,QAAQ,CAAC;AAEhD,MAAMI,mBAAmB,IAAIlG,KAAa,GAAK6E,sBAAsB,CAAC7G,IAAI,CAACgC,KAAK,CAAC;AAEjF,MAAMgH,yBAAyB,IAAIhH,KAAa,GACnDiH,sBAAsB,CAACjH,KAAK,EAAEuG,aAAa,CAAC;AAEzC,MAAMW,6BAA6B,IAAIlH,KAAa,GACvDiH,sBAAsB,CAACjH,KAAK,EAAEmH,iBAAiB,CAAC;AAE7C,MAAMC,2BAA2B,IAAIpH,KAAa,GACrDiH,sBAAsB,CAACjH,KAAK,EAAE2G,eAAe,CAAC;AAE3C,MAAMU,uBAAuB,IAAIrH,KAAa,GAAKiH,sBAAsB,CAACjH,KAAK,EAAEqG,WAAW,CAAC;AAE7F,MAAMiB,wBAAwB,IAAItH,KAAa,GAClDiH,sBAAsB,CAACjH,KAAK,EAAE6G,YAAY,CAAC;AAExC,MAAMU,yBAAyB,IAAIvH,KAAa,GACnDiH,sBAAsB,CAACjH,KAAK,EAAE+G,aAAa,EAAE,IAAI,CAAC;AAEtD,UAAA;AAEA,MAAMX,mBAAmB,GAAGA,CACxBpG,KAAa,EACbwH,SAAqC,EACrCC,SAAqC,KACrC;IACA,MAAM9E,MAAM,GAAGiC,mBAAmB,CAAC1G,IAAI,CAAC8B,KAAK,CAAC;IAE9C,IAAI2C,MAAM,EAAE;QACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;YACX,OAAO6E,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC,CAAC;;QAG/B,OAAO8E,SAAS,CAAC9E,MAAM,CAAC,CAAC,CAAE,CAAC;;IAGhC,OAAO,KAAK;AAChB,CAAC;AAED,MAAMsE,sBAAsB,GAAGA,CAC3BjH,KAAa,EACbwH,SAAqC,EACrCE,kBAAkB,GAAG,KAAK,KAC1B;IACA,MAAM/E,MAAM,GAAGkC,sBAAsB,CAAC3G,IAAI,CAAC8B,KAAK,CAAC;IAEjD,IAAI2C,MAAM,EAAE;QACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;YACX,OAAO6E,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE/B,OAAO+E,kBAAkB;;IAG7B,OAAO,KAAK;AAChB,CAAC;AAED,SAAA;AAEA,MAAMf,eAAe,IAAIgB,KAAa,GAAKA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,YAAY;AAEzF,MAAMd,YAAY,IAAIc,KAAa,GAAKA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK;AAE5E,MAAMtB,WAAW,IAAIsB,KAAa,GAAKA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS;AAEpG,MAAMpB,aAAa,IAAIoB,KAAa,GAAKA,KAAK,KAAK,QAAQ;AAE3D,MAAMlB,aAAa,IAAIkB,KAAa,GAAKA,KAAK,KAAK,QAAQ;AAE3D,MAAMR,iBAAiB,IAAIQ,KAAa,GAAKA,KAAK,KAAK,aAAa;AAEpE,MAAMZ,aAAa,IAAIY,KAAa,GAAKA,KAAK,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrGpD,MAAMC,gBAAgB,GAAGA,CAAA,KAAK;IACjC;;;GAGG,SAGH,MAAMC,UAAU,GAAGnD,SAAS,CAAC,OAAO,CAAC;IACrC,MAAMoD,SAAS,GAAGpD,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMqD,SAAS,GAAGrD,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMsD,eAAe,GAAGtD,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMuD,aAAa,GAAGvD,SAAS,CAAC,UAAU,CAAC;IAC3C,MAAMwD,YAAY,GAAGxD,SAAS,CAAC,SAAS,CAAC;IACzC,MAAMyD,eAAe,GAAGzD,SAAS,CAAC,YAAY,CAAC;IAC/C,MAAM0D,cAAc,GAAG1D,SAAS,CAAC,WAAW,CAAC;IAC7C,MAAM2D,YAAY,GAAG3D,SAAS,CAAC,SAAS,CAAC;IACzC,MAAM4D,WAAW,GAAG5D,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAM6D,WAAW,GAAG7D,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAM8D,gBAAgB,GAAG9D,SAAS,CAAC,cAAc,CAAC;IAClD,MAAM+D,eAAe,GAAG/D,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMgE,eAAe,GAAGhE,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMiE,SAAS,GAAGjE,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMkE,gBAAgB,GAAGlE,SAAS,CAAC,aAAa,CAAC;IACjD,MAAMmE,WAAW,GAAGnE,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAMoE,SAAS,GAAGpE,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMqE,YAAY,GAAGrE,SAAS,CAAC,SAAS,CAAC;IAEzC;;;;;GAKG,SAGH,MAAMsE,UAAU,GAAGA,CAAA,GACf;YAAC,MAAM;YAAE,OAAO;YAAE,KAAK;YAAE,YAAY;YAAE,MAAM;YAAE,MAAM;YAAE,OAAO;YAAE,QAAQ;SAAU;IACtF,MAAMC,aAAa,GAAGA,CAAA,GAClB;YACI,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;;YAEV,UAAU;YACV,WAAW;;YAEX,WAAW;YACX,cAAc;;YAEd,cAAc;YACd,aAAa;;YAEb,aAAa;SACP;IACd,MAAMC,0BAA0B,GAAGA,CAAA,GAC/B,CAAC;eAAGD,aAAa,CAAA,CAAE;YAAE/C,mBAAmB;YAAED,gBAAgB;SAAU;IACxE,MAAMkD,aAAa,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,QAAQ;YAAE,MAAM;YAAE,SAAS;YAAE,QAAQ;SAAU;IACpF,MAAMC,eAAe,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,SAAS;YAAE,MAAM;SAAU;IAClE,MAAMC,uBAAuB,GAAGA,CAAA,GAC5B;YAACnD,mBAAmB;YAAED,gBAAgB;YAAEoC,YAAY;SAAU;IAClE,MAAMiB,UAAU,GAAGA,CAAA,GAAM;YAAClE,UAAU;YAAE,MAAM;YAAE,MAAM,EAAE;eAAGiE,uBAAuB,EAAE;SAAU;IAC5F,MAAME,yBAAyB,GAAGA,CAAA,GAC9B;YAAC/D,SAAS;YAAE,MAAM;YAAE,SAAS;YAAEU,mBAAmB;YAAED,gBAAgB;SAAU;IAClF,MAAMuD,0BAA0B,GAAGA,CAAA,GAC/B;YACI,MAAM;YACN;gBAAEC,IAAI,EAAE;oBAAC,MAAM;oBAAEjE,SAAS;oBAAEU,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;YACpET,SAAS;YACTU,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAMyD,yBAAyB,GAAGA,CAAA,GAC9B;YAAClE,SAAS;YAAE,MAAM;YAAEU,mBAAmB;YAAED,gBAAgB;SAAU;IACvE,MAAM0D,qBAAqB,GAAGA,CAAA,GAC1B;YAAC,MAAM;YAAE,KAAK;YAAE,KAAK;YAAE,IAAI;YAAEzD,mBAAmB;YAAED,gBAAgB;SAAU;IAChF,MAAM2D,qBAAqB,GAAGA,CAAA,GAC1B;YACI,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,UAAU;SACJ;IACd,MAAMC,uBAAuB,GAAGA,CAAA,GAC5B;YAAC,OAAO;YAAE,KAAK;YAAE,QAAQ;YAAE,SAAS;YAAE,aAAa;YAAE,UAAU;SAAU;IAC7E,MAAMC,WAAW,GAAGA,CAAA,GAAM;YAAC,MAAM,EAAE;eAAGT,uBAAuB,CAAA,CAAE;SAAU;IACzE,MAAMU,WAAW,GAAGA,CAAA,GAChB;YACI3E,UAAU;YACV,MAAM;YACN,MAAM;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK,EACL;eAAGiE,uBAAuB,CAAE,CAAA;SACtB;IACd,MAAMW,UAAU,GAAGA,CAAA,GAAM;YAACnC,UAAU;YAAE3B,mBAAmB;YAAED,gBAAgB;SAAU;IACrF,MAAMgE,eAAe,GAAGA,CAAA,GACpB,CACI;eAAGhB,aAAa,CAAE,CAAA;YAClB7B,2BAA2B;YAC3BV,mBAAmB;YACnB;gBAAEwD,QAAQ,EAAE;oBAAChE,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;SAC/C;IACd,MAAMkE,aAAa,GAAGA,CAAA,GAAM;YAAC,WAAW;YAAE;gBAAEC,MAAM,EAAE;oBAAC,EAAE;oBAAE,GAAG;oBAAE,GAAG;oBAAE,OAAO;oBAAE,OAAO;iBAAA;YAAC,CAAE;SAAU;IAChG,MAAMC,WAAW,GAAGA,CAAA,GAChB;YACI,MAAM;YACN,OAAO;YACP,SAAS;YACThD,uBAAuB;YACvBlB,eAAe;YACf;gBAAEmE,IAAI,EAAE;oBAACpE,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;SAC3C;IACd,MAAMsE,yBAAyB,GAAGA,CAAA,GAC9B;YAAC9E,SAAS;YAAEuB,yBAAyB;YAAEV,iBAAiB;SAAU;IACtE,MAAMkE,WAAW,GAAGA,CAAA,GAChB;;YAEI,EAAE;YACF,MAAM;YACN,MAAM;YACNlC,WAAW;YACXpC,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAMwE,gBAAgB,GAAGA,CAAA,GACrB;YAAC,EAAE;YAAEpF,QAAQ;YAAE2B,yBAAyB;YAAEV,iBAAiB;SAAU;IACzE,MAAMoE,cAAc,GAAGA,CAAA,GAAM;YAAC,OAAO;YAAE,QAAQ;YAAE,QAAQ;YAAE,QAAQ;SAAU;IAC7E,MAAMC,cAAc,GAAGA,CAAA,GACnB;YACI,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,KAAK;YACL,YAAY;YACZ,OAAO;YACP,YAAY;SACN;IACd,MAAMC,sBAAsB,GAAGA,CAAA,GAC3B;YAACvF,QAAQ;YAAEI,SAAS;YAAE2B,2BAA2B;YAAEV,mBAAmB;SAAU;IACpF,MAAMmE,SAAS,GAAGA,CAAA,GACd;;YAEI,EAAE;YACF,MAAM;YACNlC,SAAS;YACTzC,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAM6E,WAAW,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAEzF,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAC5F,MAAM8E,UAAU,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE1F,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAC3F,MAAM+E,SAAS,GAAGA,CAAA,GAAM;YAAC3F,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAClF,MAAMgF,cAAc,GAAGA,CAAA,GAAM;YAAC7F,UAAU;YAAE,MAAM,EAAE;eAAGiE,uBAAuB,CAAA,CAAE;SAAU;IAExF,OAAO;QACHzJ,SAAS,EAAE,GAAG;QACdtB,KAAK,EAAE;YACH4M,OAAO,EAAE;gBAAC,MAAM;gBAAE,MAAM;gBAAE,OAAO;gBAAE,QAAQ;aAAC;YAC5CC,MAAM,EAAE;gBAAC,OAAO;aAAC;YACjBC,IAAI,EAAE;gBAAC1F,YAAY;aAAC;YACpB2F,UAAU,EAAE;gBAAC3F,YAAY;aAAC;YAC1B4F,KAAK,EAAE;gBAAC3F,KAAK;aAAC;YACd4F,SAAS,EAAE;gBAAC7F,YAAY;aAAC;YACzB,aAAa,EAAE;gBAACA,YAAY;aAAC;YAC7B8F,IAAI,EAAE;gBAAC,IAAI;gBAAE,KAAK;gBAAE,QAAQ;aAAC;YAC7BC,IAAI,EAAE;gBAACzF,iBAAiB;aAAC;YACzB,aAAa,EAAE;gBACX,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,WAAW;gBACX,OAAO;aACV;YACD,cAAc,EAAE;gBAACN,YAAY;aAAC;YAC9BgG,OAAO,EAAE;gBAAC,MAAM;gBAAE,OAAO;gBAAE,MAAM;gBAAE,QAAQ;gBAAE,SAAS;gBAAE,OAAO;aAAC;YAChEC,WAAW,EAAE;gBAAC,UAAU;gBAAE,MAAM;gBAAE,QAAQ;gBAAE,UAAU;gBAAE,SAAS;gBAAE,MAAM;aAAC;YAC1EC,MAAM,EAAE;gBAAClG,YAAY;aAAC;YACtBmG,MAAM,EAAE;gBAACnG,YAAY;aAAC;YACtBoG,OAAO,EAAE;gBAAC,IAAI;gBAAEzG,QAAQ;aAAC;YACzB0G,IAAI,EAAE;gBAACrG,YAAY;aAAC;YACpB,aAAa,EAAE;gBAACA,YAAY;aAAC;YAC7BsG,QAAQ,EAAE;gBAAC,SAAS;gBAAE,OAAO;gBAAE,QAAQ;gBAAE,MAAM;gBAAE,OAAO;gBAAE,QAAQ;aAAA;QACrE,CAAA;QACDzN,WAAW,EAAE;;;;YAKT;;;OAGG,GACH4M,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;wBACJ,MAAM;wBACN,QAAQ;wBACR/F,UAAU;wBACVa,gBAAgB;wBAChBC,mBAAmB;wBACnB2C,WAAW;qBAAA;gBAElB,CAAA;aACJ;YACD;;;;OAIG,GACH0C,SAAS,EAAE;gBAAC,WAAW;aAAC;YACxB;;;OAGG,GACHU,OAAO,EAAE;gBACL;oBAAEA,OAAO,EAAE;wBAAC5G,QAAQ;wBAAEY,gBAAgB;wBAAEC,mBAAmB;wBAAEkC,cAAc;qBAAA;gBAAG,CAAA;aACjF;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEY,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,YAAY;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,OAAO;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC5D;;;OAGG,GACHkD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACrC;;;OAGG,GACHC,OAAO,EAAE;gBACL,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,cAAc;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB,oBAAoB;gBACpB,iBAAiB;gBACjB,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,WAAW;gBACX,QAAQ;aACX;YACD;;;OAGG,GACHC,EAAE,EAAE;gBAAC,SAAS;gBAAE,aAAa;aAAC;YAC9B;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YAC7D;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHC,SAAS,EAAE;gBAAC,SAAS;gBAAE,gBAAgB;aAAC;YACxC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,SAAS;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,MAAM,EAAEtD,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC7D;;;OAGG,GACHuD,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAEtD,aAAa,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACHuD,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAEtD,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,eAAe,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,eAAe,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACHc,QAAQ,EAAE;gBAAC,QAAQ;gBAAE,OAAO;gBAAE,UAAU;gBAAE,UAAU;gBAAE,QAAQ;aAAC;YAC/D;;;OAGG,GACHyC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAErD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACHsD,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAEtD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACHuD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAEvD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5B;;;OAGG,GACHwD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAExD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5B;;;OAGG,GACHyD,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAEzD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH0D,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE1D,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH2D,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE3D,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC9B;;;OAGG,GACH4D,UAAU,EAAE;gBAAC,SAAS;gBAAE,WAAW;gBAAE,UAAU;aAAC;YAChD;;;OAGG,GACHC,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC3H,SAAS;wBAAE,MAAM;wBAAEU,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMtE;;;OAGG,GACHmH,KAAK,EAAE;gBACH;oBACIA,KAAK,EAAE;wBACHhI,UAAU;wBACV,MAAM;wBACN,MAAM;wBACNgD,cAAc,EACd;2BAAGiB,uBAAuB,CAAE,CAAA;qBAAA;gBAEnC,CAAA;aACJ;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAEgE,IAAI,EAAE;wBAAC,KAAK;wBAAE,aAAa;wBAAE,KAAK;wBAAE,aAAa;qBAAA;iBAAG;aAAC;YAC1E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,MAAM;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YAC3D;;;OAGG,GACHA,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAChI,QAAQ;wBAAED,UAAU;wBAAE,MAAM;wBAAE,SAAS;wBAAE,MAAM;wBAAEa,gBAAgB;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACHqH,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,EAAE;wBAAEjI,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACHsH,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAElI,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHuH,KAAK,EAAE;gBACH;oBACIA,KAAK,EAAE;wBACHhI,SAAS;wBACT,OAAO;wBACP,MAAM;wBACN,MAAM;wBACNU,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEsD,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEkE,GAAG,EAAEjE,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEE,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEH,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEmE,GAAG,EAAElE,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEE,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,KAAK;wBAAE,KAAK;wBAAE,OAAO;wBAAE,WAAW;wBAAE,WAAW;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEC,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACHgE,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAEtE,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEuE,OAAO,EAAE,CAAC;2BAAGhE,qBAAqB,CAAE,CAAA;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACxE;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE,CAAC;2BAAGC,uBAAuB,CAAE,CAAA;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAChF;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM,EAAE;2BAAGA,uBAAuB,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC5E;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEgE,OAAO,EAAE;wBAAC,QAAQ,EAAE;2BAAGjE,qBAAqB,CAAE,CAAA;qBAAA;iBAAG;aAAC;YACtE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEkE,KAAK,EAAE,CAAC;2BAAGjE,uBAAuB,CAAE,CAAA;wBAAE;4BAAEkE,QAAQ,EAAE;gCAAC,EAAE;gCAAE,MAAM;6BAAA;wBAAC,CAAE;qBAAA;gBAAC,CAAE;aAAC;YACtF;;;OAGG,GACH,YAAY,EAAE;gBACV;oBAAEC,IAAI,EAAE;wBAAC,MAAM,EAAE;2BAAGnE,uBAAuB,CAAE,CAAA;wBAAE;4BAAEkE,QAAQ,EAAE;gCAAC,EAAE;gCAAE,MAAM;6BAAA;wBAAC,CAAE;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAEnE,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YAC/D;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE,CAAC;2BAAGC,uBAAuB,CAAE,CAAA;wBAAE,UAAU;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM,EAAE;2BAAGA,uBAAuB,CAAE,CAAA;qBAAA;iBAAG;aAAC;;YAExE;;;OAGG,GACHoE,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE5E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH6E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE7E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH8E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE9E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH+E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE/E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHgF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEhF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHiF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEjF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHkF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAElF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHmF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEnF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHoF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEpF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHqF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE5E,WAAW,CAAE;gBAAA,CAAE;aAAC;YACzB;;;OAGG,GACH6E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE7E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH8E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE9E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH+E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE/E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHgF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEhF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHiF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEjF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHkF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAElF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHmF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEnF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHoF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEpF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAET,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;YACtC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;;;;YAMtC;;;OAGG,GACHiB,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAEP,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/B;;;OAGG,GACHoF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC/G,cAAc;wBAAE,QAAQ,EAAE;2BAAG2B,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YACxD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE;wBACL3B,cAAc;wBACd,QAAQ;wBAAA,yGAAA,GAER,MAAM,EACN;2BAAG2B,WAAW,CAAE,CAAA;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE;wBACL3B,cAAc;wBACd,QAAQ;wBACR,MAAM;wBAAA,mIAAA,GAEN,OAAO;wBAAA,mIAAA,GAEP;4BAAEgH,MAAM,EAAE;gCAACjH,eAAe;6BAAA;wBAAG,CAAA,EAC7B;2BAAG4B,WAAW,CAAE,CAAA;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHsF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC,QAAQ;wBAAE,IAAI,EAAE;2BAAGtF,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC9C;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAAC,QAAQ;wBAAE,IAAI;wBAAE,MAAM,EAAE;2BAAGA,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAAC,QAAQ;wBAAE,IAAI,EAAE;2BAAGA,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;;;;YAM1D;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAEgC,IAAI,EAAE;wBAAC,MAAM;wBAAEhE,SAAS;wBAAEf,yBAAyB;wBAAEV,iBAAiB;qBAAA;gBAAG,CAAA;aAC9E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,aAAa;gBAAE,sBAAsB;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC,QAAQ;gBAAE,YAAY;aAAC;YACtC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEmF,IAAI,EAAE;wBAACzD,eAAe;wBAAE9B,mBAAmB;wBAAEM,iBAAiB;qBAAA;iBAAG;aAAC;YACpF;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBACI,cAAc,EAAE;wBACZ,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,eAAe;wBACf,UAAU;wBACV,gBAAgB;wBAChB,gBAAgB;wBAChBf,SAAS;wBACTQ,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEwF,IAAI,EAAE;wBAACvE,6BAA6B;wBAAEjB,gBAAgB;wBAAE6B,SAAS;qBAAA;iBAAG;aAAC;YACvF;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;aAAC;YAC7B;;;OAGG,GACH,aAAa,EAAE;gBAAC,SAAS;aAAC;YAC1B;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,cAAc;aAAC;YACpC;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;gBAAE,eAAe;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC,mBAAmB;gBAAE,cAAc;aAAC;YACpD;;;OAGG,GACH,cAAc,EAAE;gBAAC,oBAAoB;gBAAE,mBAAmB;aAAC;YAC3D;;;OAGG,GACHkE,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC/D,aAAa;wBAAE/B,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAChF;;;OAGG,GACH,YAAY,EAAE;gBACV;oBAAE,YAAY,EAAE;wBAACZ,QAAQ;wBAAE,MAAM;wBAAEa,mBAAmB;wBAAEM,iBAAiB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACHkF,OAAO,EAAE;gBACL;oBACIA,OAAO,EAAE;wBAAA,mIAAA,GAELxD,YAAY,EACZ;2BAAGmB,uBAAuB,CAAE,CAAA;qBAAA;gBAEnC,CAAA;aACJ;YACD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM;wBAAEnD,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAEqJ,IAAI,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACxD;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,SAAS;wBAAE,MAAM;wBAAEpJ,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE8F,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,OAAO;wBAAE,SAAS;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACpF;;;;OAIG,GACH,mBAAmB,EAAE;gBAAC;oBAAEwD,WAAW,EAAEvF,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE+B,IAAI,EAAE/B,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,WAAW;gBAAE,UAAU;gBAAE,cAAc;gBAAE,cAAc;aAAC;YAC5E;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEwF,UAAU,EAAE,CAAC;2BAAG9E,cAAc,CAAE,CAAA;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACxE;;;OAGG,GACH,2BAA2B,EAAE;gBACzB;oBACI8E,UAAU,EAAE;wBACRnK,QAAQ;wBACR,WAAW;wBACX,MAAM;wBACNa,mBAAmB;wBACnBI,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEkJ,UAAU,EAAExF,UAAU,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,kBAAkB,EAAE;gBAChB;oBAAE,kBAAkB,EAAE;wBAAC3E,QAAQ;wBAAE,MAAM;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aACpF;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,WAAW;gBAAE,WAAW;gBAAE,YAAY;gBAAE,aAAa;aAAC;YACzE;;;OAGG,GACH,eAAe,EAAE;gBAAC,UAAU;gBAAE,eAAe;gBAAE,WAAW;aAAC;YAC3D;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE8F,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,SAAS;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACH0D,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAEpG,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBACIqG,KAAK,EAAE;wBACH,UAAU;wBACV,KAAK;wBACL,QAAQ;wBACR,QAAQ;wBACR,UAAU;wBACV,aAAa;wBACb,KAAK;wBACL,OAAO;wBACPxJ,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH0J,UAAU,EAAE;gBACR;oBAAEA,UAAU,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;wBAAE,KAAK;wBAAE,UAAU;wBAAE,UAAU;wBAAE,cAAc;qBAAA;gBAAG,CAAA;aACtF;YACD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,QAAQ;wBAAE,OAAO;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACtD;;;OAGG,GACHC,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,YAAY;wBAAE,UAAU;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACtD;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YAClD;;;OAGG,GACHjC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE3H,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMvE;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE8J,EAAE,EAAE;wBAAC,OAAO;wBAAE,OAAO;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACvD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACpE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEA,EAAE,EAAE9F,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE8F,EAAE,EAAE5F,aAAa,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE4F,EAAE,EAAE1F,WAAW,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH,UAAU,EAAE;gBACR;oBACI0F,EAAE,EAAE;wBACA,MAAM;wBACN;4BACIC,MAAM,EAAE;gCACJ;oCAAEC,EAAE,EAAE;wCAAC,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;qCAAA;gCAAG,CAAA;gCACpDzK,SAAS;gCACTU,mBAAmB;gCACnBD,gBAAgB;6BACnB;4BACDiK,MAAM,EAAE;gCAAC,EAAE;gCAAEhK,mBAAmB;gCAAED,gBAAgB;6BAAC;4BACnDkK,KAAK,EAAE;gCAAC3K,SAAS;gCAAEU,mBAAmB;gCAAED,gBAAgB;6BAAA;wBAC3D,CAAA;wBACDqB,wBAAwB;wBACxBV,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAEmJ,EAAE,EAAE/F,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAEoG,IAAI,EAAE7F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC5D;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE8F,GAAG,EAAE9F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC1D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE0F,EAAE,EAAE1F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE6F,IAAI,EAAEpG,UAAU,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEqG,GAAG,EAAErG,UAAU,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEiG,EAAE,EAAEjG,UAAU,CAAE;gBAAA,CAAE;aAAC;;;;YAMrC;;;OAGG,GACHsG,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE9F,WAAW,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE+F,MAAM,EAAE9F,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAC5C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,MAAM,EAAE,CAAC;2BAAG7F,cAAc,CAAA,CAAE;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,MAAM,EAAE,CAAC;2BAAG9F,cAAc,CAAA,CAAE;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE6F,MAAM,EAAEvG,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEwG,MAAM,EAAExG,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEyG,OAAO,EAAE,CAAC;2BAAG/F,cAAc,CAAA,CAAE;wBAAE,MAAM;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBAAE,gBAAgB,EAAE;wBAACrF,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC1E;YACD;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAEwK,OAAO,EAAE;wBAAC,EAAE;wBAAEpL,QAAQ;wBAAE2B,yBAAyB;wBAAEV,iBAAiB;qBAAA;gBAAG,CAAA;aAC5E;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEmK,OAAO,EAAEzG,UAAU,CAAE;gBAAA,CAAE;aAAC;;;;YAM5C;;;OAGG,GACH6B,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;;wBAEJ,EAAE;wBACF,MAAM;wBACNtD,WAAW;wBACXhB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE+E,MAAM,EAAE7B,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBACI,cAAc,EAAE;wBACZ,MAAM;wBACNxB,gBAAgB;wBAChBjB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAAC;oBAAE,cAAc,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE0G,IAAI,EAAEjG,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YACxC;;;;;OAKG,GACH,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEiG,IAAI,EAAE1G,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtC;;;;;OAKG,GACH,eAAe,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC3E,QAAQ;wBAAEiB,iBAAiB;qBAAA;gBAAC,CAAE;aAAC;YACnE;;;;;OAKG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAE0D,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,YAAY,EAAES,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,YAAY,EAAET,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;wBACX,MAAM;wBACNvB,eAAe;wBACflB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH2G,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAACtL,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE,CAAC;2BAAG0E,cAAc,CAAA,CAAE;wBAAE,aAAa;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACpF;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAE,WAAW,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;gBAAG,CAAA;gBAC3E,cAAc;aACjB;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAEiG,IAAI,EAAE;wBAAC,KAAK;wBAAE,UAAU;wBAAE,WAAW;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAACvL,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACxD,4BAA4B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEuF,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAChF,0BAA0B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC5E,8BAA8B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtE,4BAA4B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClE,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC9D,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;YACjF,4BAA4B,EAAE;gBAAC;oBAAE,kBAAkB,EAAE2E,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAChF,0BAA0B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC5E,8BAA8B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtE,4BAA4B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACrE,wBAAwB,EAAE;gBACtB;oBAAE,aAAa,EAAE;wBAAC;4BAAE6G,OAAO,EAAE;gCAAC,MAAM;gCAAE,QAAQ;6BAAC;4BAAEC,QAAQ,EAAE;gCAAC,MAAM;gCAAE,QAAQ;6BAAA;wBAAG,CAAA;qBAAA;gBAAG,CAAA;aACrF;YACD,uBAAuB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE7H,aAAa,CAAE;gBAAA,CAAE;aAAC;YAChE,sBAAsB,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC5D,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACtD,2BAA2B,EAAE;gBAAC;oBAAE,iBAAiB,EAAEuF,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC9E,yBAAyB,EAAE;gBAAC;oBAAE,eAAe,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC1E,6BAA6B,EAAE;gBAAC;oBAAE,iBAAiB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpE,2BAA2B,EAAE;gBAAC;oBAAE,eAAe,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE4G,IAAI,EAAE;wBAAC,OAAO;wBAAE,WAAW;wBAAE,OAAO;qBAAA;iBAAG;aAAC;YACxD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBAAE,aAAa,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;gBAAG,CAAA;aAChF;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEA,IAAI,EAAE3G,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE2G,IAAI,EAAEzG,aAAa,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEyG,IAAI,EAAEvG,WAAW,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,OAAO;wBAAE,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACtD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEuG,IAAI,EAAE;wBAAC,MAAM;wBAAE1K,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMzE;;;OAGG,GACH8K,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;;wBAEJ,EAAE;wBACF,MAAM;wBACN7K,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHmF,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAEP,SAAS,CAAE;gBAAA,CAAE;aAAC;YAC7B;;;OAGG,GACHmG,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAAC3L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC/E;;;OAGG,GACHgL,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC5L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;;wBAEX,EAAE;wBACF,MAAM;wBACNyC,eAAe;wBACfnB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACHkH,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAE;wBAAC,EAAE;wBAAE7L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACnF;;;OAGG,GACHkL,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAE9L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHmL,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC/L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHoL,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,EAAE;wBAAEhM,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBACI,iBAAiB,EAAE;;wBAEf,EAAE;wBACF,MAAM;wBACNC,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE4E,SAAS,CAAE;gBAAA,CAAE;aAAC;YACnD;;;OAGG,GACH,qBAAqB,EAAE;gBACnB;oBAAE,qBAAqB,EAAE;wBAACxF,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBACjB;oBAAE,mBAAmB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAClB;oBAAE,oBAAoB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAClF;YACD;;;OAGG,GACH,qBAAqB,EAAE;gBACnB;oBAAE,qBAAqB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBAAE,iBAAiB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,kBAAkB,EAAE;gBAChB;oBAAE,kBAAkB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC5E;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBACjB;oBAAE,mBAAmB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBAAE,gBAAgB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC9E;;;;YAMD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEsK,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAElH,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACnE;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvE;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEiI,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,KAAK;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;;;;YAMzC;;;OAGG,GACHC,UAAU,EAAE;gBACR;oBACIA,UAAU,EAAE;wBACR,EAAE;wBACF,KAAK;wBACL,QAAQ;wBACR,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,MAAM;wBACNtL,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAEuL,UAAU,EAAE;wBAAC,QAAQ;wBAAE,UAAU;qBAAA;gBAAC,CAAE;aAAC;YAC/D;;;OAGG,GACHC,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAACpM,QAAQ;wBAAE,SAAS;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACtF;;;OAGG,GACHuF,IAAI,EAAE;gBACF;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE1C,SAAS;wBAAE5C,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aACpF;YACD;;;OAGG,GACHyL,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACrM,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHiF,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAEnC,YAAY;wBAAE7C,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMrF;;;OAGG,GACH0L,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACHhG,WAAW,EAAE;gBACT;oBAAEA,WAAW,EAAE;wBAAC/C,gBAAgB;wBAAE1C,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAAC;oBAAE,oBAAoB,EAAEiD,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC9E;;;OAGG,GACH0I,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE9G,WAAW,CAAE;gBAAA,CAAE;aAAC;YACnC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH+G,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE9G,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,UAAU,EAAE;gBAAC,UAAU;aAAC;YACxB;;;OAGG,GACH+G,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE9G,SAAS,CAAE;gBAAA,CAAE;aAAC;YAC7B;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAEA,SAAS,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAEA,SAAS,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH+G,SAAS,EAAE;gBACP;oBAAEA,SAAS,EAAE;wBAAC7L,mBAAmB;wBAAED,gBAAgB;wBAAE,EAAE;wBAAE,MAAM;wBAAE,KAAK;wBAAE,KAAK;qBAAA;gBAAG,CAAA;aACnF;YACD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE+L,MAAM,EAAE9I,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC9D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE6I,SAAS,EAAE;wBAAC,IAAI;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClD;;;OAGG,GACHE,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAEhH,cAAc,CAAE;gBAAA,CAAE;aAAC;YAC5C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,gBAAgB;aAAC;;;;YAMpC;;;OAGG,GACHiH,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAElI,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACHmI,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEC,KAAK,EAAEpI,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBAAEqI,MAAM,EAAE;wBAAC,QAAQ;wBAAE,MAAM;wBAAE,OAAO;wBAAE,YAAY;wBAAE,WAAW;wBAAE,YAAY;qBAAA;gBAAG,CAAA;aACnF;YACD;;;OAGG,GACHC,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;wBACJ,MAAM;wBACN,SAAS;wBACT,SAAS;wBACT,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,aAAa;wBACb,MAAM;wBACN,cAAc;wBACd,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,eAAe;wBACf,OAAO;wBACP,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,UAAU;wBACV,YAAY;wBACZ,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,SAAS;wBACT,UAAU;wBACVpM,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,OAAO;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACHsM,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,EAAE;wBAAE,GAAG;wBAAE,GAAG;qBAAA;iBAAG;aAAC;YAC5C;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,MAAM;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEnJ,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEoJ,IAAI,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,GAAG;wBAAE,GAAG;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACnD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,WAAW;wBAAE,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACpD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,GAAG;wBAAE,MAAM;wBAAE,OAAO;qBAAA;iBAAG;aAAC;YACpD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,GAAG;wBAAE,IAAI;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACjD;;;OAGG,GACH,UAAU,EAAE;gBAAC,kBAAkB;aAAC;YAChC;;;OAGG,GACHC,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;wBACX,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,WAAW;wBACXzM,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;;;;YAMD;;;OAGG,GACH2M,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM,EAAE;2BAAG5I,UAAU,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBACR;oBACI6I,MAAM,EAAE;wBACJxN,QAAQ;wBACR2B,yBAAyB;wBACzBV,iBAAiB;wBACjBE,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACHqM,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM,EAAE;2BAAG7I,UAAU,CAAE,CAAA;qBAAA;iBAAG;aAAC;;;;YAM/C;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAA;QACtE,CAAA;QACD5N,sBAAsB,EAAE;YACpBqQ,QAAQ,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACtCC,UAAU,EAAE;gBAAC,cAAc;gBAAE,cAAc;aAAC;YAC5CC,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,OAAO;gBAAE,KAAK;gBAAE,KAAK;gBAAE,OAAO;gBAAE,QAAQ;gBAAE,MAAM;aAAC;YAC/E,SAAS,EAAE;gBAAC,OAAO;gBAAE,MAAM;aAAC;YAC5B,SAAS,EAAE;gBAAC,KAAK;gBAAE,QAAQ;aAAC;YAC5BU,IAAI,EAAE;gBAAC,OAAO;gBAAE,MAAM;gBAAE,QAAQ;aAAC;YACjCM,GAAG,EAAE;gBAAC,OAAO;gBAAE,OAAO;aAAC;YACvBM,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBO,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBtE,IAAI,EAAE;gBAAC,GAAG;gBAAE,GAAG;aAAC;YAChB,WAAW,EAAE;gBAAC,SAAS;aAAC;YACxB,YAAY,EAAE;gBACV,aAAa;gBACb,kBAAkB;gBAClB,YAAY;gBACZ,aAAa;gBACb,cAAc;aACjB;YACD,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,kBAAkB,EAAE;gBAAC,YAAY;aAAC;YAClC,YAAY,EAAE;gBAAC,YAAY;aAAC;YAC5B,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B,YAAY,EAAE;gBAAC,SAAS;gBAAE,UAAU;aAAC;YACrCgG,OAAO,EAAE;gBACL,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,gBAAgB,EAAE;gBAAC,kBAAkB;gBAAE,kBAAkB;aAAC;YAC1D,UAAU,EAAE;gBACR,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,cAAc,EAAE;gBACZ,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;aACnB;YACD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD2B,SAAS,EAAE;gBAAC,aAAa;gBAAE,aAAa;gBAAE,gBAAgB;aAAC;YAC3D,gBAAgB,EAAE;gBAAC,WAAW;gBAAE,aAAa;gBAAE,aAAa;gBAAE,aAAa;aAAC;YAC5E,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvCS,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,UAAU;aAAC;YACzC,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,UAAU,EAAE;gBAAC,OAAO;aAAA;QACvB,CAAA;QACDrW,8BAA8B,EAAE;YAC5B,WAAW,EAAE;gBAAC,SAAS;aAAA;QAC1B,CAAA;QACDqF,uBAAuB,EAAE;YACrB,GAAG;YACH,IAAI;YACJ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,iBAAiB;YACjB,MAAM;YACN,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,WAAW;SAAA;IAEoD,CAAA;AAC3E,CAAA;ACpzEA;;;CAGG,SACUoR,YAAY,GAAGA,CACxBC,UAAqB,EACrB,EACInT,SAAS,EACTS,MAAM,EACNC,0BAA0B,EAC1B0S,MAAM,GAAG,CAAE,CAAA,EACXC,QAAQ,GAAG,CAAA,CAAA,EACiC,KAChD;IACAC,gBAAgB,CAACH,UAAU,EAAE,WAAW,EAAEnT,SAAS,CAAC;IACpDsT,gBAAgB,CAACH,UAAU,EAAE,QAAQ,EAAE1S,MAAM,CAAC;IAC9C6S,gBAAgB,CAACH,UAAU,EAAE,4BAA4B,EAAEzS,0BAA0B,CAAC;IAEtF6S,wBAAwB,CAACJ,UAAU,CAACzU,KAAK,EAAE2U,QAAQ,CAAC3U,KAAK,CAAC;IAC1D6U,wBAAwB,CAACJ,UAAU,CAACxU,WAAW,EAAE0U,QAAQ,CAAC1U,WAAW,CAAC;IACtE4U,wBAAwB,CAACJ,UAAU,CAAC3W,sBAAsB,EAAE6W,QAAQ,CAAC7W,sBAAsB,CAAC;IAC5F+W,wBAAwB,CACpBJ,UAAU,CAAC1W,8BAA8B,EACzC4W,QAAQ,CAAC5W,8BAA8B,CAC1C;IACD6W,gBAAgB,CAACH,UAAU,EAAE,yBAAyB,EAAEE,QAAQ,CAACvR,uBAAuB,CAAC;IAEzF0R,qBAAqB,CAACL,UAAU,CAACzU,KAAK,EAAE0U,MAAM,CAAC1U,KAAK,CAAC;IACrD8U,qBAAqB,CAACL,UAAU,CAACxU,WAAW,EAAEyU,MAAM,CAACzU,WAAW,CAAC;IACjE6U,qBAAqB,CAACL,UAAU,CAAC3W,sBAAsB,EAAE4W,MAAM,CAAC5W,sBAAsB,CAAC;IACvFgX,qBAAqB,CACjBL,UAAU,CAAC1W,8BAA8B,EACzC2W,MAAM,CAAC3W,8BAA8B,CACxC;IACDgX,oBAAoB,CAACN,UAAU,EAAEC,MAAM,EAAE,yBAAyB,CAAC;IAEnE,OAAOD,UAAU;AACrB,CAAA;AAEA,MAAMG,gBAAgB,GAAGA,CACrBI,UAAa,EACbC,WAAc,EACdC,aAA+B,KAC/B;IACA,IAAIA,aAAa,KAAK/V,SAAS,EAAE;QAC7B6V,UAAU,CAACC,WAAW,CAAC,GAAGC,aAAa;;AAE/C,CAAC;AAED,MAAML,wBAAwB,GAAGA,CAC7BG,UAAuD,EACvDG,cAAuE,KACvE;IACA,IAAIA,cAAc,EAAE;QAChB,IAAK,MAAMtU,GAAG,IAAIsU,cAAc,CAAE;YAC9BP,gBAAgB,CAACI,UAAU,EAAEnU,GAAG,EAAEsU,cAAc,CAACtU,GAAG,CAAC,CAAC;;;AAGlE,CAAC;AAED,MAAMiU,qBAAqB,GAAGA,CAC1BE,UAAuD,EACvDI,WAAoE,KACpE;IACA,IAAIA,WAAW,EAAE;QACb,IAAK,MAAMvU,GAAG,IAAIuU,WAAW,CAAE;YAC3BL,oBAAoB,CAACC,UAAU,EAAEI,WAAW,EAAEvU,GAAG,CAAC;;;AAG9D,CAAC;AAED,MAAMkU,oBAAoB,GAAGA,CACzBC,UAA6D,EAC7DI,WAA8D,EAC9DvU,GAAQ,KACR;IACA,MAAMwU,UAAU,GAAGD,WAAW,CAACvU,GAAG,CAAC;IAEnC,IAAIwU,UAAU,KAAKlW,SAAS,EAAE;QAC1B6V,UAAU,CAACnU,GAAG,CAAC,GAAGmU,UAAU,CAACnU,GAAG,CAAC,GAAGmU,UAAU,CAACnU,GAAG,CAAC,CAACyU,MAAM,CAACD,UAAU,CAAC,GAAGA,UAAU;;AAE3F,CAAC;AC5EM,MAAME,mBAAmB,GAAGA,CAI/BC,eAK4B,EAC5B,GAAGC,YAAsC,GAEzC,OAAOD,eAAe,KAAK,UAAA,GACrBlQ,mBAAmB,CAACgE,gBAAgB,EAAEkM,eAAe,EAAE,GAAGC,YAAY,CAAA,GACtEnQ,mBAAmB,CACf,IAAMkP,YAAY,CAAClL,gBAAgB,CAAE,CAAA,EAAEkM,eAAe,CAAC,EACvD,GAAGC,YAAY,CAAA;MCpBhBC,OAAO,GAAA,WAAA,GAAGpQ,mBAAmB,CAACgE,gBAAgB,CAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "debugId": null}}]}