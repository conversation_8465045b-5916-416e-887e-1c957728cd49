'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import SimpleMonopolyBoard from '@/components/game/SimpleMonopolyBoard'
import OptimizedGameControls from '@/components/game/OptimizedGameControls'
import GameRules, { GameHelpButton, useGameRules } from '@/components/game/GameRules'
import { EventCardModal, useEventCards, applyCardEffect } from '@/components/game/EventCards'
import { getPersonalityColor } from '@/components/game/BotLogic'
import { SimplePlayer } from '@/types/game'
import { BOARD_CASES } from '@/data/boardCases'
import { useViewport } from '@/hooks/useViewport'

// Mock data pour la démo avec bots intelligents
const createMockPlayers = (): SimplePlayer[] => [
  { id: '1', name: 'Vous', position: 0, money: 1500, isBot: false, avatar: '👤', color: '#10b981' },
  { id: '2', name: 'Alex_Trader', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('aggressive') },
  { id: '3', name: 'Sophie_CEO', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('conservative') },
  { id: '4', name: 'Marc_Startup', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('balanced') }
]

export default function GamePage() {
  const router = useRouter()
  const viewport = useViewport()
  const { isRulesOpen, openRules, closeRules } = useGameRules()
  const { currentCard, isCardOpen, hideCard, drawAndShowCard } = useEventCards()

  // États du jeu
  const [players, setPlayers] = useState<SimplePlayer[]>(createMockPlayers())
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0)
  const gamePhase = 'playing'
  const [turn, setTurn] = useState(1)
  const [diceValues, setDiceValues] = useState<[number, number]>([0, 0])
  const [isRolling, setIsRolling] = useState(false)
  const [lastAction, setLastAction] = useState<string>('')
  const [gameLog, setGameLog] = useState<string[]>([])
  const [isMoving, setIsMoving] = useState(false)
  const [hasRolledThisTurn, setHasRolledThisTurn] = useState(false)

  const addToLog = useCallback((message: string) => {
    setGameLog(prev => [...prev.slice(-9), message])
  }, [])

  const getCurrentPlayer = () => players[currentPlayerIndex]
  const getCurrentCase = () => BOARD_CASES[getCurrentPlayer().position]

  const movePlayer = useCallback(async (playerId: string, newPosition: number) => {
    setIsMoving(true)
    setPlayers(prev => prev.map(p => 
      p.id === playerId ? { ...p, position: newPosition } : p
    ))
    
    // Animation delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsMoving(false)
  }, [])

  const updatePlayerMoney = useCallback((playerId: string, amount: number) => {
    setPlayers(prev => prev.map(p => 
      p.id === playerId ? { ...p, money: Math.max(0, p.money + amount) } : p
    ))
  }, [])

  const rollDice = useCallback(async () => {
    if (isRolling || isMoving) return
    
    setIsRolling(true)
    const dice1 = Math.floor(Math.random() * 6) + 1
    const dice2 = Math.floor(Math.random() * 6) + 1
    setDiceValues([dice1, dice2])
    setHasRolledThisTurn(true)
    
    const currentPlayer = getCurrentPlayer()
    const total = dice1 + dice2
    const newPosition = (currentPlayer.position + total) % BOARD_CASES.length
    
    addToLog(`${currentPlayer.name} lance les dés: ${dice1} + ${dice2} = ${total}`)
    
    // Animation des dés
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsRolling(false)
    
    // Déplacer le joueur
    await movePlayer(currentPlayer.id, newPosition)
    
    // Traiter la case
    const landedCase = BOARD_CASES[newPosition]
    await handleCaseAction(currentPlayer, landedCase)
    
    setLastAction(`${currentPlayer.name} arrive sur ${landedCase.name}`)
  }, [isRolling, isMoving, currentPlayerIndex, players, addToLog, movePlayer])

  const handleCaseAction = useCallback(async (player: SimplePlayer, boardCase: any) => {
    switch (boardCase.type) {
      case 'start':
        updatePlayerMoney(player.id, 200)
        addToLog(`${player.name} passe par la case NAISSANCE et reçoit 200€`)
        break
      case 'tax':
        updatePlayerMoney(player.id, -boardCase.price)
        addToLog(`${player.name} paye ${boardCase.price}€ de ${boardCase.name}`)
        break
      case 'chance':
        drawAndShowCard()
        break
      case 'job':
        if (boardCase.price > 0) {
          updatePlayerMoney(player.id, boardCase.price)
          addToLog(`${player.name} obtient un emploi: ${boardCase.name} (+${boardCase.price}€/mois)`)
        }
        break
      case 'housing':
        updatePlayerMoney(player.id, -boardCase.price)
        addToLog(`${player.name} loue/achète: ${boardCase.name} (-${boardCase.price}€/mois)`)
        break
      case 'transport':
        updatePlayerMoney(player.id, -boardCase.price)
        addToLog(`${player.name} paye ${boardCase.price}€ pour ${boardCase.name}`)
        break
      case 'education':
        if (boardCase.price > 0) {
          updatePlayerMoney(player.id, -boardCase.price)
          addToLog(`${player.name} paye ${boardCase.price}€ pour ${boardCase.name}`)
        } else {
          addToLog(`${player.name} suit ${boardCase.name}`)
        }
        break
      case 'life':
        updatePlayerMoney(player.id, -boardCase.price)
        addToLog(`${player.name} dépense ${boardCase.price}€ pour ${boardCase.name}`)
        break
      case 'utility':
        updatePlayerMoney(player.id, -boardCase.price)
        addToLog(`${player.name} paye ${boardCase.price}€ pour ${boardCase.name}`)
        break
      case 'corner':
        addToLog(`${player.name} arrive sur ${boardCase.name}`)
        break
      default:
        addToLog(`${player.name} arrive sur ${boardCase.name}`)
    }
  }, [updatePlayerMoney, addToLog, drawAndShowCard])

  const nextTurn = useCallback(() => {
    const nextIndex = (currentPlayerIndex + 1) % players.length
    setCurrentPlayerIndex(nextIndex)
    setHasRolledThisTurn(false) // Réinitialiser pour le nouveau joueur
    setDiceValues([0, 0]) // Réinitialiser les dés
    setLastAction('')

    if (nextIndex === 0) {
      setTurn(prev => prev + 1)
    }
  }, [currentPlayerIndex, players.length])



  // Bot turn logic
  useEffect(() => {
    if (gamePhase === 'playing' && getCurrentPlayer().isBot && !isRolling && !isMoving) {
      const timer = setTimeout(() => {
        rollDice()
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [gamePhase, currentPlayerIndex, players, isRolling, isMoving, rollDice])

  // Auto next turn after action (seulement pour les bots)
  useEffect(() => {
    if (gamePhase === 'playing' && !isRolling && !isMoving && lastAction && getCurrentPlayer().isBot) {
      const currentCase = getCurrentCase()

      // Si c'est une case automatique (impôts, chance), passer automatiquement
      if (currentCase.type === 'tax' || currentCase.type === 'chance' || currentCase.type === 'start') {
        const isDouble = diceValues[0] === diceValues[1]
        if (!isDouble) {
          setTimeout(() => {
            nextTurn()
          }, 3000)
        }
      }
    }
  }, [gamePhase, isRolling, isMoving, lastAction, diceValues, nextTurn, getCurrentCase, getCurrentPlayer])

  const handleCardEffect = useCallback(() => {
    const currentPlayer = getCurrentPlayer()
    if (currentCard) {
      const message = applyCardEffect(currentCard, currentPlayer, setPlayers, currentPlayerIndex)
      addToLog(message)
    }

    // Continue le tour après la carte
    setTimeout(() => {
      const isDouble = diceValues[0] === diceValues[1]
      if (!isDouble) {
        nextTurn()
      }
    }, 2000)
  }, [currentPlayerIndex, players, addToLog, diceValues, nextTurn, currentCard])

  if (viewport.isMobile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-2">
        <div className="text-center py-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">TheRateRace.io</h1>
          <p className="text-gray-600 mb-4">
            Pour une meilleure expérience de jeu, veuillez utiliser un écran plus large.
          </p>
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retour à l'accueil
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 overflow-hidden">
      {/* Header minimal */}
      <div className="h-12 bg-white/80 backdrop-blur-sm border-b border-gray-200 flex items-center justify-between px-4">
        <h1 className="text-lg font-bold text-gray-800">TheRateRace.io</h1>
        <div className="flex items-center gap-2">
          <GameHelpButton onClick={openRules} />
          <button
            onClick={() => router.push('/')}
            className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
          >
            Quitter
          </button>
        </div>
      </div>

      {/* Game content */}
      <div className="h-[calc(100vh-3rem)] flex overflow-hidden">
        {/* Main game area */}
        <div className="flex-1 flex items-center justify-center p-2 min-w-0">
          <div className="w-full h-full max-w-[calc(100vh-8rem)] max-h-[calc(100vh-8rem)] aspect-square">
            <SimpleMonopolyBoard
              cases={BOARD_CASES}
              players={players}
              currentPlayer={currentPlayerIndex}
              diceValues={diceValues}
              isRolling={isRolling}
            />
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-72 bg-white/90 backdrop-blur-sm border-l border-gray-200 flex flex-col overflow-hidden">
          <OptimizedGameControls
            currentPlayer={getCurrentPlayer()}
            allPlayers={players}
            currentCase={getCurrentCase()}
            diceValues={diceValues}
            isRolling={isRolling}
            isMyTurn={!getCurrentPlayer().isBot && gamePhase === 'playing'}
            gameMessage={lastAction || (hasRolledThisTurn ? 'Cliquez sur "Terminer le tour"' : 'Cliquez sur "Lancer les dés"')}
            onRollDice={rollDice}
            onBuyProperty={() => {}}
            onEndTurn={nextTurn}
            onQuitGame={() => router.push('/')}
          />
        </div>
      </div>

      {/* Modals */}
      <GameRules isOpen={isRulesOpen} onClose={closeRules} />
      <EventCardModal
        isOpen={isCardOpen}
        card={currentCard}
        onClose={hideCard}
        onApply={handleCardEffect}
      />
    </div>
  )
}
