'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

// Types pour le jeu
interface Player {
  id: string
  name: string
  position: number
  money: number
  isBot: boolean
  avatar: string
}

interface GameState {
  currentPlayer: number
  players: Player[]
  turn: number
  phase: 'waiting' | 'playing' | 'finished'
}

export default function GamePage() {
  const [gameState, setGameState] = useState<GameState>({
    currentPlayer: 0,
    players: [
      { id: '1', name: 'V<PERSON>', position: 0, money: 1500, isBot: false, avatar: '👤' },
      { id: '2', name: 'Alex_Trader', position: 0, money: 1500, isBot: true, avatar: '🤖' },
      { id: '3', name: 'Sophie_CEO', position: 0, money: 1500, isBot: true, avatar: '🤖' },
      { id: '4', name: '<PERSON>_<PERSON><PERSON>', position: 0, money: 1500, isBot: true, avatar: '🤖' }
    ],
    turn: 1,
    phase: 'playing'
  })

  const [diceValue, setDiceValue] = useState<number | null>(null)
  const [isRolling, setIsRolling] = useState(false)

  const rollDice = () => {
    setIsRolling(true)
    setDiceValue(null)
    
    // Animation du dé
    setTimeout(() => {
      const value = Math.floor(Math.random() * 6) + 1
      setDiceValue(value)
      setIsRolling(false)
      
      // Déplacer le joueur
      movePlayer(value)
    }, 1000)
  }

  const movePlayer = (steps: number) => {
    setGameState(prev => {
      const newPlayers = [...prev.players]
      const currentPlayer = newPlayers[prev.currentPlayer]
      currentPlayer.position = (currentPlayer.position + steps) % 40 // 40 cases sur le plateau
      
      return {
        ...prev,
        players: newPlayers,
        currentPlayer: (prev.currentPlayer + 1) % prev.players.length
      }
    })
  }

  const currentPlayer = gameState.players[gameState.currentPlayer]
  const isMyTurn = !currentPlayer.isBot

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            
            {/* Header du Jeu */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-black text-primary mb-2">🎲 TheRateRace.io</h1>
              <p className="text-lg text-muted-foreground">Tour {gameState.turn} • C'est au tour de {currentPlayer.name}</p>
            </div>

            <div className="grid lg:grid-cols-4 gap-6">
              
              {/* Plateau de Jeu */}
              <div className="lg:col-span-3">
                <Card variant="elevated" className="p-6">
                  <h2 className="text-2xl font-bold mb-6 text-center">🏢 Plateau de Jeu</h2>
                  
                  {/* Plateau simplifié - grille 10x10 */}
                  <div className="grid grid-cols-10 gap-1 aspect-square max-w-2xl mx-auto">
                    {Array.from({ length: 100 }, (_, i) => {
                      const playersOnCase = gameState.players.filter(p => p.position === i)
                      const isCorner = i === 0 || i === 9 || i === 90 || i === 99
                      
                      return (
                        <div 
                          key={i} 
                          className={`
                            relative border border-border rounded text-xs flex items-center justify-center
                            ${isCorner ? 'bg-primary/20 font-bold' : 'bg-card'}
                            ${playersOnCase.length > 0 ? 'ring-2 ring-primary' : ''}
                          `}
                        >
                          <span className="text-[10px] text-muted-foreground">{i}</span>
                          {playersOnCase.length > 0 && (
                            <div className="absolute -top-1 -right-1 flex">
                              {playersOnCase.map(player => (
                                <span key={player.id} className="text-xs">
                                  {player.avatar}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>

                  {/* Contrôles du Jeu */}
                  <div className="mt-8 text-center">
                    {isMyTurn ? (
                      <div className="space-y-4">
                        <div className="text-xl font-bold text-primary">
                          🎯 C'est votre tour !
                        </div>
                        
                        <div className="flex items-center justify-center gap-4">
                          <div className="text-6xl">
                            {isRolling ? '🎲' : diceValue ? `⚀⚁⚂⚃⚄⚅`[diceValue - 1] : '🎲'}
                          </div>
                          
                          <Button 
                            onClick={rollDice}
                            disabled={isRolling || diceValue !== null}
                            variant="primary"
                            size="lg"
                            neonGlow
                            className="text-xl px-8 py-4"
                          >
                            {isRolling ? 'Lancement...' : diceValue ? `Vous avez fait ${diceValue}` : 'Lancer le Dé'}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-xl text-muted-foreground">
                        🤖 {currentPlayer.name} réfléchit...
                      </div>
                    )}
                  </div>
                </Card>
              </div>

              {/* Panneau des Joueurs */}
              <div className="space-y-4">
                <Card variant="elevated">
                  <h3 className="text-xl font-bold mb-4 text-center">👥 Joueurs</h3>
                  
                  <div className="space-y-3">
                    {gameState.players.map((player, index) => (
                      <div 
                        key={player.id}
                        className={`
                          p-3 rounded-lg border transition-all
                          ${index === gameState.currentPlayer 
                            ? 'border-primary bg-primary/10 ring-2 ring-primary/50' 
                            : 'border-border bg-card/50'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-2xl">{player.avatar}</span>
                            <div>
                              <div className="font-semibold text-sm">{player.name}</div>
                              <div className="text-xs text-muted-foreground">
                                Case {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-green-400">
                              {player.money}€
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Actions Rapides */}
                <Card variant="elevated">
                  <h3 className="text-lg font-bold mb-3">⚡ Actions</h3>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full">
                      📊 Statistiques
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      🏠 Propriétés
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      💳 Cartes
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      ⚙️ Options
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
