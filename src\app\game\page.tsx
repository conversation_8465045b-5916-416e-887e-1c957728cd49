'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

// Mock data pour la démo
const mockPlayers = [
  { id: '1', name: 'Vous', position: 0, money: 1500, avatar: '🟢', color: '#10b981' },
  { id: '2', name: 'Alex_Trader', position: 5, money: 1500, avatar: '🔴', color: '#ef4444' },
  { id: '3', name: 'Sophie_CEO', position: 12, money: 1500, avatar: '🟡', color: '#f59e0b' },
  { id: '4', name: 'Marc_Startup', position: 8, money: 1500, avatar: '🔵', color: '#3b82f6' }
]

const BOARD_CASES = [
  { id: 0, name: 'DÉPART', type: 'start', icon: '🏁', price: 0 },
  { id: 1, name: 'Startup Tech', type: 'property', price: 60, icon: '💻' },
  { id: 2, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 3, name: 'Blog Influenceur', type: 'property', price: 60, icon: '📱' },
  { id: 4, name: 'Impô<PERSON>', type: 'tax', price: 200, icon: '💸' },
  { id: 5, name: 'Aéroport', type: 'railroad', price: 200, icon: '✈️' },
  { id: 6, name: 'Café Hipster', type: 'property', price: 100, icon: '☕' },
  { id: 7, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 8, name: 'Coworking Space', type: 'property', price: 100, icon: '🏢' },
  { id: 9, name: 'Salle de Sport', type: 'property', price: 120, icon: '💪' },
  { id: 10, name: 'PRISON', type: 'corner', icon: '🔒', price: 0 },
  { id: 11, name: 'Agence Marketing', type: 'property', price: 140, icon: '📊' },
  { id: 12, name: 'Électricité', type: 'utility', price: 150, icon: '⚡' },
  { id: 13, name: 'Studio Photo', type: 'property', price: 140, icon: '📸' },
  { id: 14, name: 'Boutique Mode', type: 'property', price: 160, icon: '👗' },
  { id: 15, name: 'Gare', type: 'railroad', price: 200, icon: '🚂' },
  { id: 16, name: 'Restaurant Gastronomique', type: 'property', price: 180, icon: '🍽️' },
  { id: 17, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 18, name: 'Hôtel Boutique', type: 'property', price: 180, icon: '🏨' },
  { id: 19, name: 'Spa de Luxe', type: 'property', price: 200, icon: '🧘' },
  { id: 20, name: 'PARKING GRATUIT', type: 'corner', icon: '🅿️', price: 0 },
  { id: 21, name: 'Galerie d\'Art', type: 'property', price: 220, icon: '🎨' },
  { id: 22, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 23, name: 'Théâtre', type: 'property', price: 220, icon: '🎭' },
  { id: 24, name: 'Opéra', type: 'property', price: 240, icon: '🎼' },
  { id: 25, name: 'Port', type: 'railroad', price: 200, icon: '🚢' },
  { id: 26, name: 'Penthouse', type: 'property', price: 260, icon: '🏙️' },
  { id: 27, name: 'Villa Moderne', type: 'property', price: 260, icon: '🏡' },
  { id: 28, name: 'Eau', type: 'utility', price: 150, icon: '💧' },
  { id: 29, name: 'Château', type: 'property', price: 280, icon: '🏰' },
  { id: 30, name: 'ALLER EN PRISON', type: 'corner', icon: '👮', price: 0 },
  { id: 31, name: 'Gratte-Ciel', type: 'property', price: 300, icon: '🏗️' },
  { id: 32, name: 'Tour de Bureaux', type: 'property', price: 300, icon: '🏢' },
  { id: 33, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 34, name: 'Centre Commercial', type: 'property', price: 320, icon: '🛍️' },
  { id: 35, name: 'Métro', type: 'railroad', price: 200, icon: '🚇' },
  { id: 36, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 37, name: 'Yacht Club', type: 'property', price: 350, icon: '⛵' },
  { id: 38, name: 'Taxe de Luxe', type: 'tax', price: 100, icon: '💎' },
  { id: 39, name: 'Empire Financier', type: 'property', price: 400, icon: '🏦' }
]

export default function GamePage() {
  const router = useRouter()
  const [players, setPlayers] = useState(mockPlayers)
  const [currentPlayer, setCurrentPlayer] = useState(0)
  const [diceValues, setDiceValues] = useState([4, 2])
  const [isRolling, setIsRolling] = useState(false)
  const [gameMessage, setGameMessage] = useState("C'est votre tour ! Lancez les dés.")

  const rollDice = () => {
    if (isRolling) return

    setIsRolling(true)
    setGameMessage("Lancement des dés...")

    // Animation des dés
    setTimeout(() => {
      const dice1 = Math.floor(Math.random() * 6) + 1
      const dice2 = Math.floor(Math.random() * 6) + 1
      setDiceValues([dice1, dice2])

      const total = dice1 + dice2
      movePlayer(total)

      setIsRolling(false)
      setGameMessage(`Vous avez fait ${total} ! Déplacement en cours...`)
    }, 1500)
  }

  const movePlayer = (steps: number) => {
    setPlayers(prev => {
      const newPlayers = [...prev]
      const player = newPlayers[currentPlayer]
      const newPosition = (player.position + steps) % 40
      player.position = newPosition

      // Bonus si on passe par la case départ
      if (newPosition < player.position) {
        player.money += 200
        setGameMessage(`Vous passez par la case DÉPART ! +200€`)
      }

      return newPlayers
    })

    // Passer au joueur suivant après un délai
    setTimeout(() => {
      setCurrentPlayer((prev) => (prev + 1) % players.length)
      setGameMessage("Tour suivant...")
    }, 2000)
  }

  const handleBackToLobby = () => {
    router.push('/salons')
  }

  const renderBoardCase = (caseData: any, index: number) => {
    const playersOnCase = players.filter(p => p.position === index)

    return (
      <div
        key={index}
        className="relative bg-slate-700 border border-slate-600 flex flex-col items-center justify-center text-xs text-white p-1"
        style={{
          backgroundColor: caseData.type === 'property' ? '#4a5568' :
                          caseData.type === 'corner' ? '#2d3748' :
                          caseData.type === 'chance' ? '#553c9a' : '#4a5568'
        }}
      >
        <div className="text-lg mb-1">{caseData.icon}</div>
        <div className="text-center leading-tight">
          <div className="font-bold text-xs">{caseData.name}</div>
          {caseData.price > 0 && (
            <div className="text-yellow-400 text-xs">{caseData.price}€</div>
          )}
        </div>

        {/* Joueurs sur cette case */}
        {playersOnCase.length > 0 && (
          <div className="absolute -top-2 -right-2 flex gap-1">
            {playersOnCase.map((player, i) => (
              <div
                key={player.id}
                className="w-4 h-4 rounded-full border-2 border-white text-xs flex items-center justify-center"
                style={{ backgroundColor: player.color }}
              >
                {player.avatar}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 flex justify-between items-center p-4 z-10">
        <div className="flex items-center gap-2">
          <button
            onClick={handleBackToLobby}
            className="text-white hover:text-purple-300 transition-colors"
          >
            ← Retour au salon
          </button>
          <span className="text-white font-bold text-xl">TheRateRace.io</span>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-gray-300">🔊</span>
            <span className="text-gray-300">🔍</span>
            <span className="text-gray-300">⚙️</span>
          </div>
        </div>
      </header>

      {/* Plateau de jeu central */}
      <div className="flex-1 flex items-center justify-center p-20">
        <div className="relative">
          {/* Plateau de jeu */}
          <div className="w-[600px] h-[600px] bg-slate-800 rounded-lg border-4 border-slate-600 relative">
            {/* Cases du plateau */}
            <div className="absolute inset-4">
              {/* Cases du haut (0-9) */}
              <div className="absolute top-0 left-0 right-0 h-16 grid grid-cols-10 gap-1">
                {BOARD_CASES.slice(0, 10).map((caseData, i) => renderBoardCase(caseData, i))}
              </div>

              {/* Cases de droite (10-19) */}
              <div className="absolute top-16 right-0 bottom-16 w-16 grid grid-rows-10 gap-1">
                {BOARD_CASES.slice(10, 20).map((caseData, i) => renderBoardCase(caseData, i + 10))}
              </div>

              {/* Cases du bas (20-29) */}
              <div className="absolute bottom-0 left-0 right-0 h-16 grid grid-cols-10 gap-1">
                {BOARD_CASES.slice(20, 30).reverse().map((caseData, i) => renderBoardCase(caseData, 29 - i))}
              </div>

              {/* Cases de gauche (30-39) */}
              <div className="absolute top-16 left-0 bottom-16 w-16 grid grid-rows-10 gap-1">
                {BOARD_CASES.slice(30, 40).reverse().map((caseData, i) => renderBoardCase(caseData, 39 - i))}
              </div>
            </div>
          </div>

          {/* Dés au centre */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex gap-4">
            <div className={`w-20 h-20 bg-white rounded-lg shadow-lg flex items-center justify-center transition-transform ${isRolling ? 'animate-spin' : ''}`}>
              <div className="grid grid-cols-3 gap-1 p-2">
                {Array.from({length: diceValues[0]}).map((_, i) => (
                  <div key={i} className="w-2 h-2 bg-black rounded-full"></div>
                ))}
              </div>
            </div>
            <div className={`w-20 h-20 bg-white rounded-lg shadow-lg flex items-center justify-center transition-transform ${isRolling ? 'animate-spin' : ''}`}>
              <div className="grid grid-cols-3 gap-1 p-2">
                {Array.from({length: diceValues[1]}).map((_, i) => (
                  <div key={i} className="w-2 h-2 bg-black rounded-full"></div>
                ))}
              </div>
            </div>
          </div>

          {/* Message de jeu */}
          <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 text-center">
            <p className="text-white text-lg mb-2">{gameMessage}</p>
            {currentPlayer === 0 && !isRolling && (
              <button
                onClick={rollDice}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                🎲 Lancer les dés
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Panneau des joueurs à droite */}
      <div className="w-80 bg-slate-800 p-6 flex flex-col">
        {/* Liste des joueurs */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-4">Joueurs</h3>
          <div className="space-y-3">
            {players.map((player, index) => (
              <div
                key={player.id}
                className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                  index === currentPlayer ? 'bg-purple-700' : 'bg-slate-700'
                }`}
              >
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold border-2 border-white"
                  style={{ backgroundColor: player.color }}
                >
                  {player.avatar}
                </div>
                <div className="flex-1">
                  <div className="text-white font-medium">{player.name}</div>
                  <div className="text-gray-400 text-sm">Case {player.position}</div>
                </div>
                <div className="text-yellow-400 font-bold">{player.money}€</div>
                {index === currentPlayer && (
                  <div className="text-green-400 text-sm">← Tour</div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Informations de la case actuelle */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-4">Case actuelle</h3>
          <div className="bg-slate-700 rounded-lg p-4">
            {(() => {
              const currentCase = BOARD_CASES[players[currentPlayer]?.position || 0]
              return (
                <div className="text-center">
                  <div className="text-4xl mb-2">{currentCase.icon}</div>
                  <div className="text-white font-medium mb-1">{currentCase.name}</div>
                  {currentCase.price > 0 && (
                    <div className="text-yellow-400">{currentCase.price}€</div>
                  )}
                  <div className="text-gray-400 text-sm mt-2 capitalize">{currentCase.type}</div>
                </div>
              )
            })()}
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <button
            onClick={rollDice}
            disabled={currentPlayer !== 0 || isRolling}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 disabled:from-gray-600 disabled:to-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"
          >
            {isRolling ? '🎲 Lancement...' : '🎲 Lancer les dés'}
          </button>

          <button
            onClick={handleBackToLobby}
            className="w-full bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            🚪 Quitter la partie
          </button>
        </div>
      </div>
    </div>
  )
}
