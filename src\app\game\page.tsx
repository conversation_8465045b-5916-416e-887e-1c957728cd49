'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import OptimizedGameBoard from '@/components/game/OptimizedGameBoard'
import OptimizedGameControls from '@/components/game/OptimizedGameControls'
import GameRules, { GameHelpButton, useGameRules } from '@/components/game/GameRules'
import { EventCardModal, useEventCards, applyCardEffect } from '@/components/game/EventCards'
import { BotLogic, createRandomBot, getPersonalityColor } from '@/components/game/BotLogic'
import { SimplePlayer, SimpleBoardCase } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

// Mock data pour la démo avec bots intelligents
const createMockPlayers = (): SimplePlayer[] => [
  { id: '1', name: 'Vous', position: 0, money: 1500, isBot: false, avatar: '👤', color: '#10b981' },
  { id: '2', name: 'Alex_Trader', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('aggressive') },
  { id: '3', name: 'Sophie_CEO', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('conservative') },
  { id: '4', name: 'Marc_Startup', position: 0, money: 1500, isBot: true, avatar: '🤖', color: getPersonalityColor('balanced') }
]

const BOARD_CASES: SimpleBoardCase[] = [
  { id: 0, name: 'DÉPART', type: 'start', icon: '🏁', price: 0 },
  { id: 1, name: 'Startup Tech', type: 'property', price: 60, icon: '💻', color: '#8B4513' },
  { id: 2, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 3, name: 'Blog Influenceur', type: 'property', price: 60, icon: '📱', color: '#8B4513' },
  { id: 4, name: 'Impôts', type: 'tax', price: 200, icon: '💸' },
  { id: 5, name: 'Aéroport', type: 'railroad', price: 200, icon: '✈️' },
  { id: 6, name: 'Café Hipster', type: 'property', price: 100, icon: '☕', color: '#87CEEB' },
  { id: 7, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 8, name: 'Coworking Space', type: 'property', price: 100, icon: '🏢', color: '#87CEEB' },
  { id: 9, name: 'Salle de Sport', type: 'property', price: 120, icon: '💪', color: '#87CEEB' },
  { id: 10, name: 'PRISON', type: 'corner', icon: '🔒', price: 0 },
  { id: 11, name: 'Agence Marketing', type: 'property', price: 140, icon: '📊', color: '#FF69B4' },
  { id: 12, name: 'Électricité', type: 'utility', price: 150, icon: '⚡' },
  { id: 13, name: 'Studio Photo', type: 'property', price: 140, icon: '📸', color: '#FF69B4' },
  { id: 14, name: 'Boutique Mode', type: 'property', price: 160, icon: '👗', color: '#FF69B4' },
  { id: 15, name: 'Gare', type: 'railroad', price: 200, icon: '🚂' },
  { id: 16, name: 'Restaurant', type: 'property', price: 180, icon: '🍽️', color: '#FFA500' },
  { id: 17, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 18, name: 'Hôtel Boutique', type: 'property', price: 180, icon: '🏨', color: '#FFA500' },
  { id: 19, name: 'Spa de Luxe', type: 'property', price: 200, icon: '🧘', color: '#FFA500' },
  { id: 20, name: 'PARKING GRATUIT', type: 'corner', icon: '🅿️', price: 0 },
  { id: 21, name: 'Galerie d\'Art', type: 'property', price: 220, icon: '🎨', color: '#DC143C' },
  { id: 22, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 23, name: 'Théâtre', type: 'property', price: 220, icon: '🎭', color: '#DC143C' },
  { id: 24, name: 'Opéra', type: 'property', price: 240, icon: '🎼', color: '#DC143C' },
  { id: 25, name: 'Port', type: 'railroad', price: 200, icon: '🚢' },
  { id: 26, name: 'Penthouse', type: 'property', price: 260, icon: '🏙️', color: '#FFD700' },
  { id: 27, name: 'Villa Moderne', type: 'property', price: 260, icon: '🏡', color: '#FFD700' },
  { id: 28, name: 'Eau', type: 'utility', price: 150, icon: '💧' },
  { id: 29, name: 'Château', type: 'property', price: 280, icon: '🏰', color: '#FFD700' },
  { id: 30, name: 'ALLER EN PRISON', type: 'corner', icon: '👮', price: 0 },
  { id: 31, name: 'Gratte-Ciel', type: 'property', price: 300, icon: '🏗️', color: '#228B22' },
  { id: 32, name: 'Tour de Bureaux', type: 'property', price: 300, icon: '🏢', color: '#228B22' },
  { id: 33, name: 'Opportunité', type: 'chance', icon: '💡', price: 0 },
  { id: 34, name: 'Centre Commercial', type: 'property', price: 320, icon: '🛍️', color: '#228B22' },
  { id: 35, name: 'Métro', type: 'railroad', price: 200, icon: '🚇' },
  { id: 36, name: 'Dépense Imprévue', type: 'chance', icon: '💳', price: 0 },
  { id: 37, name: 'Yacht Club', type: 'property', price: 350, icon: '⛵', color: '#000080' },
  { id: 38, name: 'Taxe de Luxe', type: 'tax', price: 100, icon: '💎' },
  { id: 39, name: 'Empire Financier', type: 'property', price: 400, icon: '🏦', color: '#000080' }
]

export default function GamePage() {
  const router = useRouter()
  const viewport = useViewport()
  const { isRulesOpen, openRules, closeRules } = useGameRules()
  const { currentCard, isCardOpen, hideCard, drawAndShowCard } = useEventCards()

  // États du jeu
  const [players, setPlayers] = useState<SimplePlayer[]>(createMockPlayers())
  const [currentPlayer, setCurrentPlayer] = useState(0)
  const [diceValues, setDiceValues] = useState<[number, number]>([0, 0])
  const [isRolling, setIsRolling] = useState(false)
  const [gameMessage, setGameMessage] = useState("C'est votre tour ! Lancez les dés.")
  const [ownedProperties, setOwnedProperties] = useState<{ [propertyId: number]: string }>({})
  const [movingPlayers, setMovingPlayers] = useState<Set<string>>(new Set())
  const [botLogics] = useState<{ [playerId: string]: BotLogic }>(() => {
    const bots: { [playerId: string]: BotLogic } = {}
    players.forEach(player => {
      if (player.isBot) {
        bots[player.id] = createRandomBot(player.id)
      }
    })
    return bots
  })

  // Obtenir la case actuelle
  const getCurrentCase = () => BOARD_CASES[players[currentPlayer]?.position || 0]

  // Vérifier si c'est le tour du joueur humain
  const isMyTurn = !players[currentPlayer]?.isBot

  // Lancer les dés
  const rollDice = useCallback(() => {
    if (isRolling) return

    setIsRolling(true)
    setGameMessage("Lancement des dés...")

    // Animation des dés
    setTimeout(() => {
      const dice1 = Math.floor(Math.random() * 6) + 1
      const dice2 = Math.floor(Math.random() * 6) + 1
      setDiceValues([dice1, dice2])

      const total = dice1 + dice2
      movePlayer(total)

      setIsRolling(false)

      const isDouble = dice1 === dice2
      if (isDouble) {
        setGameMessage(`${players[currentPlayer].name} a fait ${total} (double) ! Rejouez !`)
      } else {
        setGameMessage(`${players[currentPlayer].name} a fait ${total} !`)
      }
    }, 1500)
  }, [isRolling, players, currentPlayer])

  // Déplacer un joueur avec animation
  const movePlayer = useCallback((steps: number) => {
    const player = players[currentPlayer]
    const oldPosition = player.position
    const newPosition = (player.position + steps) % 40

    // Démarrer l'animation
    setMovingPlayers(prev => new Set(prev).add(player.id))

    // Animation case par case
    let currentStep = 0
    const animationInterval = setInterval(() => {
      if (currentStep < steps) {
        currentStep++
        setPlayers(prev => {
          const newPlayers = [...prev]
          newPlayers[currentPlayer].position = (oldPosition + currentStep) % 40
          return newPlayers
        })
      } else {
        clearInterval(animationInterval)

        // Finaliser la position et arrêter l'animation
        setPlayers(prev => {
          const newPlayers = [...prev]
          const player = newPlayers[currentPlayer]
          player.position = newPosition

          // Bonus si on passe par la case départ
          if (newPosition < oldPosition) {
            player.money += 200
            setGameMessage(`${player.name} passe par la case DÉPART ! +200€`)
          }

          return newPlayers
        })

        // Arrêter l'animation
        setMovingPlayers(prev => {
          const newSet = new Set(prev)
          newSet.delete(player.id)
          return newSet
        })

        // Traiter la case d'arrivée après un délai
        setTimeout(() => {
          handleCaseAction()
        }, 500)
      }
    }, 400) // 400ms par case pour une animation fluide
  }, [currentPlayer, players])

  // Gérer les actions sur une case
  const handleCaseAction = useCallback(() => {
    const currentCase = getCurrentCase()
    const player = players[currentPlayer]

    switch (currentCase.type) {
      case 'chance':
        // Tirer une carte opportunité
        const card = drawAndShowCard()
        break

      case 'tax':
        // Payer les impôts
        if (currentCase.price) {
          setPlayers(prev => {
            const newPlayers = [...prev]
            newPlayers[currentPlayer].money -= currentCase.price!
            return newPlayers
          })
          setGameMessage(`${player.name} paye ${currentCase.price}€ d'impôts`)
        }
        break

      case 'property':
        // Vérifier si la propriété appartient à quelqu'un
        const owner = ownedProperties[currentCase.id]
        if (owner && owner !== player.id) {
          // Payer un loyer
          const rent = Math.floor((currentCase.price || 0) * 0.1) // 10% du prix
          setPlayers(prev => {
            const newPlayers = [...prev]
            const playerIndex = newPlayers.findIndex(p => p.id === player.id)
            const ownerIndex = newPlayers.findIndex(p => p.id === owner)

            if (playerIndex !== -1 && ownerIndex !== -1) {
              newPlayers[playerIndex].money -= rent
              newPlayers[ownerIndex].money += rent
            }

            return newPlayers
          })

          const ownerName = players.find(p => p.id === owner)?.name || 'Propriétaire'
          setGameMessage(`${player.name} paye ${rent}€ de loyer à ${ownerName}`)
        }
        break

      default:
        setGameMessage(`${player.name} s'arrête sur ${currentCase.name}`)
    }
  }, [currentPlayer, players, ownedProperties, getCurrentCase, drawAndShowCard])

  // Passer au joueur suivant
  const nextTurn = useCallback(() => {
    const isDouble = diceValues[0] === diceValues[1] && diceValues[0] > 0

    if (!isDouble) {
      setTimeout(() => {
        setCurrentPlayer((prev) => (prev + 1) % players.length)
        setDiceValues([0, 0])
        const nextPlayerName = players[(currentPlayer + 1) % players.length]?.name || "Joueur suivant"
        setGameMessage(`Tour de ${nextPlayerName}`)
      }, 1000)
    } else {
      // En cas de double, remettre les dés à zéro pour permettre un nouveau lancer
      setTimeout(() => {
        setDiceValues([0, 0])
        setGameMessage(`${players[currentPlayer]?.name} rejoue (double) !`)
      }, 1000)
    }
  }, [diceValues, players, currentPlayer])

  // Acheter une propriété
  const buyProperty = useCallback(() => {
    const currentCase = getCurrentCase()
    const player = players[currentPlayer]

    if (currentCase.type === 'property' && currentCase.price &&
        player.money >= currentCase.price && !ownedProperties[currentCase.id]) {

      setPlayers(prev => {
        const newPlayers = [...prev]
        newPlayers[currentPlayer].money -= currentCase.price!
        return newPlayers
      })

      setOwnedProperties(prev => ({
        ...prev,
        [currentCase.id]: player.id
      }))

      setGameMessage(`${player.name} achète ${currentCase.name} pour ${currentCase.price}€ !`)
    }
  }, [currentPlayer, players, ownedProperties, getCurrentCase])

  // Terminer le tour
  const endTurn = useCallback(() => {
    nextTurn()
  }, [nextTurn])

  // Logique des bots
  useEffect(() => {
    if (players[currentPlayer]?.isBot && !isRolling) {
      const bot = botLogics[players[currentPlayer].id]
      const currentCase = getCurrentCase()

      if (diceValues[0] === 0) {
        // Le bot doit lancer les dés
        const decision = bot.decideRollDice()
        setTimeout(() => {
          rollDice()
        }, decision.delay)
      } else {
        // Le bot doit prendre une décision sur la case
        const decision = bot.decideTurn(
          players[currentPlayer],
          currentCase,
          players,
          ownedProperties
        )

        setTimeout(() => {
          if (decision.action === 'buy' && decision.data) {
            buyProperty()
          }
          // Les bots terminent automatiquement leur tour
          nextTurn()
        }, decision.delay)
      }
    }
  }, [currentPlayer, players, diceValues, isRolling, botLogics, ownedProperties, rollDice, buyProperty, nextTurn, getCurrentCase])

  // Auto-passer au tour suivant après action sur case pour joueur humain
  useEffect(() => {
    if (!players[currentPlayer]?.isBot && diceValues[0] > 0 && !isRolling) {
      const currentCase = getCurrentCase()

      // Si c'est une case automatique (impôts, chance), passer automatiquement
      if (currentCase.type === 'tax' || currentCase.type === 'chance' || currentCase.type === 'start' || currentCase.type === 'jail') {
        const isDouble = diceValues[0] === diceValues[1]
        if (!isDouble) {
          setTimeout(() => {
            nextTurn()
          }, 3000) // Laisser le temps de lire le message
        }
      }
    }
  }, [currentPlayer, players, diceValues, isRolling, getCurrentCase, nextTurn])

  const handleBackToLobby = () => {
    router.push('/salons')
  }

  const handleCaseClick = (caseId: number) => {
    // Logique pour cliquer sur une case (pour plus tard)
    console.log('Case cliquée:', caseId)
  }

  return (
    <div className={`
      h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900
      ${viewport.isMobile ? 'flex flex-col' : 'flex'}
    `}>

      {/* Header minimal */}
      <header className={`
        ${viewport.isMobile ? 'relative' : 'absolute top-0 left-0 right-0'}
        flex justify-between items-center z-10
        ${viewport.isMobile ? 'p-1 h-12' : 'p-2 h-16'}
        bg-black/20 backdrop-blur-sm
      `}>
        <button
          onClick={handleBackToLobby}
          className="text-white hover:text-purple-300 transition-colors text-sm"
        >
          ← Retour
        </button>
        <span className={`text-white font-bold ${viewport.isMobile ? 'text-sm' : 'text-lg'}`}>
          TheRateRace.io
        </span>
        <div className="w-16"></div> {/* Spacer */}
      </header>

      {/* Layout principal */}
      <div className={`
        flex-1 flex overflow-hidden
        ${viewport.isMobile ? 'flex-col' : viewport.isTablet ? 'flex-col lg:flex-row' : 'flex-row'}
        ${viewport.isMobile ? 'gap-1 p-1' : 'gap-2 p-2'}
        ${viewport.isMobile ? 'pt-0' : 'pt-16'}
      `}>

        {/* Plateau de jeu */}
        <div className={`
          ${viewport.isMobile ? 'flex-1 min-h-0' : viewport.isTablet ? 'flex-1 min-h-0' : 'flex-1'}
          flex items-center justify-center overflow-hidden
        `}>
          <OptimizedGameBoard
            cases={BOARD_CASES}
            players={players}
            currentPlayer={currentPlayer}
            movingPlayers={movingPlayers}
            onCaseClick={handleCaseClick}
            diceValues={diceValues}
            isRolling={isRolling}
          />
        </div>

        {/* Contrôles de jeu */}
        <div className={`
          ${viewport.isMobile ? 'h-48 flex-shrink-0' : viewport.isTablet ? 'h-64 flex-shrink-0' : 'w-80 flex-shrink-0'}
          overflow-hidden
        `}>
          <div className="h-full overflow-y-auto">
            <OptimizedGameControls
              currentPlayer={players[currentPlayer]}
              allPlayers={players}
              currentCase={getCurrentCase()}
              diceValues={diceValues}
              isRolling={isRolling}
              isMyTurn={isMyTurn}
              gameMessage={gameMessage}
              onRollDice={rollDice}
              onBuyProperty={buyProperty}
              onEndTurn={endTurn}
              onQuitGame={handleBackToLobby}
            />
          </div>
        </div>
      </div>

      {/* Bouton d'aide */}
      <GameHelpButton onClick={openRules} />

      {/* Modal des règles */}
      <GameRules isOpen={isRulesOpen} onClose={closeRules} />
    </div>
  )
}
