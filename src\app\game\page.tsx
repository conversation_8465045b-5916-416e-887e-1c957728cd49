'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Dice from '@/components/game/Dice'
import GameBoard from '@/components/game/GameBoard'
import { SimplePlayer, SimpleBoardCase, SimpleGameState } from '@/types/game'

// Plateau de jeu TheRateRace.io (40 cases comme Monopoly)

const BOARD_CASES: SimpleBoardCase[] = [
  { id: 0, name: 'DÉPART', type: 'start', icon: '🏁' },
  { id: 1, name: 'Startup Tech', type: 'property', price: 60, color: '#8B4513', icon: '💻' },
  { id: 2, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 3, name: 'Blog Influenceur', type: 'property', price: 60, color: '#8B4513', icon: '📱' },
  { id: 4, name: 'Impôts', type: 'tax', price: 200, icon: '💸' },
  { id: 5, name: 'Aéroport', type: 'railroad', price: 200, icon: '✈️' },
  { id: 6, name: 'Café Hipster', type: 'property', price: 100, color: '#87CEEB', icon: '☕' },
  { id: 7, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 8, name: 'Coworking Space', type: 'property', price: 100, color: '#87CEEB', icon: '🏢' },
  { id: 9, name: 'Salle de Sport', type: 'property', price: 120, color: '#87CEEB', icon: '💪' },
  { id: 10, name: 'PRISON', type: 'corner', icon: '🔒' },
  { id: 11, name: 'Agence Marketing', type: 'property', price: 140, color: '#FF1493', icon: '📊' },
  { id: 12, name: 'Électricité', type: 'utility', price: 150, icon: '⚡' },
  { id: 13, name: 'Studio Photo', type: 'property', price: 140, color: '#FF1493', icon: '📸' },
  { id: 14, name: 'Boutique Mode', type: 'property', price: 160, color: '#FF1493', icon: '👗' },
  { id: 15, name: 'Gare', type: 'railroad', price: 200, icon: '🚂' },
  { id: 16, name: 'Restaurant Gastronomique', type: 'property', price: 180, color: '#FFA500', icon: '🍽️' },
  { id: 17, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 18, name: 'Hôtel Boutique', type: 'property', price: 180, color: '#FFA500', icon: '🏨' },
  { id: 19, name: 'Spa de Luxe', type: 'property', price: 200, color: '#FFA500', icon: '🧘' },
  { id: 20, name: 'PARKING GRATUIT', type: 'corner', icon: '🅿️' },
  { id: 21, name: 'Galerie d\'Art', type: 'property', price: 220, color: '#FF0000', icon: '🎨' },
  { id: 22, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 23, name: 'Théâtre', type: 'property', price: 220, color: '#FF0000', icon: '🎭' },
  { id: 24, name: 'Opéra', type: 'property', price: 240, color: '#FF0000', icon: '🎼' },
  { id: 25, name: 'Port', type: 'railroad', price: 200, icon: '🚢' },
  { id: 26, name: 'Penthouse', type: 'property', price: 260, color: '#FFFF00', icon: '🏙️' },
  { id: 27, name: 'Villa Moderne', type: 'property', price: 260, color: '#FFFF00', icon: '🏡' },
  { id: 28, name: 'Eau', type: 'utility', price: 150, icon: '💧' },
  { id: 29, name: 'Château', type: 'property', price: 280, color: '#FFFF00', icon: '🏰' },
  { id: 30, name: 'ALLER EN PRISON', type: 'corner', icon: '👮' },
  { id: 31, name: 'Gratte-Ciel', type: 'property', price: 300, color: '#00FF00', icon: '🏗️' },
  { id: 32, name: 'Tour de Bureaux', type: 'property', price: 300, color: '#00FF00', icon: '🏢' },
  { id: 33, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 34, name: 'Centre Commercial', type: 'property', price: 320, color: '#00FF00', icon: '🛍️' },
  { id: 35, name: 'Métro', type: 'railroad', price: 200, icon: '🚇' },
  { id: 36, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 37, name: 'Yacht Club', type: 'property', price: 350, color: '#0000FF', icon: '⛵' },
  { id: 38, name: 'Taxe de Luxe', type: 'tax', price: 100, icon: '💎' },
  { id: 39, name: 'Empire Financier', type: 'property', price: 400, color: '#0000FF', icon: '🏦' }
]

export default function GamePage() {
  const [gameState, setGameState] = useState<SimpleGameState>({
    currentPlayer: 0,
    players: [
      { id: '1', name: 'Vous', position: 0, money: 1500, isBot: false, avatar: '👤', color: '#3b82f6' },
      { id: '2', name: 'Alex_Trader', position: 5, money: 1500, isBot: true, avatar: '🤖', color: '#ef4444' },
      { id: '3', name: 'Sophie_CEO', position: 12, money: 1500, isBot: true, avatar: '🤖', color: '#10b981' },
      { id: '4', name: 'Marc_Startup', position: 8, money: 1500, isBot: true, avatar: '🤖', color: '#f59e0b' }
    ],
    turn: 1,
    phase: 'playing'
  })

  const [diceValue, setDiceValue] = useState<number | null>(null)
  const [isRolling, setIsRolling] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  const rollDice = () => {
    setIsRolling(true)
    setDiceValue(null)

    // Animation du dé
    setTimeout(() => {
      const value = Math.floor(Math.random() * 6) + 1
      setDiceValue(value)
      setIsRolling(false)

      // Déplacer le joueur avec animation
      movePlayer(value)
    }, 1500)
  }

  const movePlayer = (steps: number) => {
    setIsAnimating(true)

    setGameState(prev => {
      const newPlayers = [...prev.players]
      const currentPlayer = newPlayers[prev.currentPlayer]
      const newPosition = (currentPlayer.position + steps) % 40
      currentPlayer.position = newPosition

      // Passer de l'argent si on passe par la case départ
      if (currentPlayer.position < prev.players[prev.currentPlayer].position) {
        currentPlayer.money += 200
      }

      return {
        ...prev,
        players: newPlayers,
        lastDiceRoll: steps
      }
    })

    // Animation terminée, passer au joueur suivant
    setTimeout(() => {
      setIsAnimating(false)
      setDiceValue(null)

      setGameState(prev => ({
        ...prev,
        currentPlayer: (prev.currentPlayer + 1) % prev.players.length
      }))

      // Si c'est un bot, jouer automatiquement après un délai
      setTimeout(() => {
        const nextPlayer = gameState.players[(gameState.currentPlayer + 1) % gameState.players.length]
        if (nextPlayer.isBot) {
          botPlay()
        }
      }, 1000)
    }, 2000)
  }

  const botPlay = () => {
    if (!gameState.players[gameState.currentPlayer].isBot) return

    // Les bots jouent automatiquement après un délai
    setTimeout(() => {
      rollDice()
    }, 2000)
  }

  // Effet pour faire jouer les bots automatiquement
  useEffect(() => {
    const currentPlayer = gameState.players[gameState.currentPlayer]
    if (currentPlayer.isBot && !isRolling && !isAnimating && diceValue === null) {
      botPlay()
    }
  }, [gameState.currentPlayer, isRolling, isAnimating, diceValue])

  const currentPlayer = gameState.players[gameState.currentPlayer]
  const isMyTurn = !currentPlayer.isBot

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            
            {/* Header du Jeu */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-black text-primary mb-2">🎲 TheRateRace.io</h1>
              <p className="text-lg text-muted-foreground">Tour {gameState.turn} • C'est au tour de {currentPlayer.name}</p>
            </div>

            <div className="grid lg:grid-cols-4 gap-6">
              
              {/* Plateau de Jeu */}
              <div className="lg:col-span-3">
                <GameBoard
                  cases={BOARD_CASES}
                  players={gameState.players}
                  currentPlayer={gameState.currentPlayer}
                />


                {/* Contrôles du Jeu */}
                <Card variant="elevated" className="mt-6 p-6">
                  <div className="text-center">
                    {isMyTurn ? (
                      <div className="space-y-6">
                        <div className="text-2xl font-bold text-primary">
                          🎯 C'est votre tour !
                        </div>

                        <div className="flex items-center justify-center gap-8">
                          <Dice
                            value={diceValue}
                            isRolling={isRolling}
                            size="lg"
                          />

                          <div className="space-y-4">
                            <Button
                              onClick={rollDice}
                              disabled={isRolling || diceValue !== null || isAnimating}
                              variant="primary"
                              size="lg"
                              neonGlow
                              className="text-xl px-8 py-4"
                            >
                              {isRolling ? 'Lancement...' :
                               diceValue ? `Vous avez fait ${diceValue}` :
                               'Lancer le Dé'}
                            </Button>

                            {gameState.lastDiceRoll && (
                              <div className="text-sm text-muted-foreground">
                                Dernier lancer : {gameState.lastDiceRoll}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="text-xl text-muted-foreground">
                          🤖 {currentPlayer.name} réfléchit...
                        </div>
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              </div>

              {/* Panneau des Joueurs */}
              <div className="space-y-4">
                <Card variant="elevated">
                  <h3 className="text-xl font-bold mb-4 text-center">👥 Joueurs</h3>
                  
                  <div className="space-y-3">
                    {gameState.players.map((player, index) => (
                      <div 
                        key={player.id}
                        className={`
                          p-3 rounded-lg border transition-all
                          ${index === gameState.currentPlayer 
                            ? 'border-primary bg-primary/10 ring-2 ring-primary/50' 
                            : 'border-border bg-card/50'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-2xl">{player.avatar}</span>
                            <div>
                              <div className="font-semibold text-sm">{player.name}</div>
                              <div className="text-xs text-muted-foreground">
                                Case {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-green-400">
                              {player.money}€
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Actions Rapides */}
                <Card variant="elevated">
                  <h3 className="text-lg font-bold mb-3">⚡ Actions</h3>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full">
                      📊 Statistiques
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      🏠 Propriétés
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      💳 Cartes
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      ⚙️ Options
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
