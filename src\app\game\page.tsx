'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Dice from '@/components/game/Dice'
import ResponsiveGameBoard from '@/components/game/ResponsiveGameBoard'
import GameControls from '@/components/game/GameControls'
import { SimplePlayer, SimpleBoardCase, SimpleGameState } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

// Plateau de jeu TheRateRace.io (40 cases comme Monopoly)

const BOARD_CASES: SimpleBoardCase[] = [
  { id: 0, name: 'DÉPART', type: 'start', icon: '🏁' },
  { id: 1, name: 'Startup Tech', type: 'property', price: 60, color: '#8B4513', icon: '💻' },
  { id: 2, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 3, name: 'Blog Influenceur', type: 'property', price: 60, color: '#8B4513', icon: '📱' },
  { id: 4, name: 'Impôts', type: 'tax', price: 200, icon: '💸' },
  { id: 5, name: 'Aéroport', type: 'railroad', price: 200, icon: '✈️' },
  { id: 6, name: 'Café Hipster', type: 'property', price: 100, color: '#87CEEB', icon: '☕' },
  { id: 7, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 8, name: 'Coworking Space', type: 'property', price: 100, color: '#87CEEB', icon: '🏢' },
  { id: 9, name: 'Salle de Sport', type: 'property', price: 120, color: '#87CEEB', icon: '💪' },
  { id: 10, name: 'PRISON', type: 'corner', icon: '🔒' },
  { id: 11, name: 'Agence Marketing', type: 'property', price: 140, color: '#FF1493', icon: '📊' },
  { id: 12, name: 'Électricité', type: 'utility', price: 150, icon: '⚡' },
  { id: 13, name: 'Studio Photo', type: 'property', price: 140, color: '#FF1493', icon: '📸' },
  { id: 14, name: 'Boutique Mode', type: 'property', price: 160, color: '#FF1493', icon: '👗' },
  { id: 15, name: 'Gare', type: 'railroad', price: 200, icon: '🚂' },
  { id: 16, name: 'Restaurant Gastronomique', type: 'property', price: 180, color: '#FFA500', icon: '🍽️' },
  { id: 17, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 18, name: 'Hôtel Boutique', type: 'property', price: 180, color: '#FFA500', icon: '🏨' },
  { id: 19, name: 'Spa de Luxe', type: 'property', price: 200, color: '#FFA500', icon: '🧘' },
  { id: 20, name: 'PARKING GRATUIT', type: 'corner', icon: '🅿️' },
  { id: 21, name: 'Galerie d\'Art', type: 'property', price: 220, color: '#FF0000', icon: '🎨' },
  { id: 22, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 23, name: 'Théâtre', type: 'property', price: 220, color: '#FF0000', icon: '🎭' },
  { id: 24, name: 'Opéra', type: 'property', price: 240, color: '#FF0000', icon: '🎼' },
  { id: 25, name: 'Port', type: 'railroad', price: 200, icon: '🚢' },
  { id: 26, name: 'Penthouse', type: 'property', price: 260, color: '#FFFF00', icon: '🏙️' },
  { id: 27, name: 'Villa Moderne', type: 'property', price: 260, color: '#FFFF00', icon: '🏡' },
  { id: 28, name: 'Eau', type: 'utility', price: 150, icon: '💧' },
  { id: 29, name: 'Château', type: 'property', price: 280, color: '#FFFF00', icon: '🏰' },
  { id: 30, name: 'ALLER EN PRISON', type: 'corner', icon: '👮' },
  { id: 31, name: 'Gratte-Ciel', type: 'property', price: 300, color: '#00FF00', icon: '🏗️' },
  { id: 32, name: 'Tour de Bureaux', type: 'property', price: 300, color: '#00FF00', icon: '🏢' },
  { id: 33, name: 'Opportunité', type: 'chance', icon: '💡' },
  { id: 34, name: 'Centre Commercial', type: 'property', price: 320, color: '#00FF00', icon: '🛍️' },
  { id: 35, name: 'Métro', type: 'railroad', price: 200, icon: '🚇' },
  { id: 36, name: 'Dépense Imprévue', type: 'chance', icon: '💳' },
  { id: 37, name: 'Yacht Club', type: 'property', price: 350, color: '#0000FF', icon: '⛵' },
  { id: 38, name: 'Taxe de Luxe', type: 'tax', price: 100, icon: '💎' },
  { id: 39, name: 'Empire Financier', type: 'property', price: 400, color: '#0000FF', icon: '🏦' }
]

export default function GamePage() {
  const viewport = useViewport()

  const [gameState, setGameState] = useState<SimpleGameState>({
    currentPlayer: 0,
    players: [
      { id: '1', name: 'Vous', position: 0, money: 1500, isBot: false, avatar: '👤', color: '#3b82f6' },
      { id: '2', name: 'Alex_Trader', position: 5, money: 1500, isBot: true, avatar: '🤖', color: '#ef4444' },
      { id: '3', name: 'Sophie_CEO', position: 12, money: 1500, isBot: true, avatar: '🤖', color: '#10b981' },
      { id: '4', name: 'Marc_Startup', position: 8, money: 1500, isBot: true, avatar: '🤖', color: '#f59e0b' }
    ],
    turn: 1,
    phase: 'playing'
  })

  const [diceValue, setDiceValue] = useState<number | null>(null)
  const [isRolling, setIsRolling] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  const rollDice = () => {
    setIsRolling(true)
    setDiceValue(null)

    // Animation du dé
    setTimeout(() => {
      const value = Math.floor(Math.random() * 6) + 1
      setDiceValue(value)
      setIsRolling(false)

      // Déplacer le joueur avec animation
      movePlayer(value)
    }, 1500)
  }

  const movePlayer = (steps: number) => {
    setIsAnimating(true)

    setGameState(prev => {
      const newPlayers = [...prev.players]
      const currentPlayer = newPlayers[prev.currentPlayer]
      const newPosition = (currentPlayer.position + steps) % 40
      currentPlayer.position = newPosition

      // Passer de l'argent si on passe par la case départ
      if (currentPlayer.position < prev.players[prev.currentPlayer].position) {
        currentPlayer.money += 200
      }

      return {
        ...prev,
        players: newPlayers,
        lastDiceRoll: steps
      }
    })

    // Animation terminée, passer au joueur suivant
    setTimeout(() => {
      setIsAnimating(false)
      setDiceValue(null)

      setGameState(prev => ({
        ...prev,
        currentPlayer: (prev.currentPlayer + 1) % prev.players.length
      }))

      // Si c'est un bot, jouer automatiquement après un délai
      setTimeout(() => {
        const nextPlayer = gameState.players[(gameState.currentPlayer + 1) % gameState.players.length]
        if (nextPlayer.isBot) {
          botPlay()
        }
      }, 1000)
    }, 2000)
  }

  const botPlay = () => {
    if (!gameState.players[gameState.currentPlayer].isBot) return

    // Les bots jouent automatiquement après un délai
    setTimeout(() => {
      rollDice()
    }, 2000)
  }

  // Effet pour faire jouer les bots automatiquement
  useEffect(() => {
    const currentPlayer = gameState.players[gameState.currentPlayer]
    if (currentPlayer.isBot && !isRolling && !isAnimating && diceValue === null) {
      botPlay()
    }
  }, [gameState.currentPlayer, isRolling, isAnimating, diceValue])

  const currentPlayer = gameState.players[gameState.currentPlayer]
  const isMyTurn = !currentPlayer.isBot

  return (
    <Layout fullScreen>
      <div className="h-screen w-screen bg-gradient-to-br from-background via-background to-primary/5 flex flex-col overflow-hidden">

        {/* Header Compact */}
        <div className="flex-shrink-0 bg-card/80 backdrop-blur-sm border-b border-border px-2 md:px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 md:space-x-4">
              <h1 className={`font-black text-primary ${viewport.isMobile ? 'text-lg' : 'text-2xl'}`}>
                🎲 TheRateRace.io
              </h1>
              <div className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
                Tour {gameState.turn} • {currentPlayer.name}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                onClick={() => window.location.href = '/salons'}
                variant="outline"
                size="sm"
                className={viewport.isMobile ? 'text-xs px-2 py-1' : 'text-sm'}
              >
                🏠 Salons
              </Button>
            </div>
          </div>
        </div>

        {/* Contenu Principal */}
        <div className={`flex-1 flex overflow-hidden ${
          viewport.isMobile ? 'flex-col' : 'flex-row'
        }`}>

          {/* Plateau de Jeu */}
          <div className={`${viewport.isMobile ? 'flex-1' : 'flex-1'} flex items-center justify-center`}>
            <ResponsiveGameBoard
              cases={BOARD_CASES}
              players={gameState.players}
              currentPlayer={gameState.currentPlayer}
            />
          </div>

          {/* Panneau de Contrôle */}
          <div className={`${
            viewport.isMobile
              ? 'flex-shrink-0 border-t border-border'
              : 'flex-shrink-0 w-80 border-l border-border'
          }`}>
            <GameControls
              gameState={gameState}
              currentPlayer={currentPlayer}
              diceValue={diceValue}
              isRolling={isRolling}
              isMyTurn={isMyTurn}
              onRollDice={rollDice}
            />
          </div>
        </div>
      </div>
    </Layout>
  )
}
