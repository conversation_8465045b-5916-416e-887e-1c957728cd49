-- TheRateRace.io Game Cards
-- Satirical Chance and Community Chest cards

-- Chance Cards (Opportunités/Risques)
INSERT INTO public.game_cards (type, title, description, action_type, action_data) VALUES
('chance', 'Promotion Surprise', 'Votre patron vous remarque enfin ! Avancez au poste de Directeur Général.', 'move_to', '{"position": 31}'),
('chance', 'Licenciement Économique', 'Restructuration ! Allez directement en prison du burnout.', 'go_to_jail', '{}'),
('chance', 'Héritage Inattendu', 'Votre grand-tante riche vous lègue sa fortune. Recevez 500€.', 'collect', '{"amount": 500}'),
('chance', 'Audit Fiscal', 'Le fisc s''intéresse à vos revenus. Payez 150€.', 'pay', '{"amount": 150}'),
('chance', 'Startup Rachetée', 'Votre startup est rachetée par Google ! Recevez 300€.', 'collect', '{"amount": 300}'),
('chance', 'Burn-out Collectif', 'Toute votre équipe craque. Reculez de 3 cases.', 'move_relative', '{"spaces": -3}'),
('chance', 'Investissement Crypto', 'Vos Bitcoins explosent ! Recevez 200€.', 'collect', '{"amount": 200}'),
('chance', 'Krach Boursier', 'Vos actions s''effondrent. Perdez 250€.', 'pay', '{"amount": 250}'),
('chance', 'Networking Réussi', 'Votre réseau LinkedIn paie ! Avancez jusqu''au prochain emploi corporate.', 'move_to_next_group', '{"group": "pink"}'),
('chance', 'Reconversion Forcée', 'L''IA remplace votre job. Retournez au Départ.', 'move_to', '{"position": 0}'),
('chance', 'Grève Générale', 'Les transports sont bloqués. Restez où vous êtes ce tour.', 'skip_turn', '{}'),
('chance', 'Prime de Performance', 'Vos KPI sont excellents ! Recevez 100€.', 'collect', '{"amount": 100}'),
('chance', 'Démission Spectaculaire', 'Vous quittez votre job en claquant la porte. Avancez de 5 cases.', 'move_relative', '{"spaces": 5}'),
('chance', 'Fusion d''Entreprise', 'Votre boîte fusionne. Échangez de place avec un autre joueur.', 'swap_position', '{}'),
('chance', 'Sortie de Prison', 'Cette carte peut être conservée. Utilisez-la pour sortir de prison.', 'get_out_of_jail', '{"keep": true}'),
('chance', 'Crise de la Quarantaine', 'Vous achetez une voiture de sport. Payez 200€.', 'pay', '{"amount": 200}');

-- Community Chest Cards (Solidarité/Société)
INSERT INTO public.game_cards (type, title, description, action_type, action_data) VALUES
('community_chest', 'Aide de l''État', 'Vous bénéficiez du RSA. Recevez 100€.', 'collect', '{"amount": 100}'),
('community_chest', 'Cotisations Sociales', 'Vos charges sociales augmentent. Payez 50€.', 'pay', '{"amount": 50}'),
('community_chest', 'Crowdfunding Réussi', 'Votre projet participatif cartonne ! Recevez 200€.', 'collect', '{"amount": 200}'),
('community_chest', 'Amende RGPD', 'Vous avez mal protégé les données. Payez 100€.', 'pay', '{"amount": 100}'),
('community_chest', 'Bénévolat Récompensé', 'Votre engagement associatif est reconnu. Recevez 50€.', 'collect', '{"amount": 50}'),
('community_chest', 'Frais Médicaux', 'Votre mutuelle ne couvre pas tout. Payez 75€.', 'pay', '{"amount": 75}'),
('community_chest', 'Concours Gagné', 'Vous remportez un concours de startup. Recevez 150€.', 'collect', '{"amount": 150}'),
('community_chest', 'Taxe Carbone', 'Votre empreinte écologique coûte cher. Payez 80€.', 'pay', '{"amount": 80}'),
('community_chest', 'Formation Professionnelle', 'L''État finance votre reconversion. Recevez 120€.', 'collect', '{"amount": 120}'),
('community_chest', 'Contrôle Urssaf', 'Vos déclarations sont vérifiées. Payez 90€.', 'pay', '{"amount": 90}'),
('community_chest', 'Aide au Logement', 'Vous bénéficiez des APL. Recevez 80€.', 'collect', '{"amount": 80}'),
('community_chest', 'Réparations Locatives', 'Votre propriétaire vous fait payer. Payez 60€.', 'pay', '{"amount": 60}'),
('community_chest', 'Prix d''Excellence', 'Votre travail est récompensé. Recevez 100€.', 'collect', '{"amount": 100}'),
('community_chest', 'Erreur Bancaire', 'La banque s''est trompée en votre faveur. Recevez 200€.', 'collect', '{"amount": 200}'),
('community_chest', 'Sortie de Prison', 'Cette carte peut être conservée. Utilisez-la pour sortir de prison.', 'get_out_of_jail', '{"keep": true}'),
('community_chest', 'Collecte pour un Collègue', 'Chaque joueur vous donne 10€ pour l''anniversaire du boss.', 'collect_from_players', '{"amount": 10}');
