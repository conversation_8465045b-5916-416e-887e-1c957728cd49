// TheRateRace.io Game Types

export interface Profile {
  id: string
  username: string
  display_name?: string
  avatar_url?: string
  total_games: number
  wins: number
  created_at: string
  updated_at: string
}

export interface GameRoom {
  id: string
  name: string
  description?: string
  max_players: number
  current_players: number
  password_hash?: string
  invite_code: string
  status: 'waiting' | 'playing' | 'finished'
  created_by: string
  created_at: string
  started_at?: string
  finished_at?: string
}

export interface GameSession {
  id: string
  room_id: string
  current_turn: number
  current_player_index: number
  game_state: GameState
  status: 'active' | 'paused' | 'finished'
  winner_id?: string
  created_at: string
  updated_at: string
}

export interface RoomPlayer {
  id: string
  room_id: string
  player_id?: string
  player_name: string
  is_bot: boolean
  bot_personality?: 'aggressive' | 'conservative' | 'balanced' | 'random'
  player_index: number
  position: number
  money: number
  properties: Property[]
  cards: GameCard[]
  status: 'active' | 'bankrupt' | 'disconnected'
  joined_at: string
}

export interface GameAction {
  id: string
  session_id: string
  player_id?: string
  player_name: string
  action_type: ActionType
  action_data: any
  turn_number: number
  timestamp: string
}

export interface BoardProperty {
  id: number
  name: string
  type: 'property' | 'utility' | 'railroad' | 'special'
  price?: number
  rent_base?: number
  rent_with_monopoly?: number
  color_group?: string
  description?: string
  position: number
}

export interface GameCard {
  id: string
  type: 'chance' | 'community_chest'
  title: string
  description: string
  action_type: string
  action_data: any
  is_active: boolean
}

export interface Property {
  property_id: number
  owner_id: string
  houses: number
  hotels: number
  mortgaged: boolean
}

export interface GameState {
  board: BoardProperty[]
  players: RoomPlayer[]
  current_player: number
  dice_values?: [number, number]
  last_action?: string
  cards_deck: {
    chance: GameCard[]
    community_chest: GameCard[]
  }
  properties_owned: { [propertyId: number]: string } // propertyId -> playerId
  game_phase: 'setup' | 'playing' | 'finished'
  turn_time_limit?: number
  turn_start_time?: string
}

export type ActionType = 
  | 'roll_dice'
  | 'move'
  | 'buy_property'
  | 'pay_rent'
  | 'draw_card'
  | 'pay_tax'
  | 'go_to_jail'
  | 'get_out_of_jail'
  | 'build_house'
  | 'build_hotel'
  | 'mortgage_property'
  | 'unmortgage_property'
  | 'trade'
  | 'bankruptcy'
  | 'end_turn'

export interface DiceRoll {
  dice1: number
  dice2: number
  total: number
  is_double: boolean
}

export interface CreateRoomData {
  name: string
  description?: string
  max_players: number
  password?: string
}

export interface JoinRoomData {
  room_id: string
  password?: string
}

// Bot personalities and behaviors
export interface BotPersonality {
  name: string
  aggression: number // 0-1, how likely to take risks
  patience: number // 0-1, how long they wait before making decisions
  trading_willingness: number // 0-1, how likely to trade
  building_strategy: 'conservative' | 'aggressive' | 'balanced'
  decision_delay_min: number // minimum seconds before action
  decision_delay_max: number // maximum seconds before action
}

export const BOT_PERSONALITIES: { [key: string]: BotPersonality } = {
  aggressive: {
    name: 'Agressif',
    aggression: 0.8,
    patience: 0.2,
    trading_willingness: 0.7,
    building_strategy: 'aggressive',
    decision_delay_min: 1,
    decision_delay_max: 3
  },
  conservative: {
    name: 'Conservateur',
    aggression: 0.2,
    patience: 0.8,
    trading_willingness: 0.3,
    building_strategy: 'conservative',
    decision_delay_min: 3,
    decision_delay_max: 8
  },
  balanced: {
    name: 'Équilibré',
    aggression: 0.5,
    patience: 0.5,
    trading_willingness: 0.5,
    building_strategy: 'balanced',
    decision_delay_min: 2,
    decision_delay_max: 5
  },
  random: {
    name: 'Imprévisible',
    aggression: Math.random(),
    patience: Math.random(),
    trading_willingness: Math.random(),
    building_strategy: ['conservative', 'aggressive', 'balanced'][Math.floor(Math.random() * 3)] as any,
    decision_delay_min: 1,
    decision_delay_max: 6
  }
}

// Types simplifiés pour le prototype de jeu
export interface SimplePlayer {
  id: string
  name: string
  position: number
  money: number
  isBot: boolean
  avatar: string
  color: string
}

export interface SimpleBoardCase {
  id: number
  name: string
  type: 'start' | 'property' | 'chance' | 'tax' | 'corner' | 'utility' | 'railroad' | 'education' | 'job' | 'housing' | 'transport' | 'life'
  price?: number
  color?: string
  icon: string
}

export interface SimpleGameState {
  currentPlayer: number
  players: SimplePlayer[]
  turn: number
  phase: 'waiting' | 'playing' | 'finished'
  lastDiceRoll?: number
}
