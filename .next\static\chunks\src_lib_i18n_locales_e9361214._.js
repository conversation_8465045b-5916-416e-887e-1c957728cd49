(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/i18n/locales/en/auth.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_en_auth_json_26a34d8f._.js",
  "static/chunks/src_lib_i18n_locales_en_auth_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/auth.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/en/common.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_en_common_json_dde450ff._.js",
  "static/chunks/src_lib_i18n_locales_en_common_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/common.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/en/game.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_en_game_json_ee7ded7c._.js",
  "static/chunks/src_lib_i18n_locales_en_game_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/game.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/auth.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_fr_auth_json_f77395bd._.js",
  "static/chunks/src_lib_i18n_locales_fr_auth_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/auth.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/common.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_fr_common_json_6082ba07._.js",
  "static/chunks/src_lib_i18n_locales_fr_common_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/common.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/game.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_i18n_locales_fr_game_json_483ccae0._.js",
  "static/chunks/src_lib_i18n_locales_fr_game_json_8e0b507e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/game.json (json)");
    });
});
}}),
}]);