import React from 'react'
import { SimplePlayer, SimpleGameState } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Dice from '@/components/game/Dice'

interface GameControlsProps {
  gameState: SimpleGameState
  currentPlayer: SimplePlayer
  diceValue: number | null
  isRolling: boolean
  isMyTurn: boolean
  onRollDice: () => void
}

export default function GameControls({ 
  gameState, 
  currentPlayer, 
  diceValue, 
  isRolling, 
  isMyTurn, 
  onRollDice 
}: GameControlsProps) {
  const viewport = useViewport()

  return (
    <div className={`${
      viewport.isMobile 
        ? 'w-full h-auto max-h-[200px] overflow-y-auto' 
        : 'w-80 h-full overflow-y-auto'
    } space-y-2 md:space-y-4 p-2 md:p-4`}>
      
      {/* Informations du tour */}
      <Card variant="elevated" className="p-3 md:p-4">
        <div className="text-center space-y-2">
          <div className={`font-bold text-primary ${viewport.isMobile ? 'text-sm' : 'text-lg'}`}>
            Tour {gameState.turn}
          </div>
          <div className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
            {isMyTurn ? "🎯 C'est votre tour !" : `Au tour de ${currentPlayer.name}`}
          </div>
        </div>
      </Card>

      {/* Contrôles de jeu */}
      <Card variant="elevated" className="p-3 md:p-4">
        <div className="text-center space-y-3 md:space-y-4">
          
          {/* Dé */}
          <div className="flex justify-center">
            <Dice 
              value={diceValue} 
              isRolling={isRolling} 
              size={viewport.isMobile ? "sm" : "md"}
            />
          </div>
          
          {/* Bouton lancer */}
          {isMyTurn && (
            <Button
              onClick={onRollDice}
              disabled={isRolling}
              variant="primary"
              size={viewport.isMobile ? "sm" : "md"}
              neonGlow
              className={`w-full font-bold ${viewport.isMobile ? 'text-sm py-2' : 'text-lg py-3'}`}
            >
              {isRolling ? '🎲 Lancement...' : '🎲 Lancer le dé'}
            </Button>
          )}
          
          {/* Message pour les bots */}
          {!isMyTurn && currentPlayer.isBot && (
            <div className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
              🤖 {currentPlayer.name} réfléchit...
            </div>
          )}
        </div>
      </Card>

      {/* Liste des joueurs */}
      <Card variant="elevated" className="p-3 md:p-4">
        <h3 className={`font-bold mb-2 md:mb-3 ${viewport.isMobile ? 'text-sm' : 'text-base'}`}>
          Joueurs
        </h3>
        <div className="space-y-2">
          {gameState.players.map((player, index) => (
            <div
              key={player.id}
              className={`flex items-center justify-between p-2 rounded-lg transition-colors ${
                index === gameState.currentPlayer 
                  ? 'bg-primary/20 border border-primary/30' 
                  : 'bg-muted/50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <div
                  className={`${viewport.isMobile ? 'w-6 h-6 text-sm' : 'w-8 h-8 text-base'} rounded-full flex items-center justify-center font-bold text-white`}
                  style={{ backgroundColor: player.color }}
                >
                  {player.avatar}
                </div>
                <div>
                  <div className={`font-semibold ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
                    {player.name}
                  </div>
                  <div className={`text-muted-foreground ${viewport.isMobile ? 'text-[10px]' : 'text-xs'}`}>
                    Case {player.position}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className={`font-bold text-secondary ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
                  {player.money}€
                </div>
                {index === gameState.currentPlayer && (
                  <div className={`text-primary ${viewport.isMobile ? 'text-[8px]' : 'text-xs'}`}>
                    ▶ En cours
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Actions rapides - Desktop seulement */}
      {viewport.isDesktop && (
        <Card variant="elevated" className="p-4">
          <h3 className="font-bold mb-3 text-base">Actions</h3>
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-sm"
              onClick={() => window.location.href = '/salons'}
            >
              🏠 Retour aux salons
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full text-sm"
              onClick={() => window.location.reload()}
            >
              🔄 Nouvelle partie
            </Button>
          </div>
        </Card>
      )}

      {/* Statistiques rapides - Tablet/Desktop */}
      {!viewport.isMobile && (
        <Card variant="glass" className="p-4">
          <h3 className="font-bold mb-3 text-sm">Statistiques</h3>
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Tours joués:</span>
              <span className="text-foreground">{gameState.turn}</span>
            </div>
            <div className="flex justify-between">
              <span>Joueurs actifs:</span>
              <span className="text-foreground">{gameState.players.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Bots:</span>
              <span className="text-foreground">
                {gameState.players.filter(p => p.isBot).length}
              </span>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
