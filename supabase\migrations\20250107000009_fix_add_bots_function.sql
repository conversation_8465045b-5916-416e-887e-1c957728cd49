-- Migration pour corriger l'ambiguïté dans la fonction add_bots_to_room

-- Supprimer l'ancienne fonction
DROP FUNCTION IF EXISTS add_bots_to_room(UUID);

-- Recréer la fonction avec des noms de variables non ambigus
CREATE OR REPLACE FUNCTION add_bots_to_room(target_room_id UUID)
RETURNS INTEGER AS $$
DECLARE
    room_record RECORD;
    bots_to_add INTEGER;
    bot_names TEXT[] := ARRAY[
        'Alex_Trader', 'Sophie_CEO', '<PERSON>_Startup', 'Julie_Finance', 
        'Thomas_Tech', 'Emma_Marketing', '<PERSON>_Consultant', 'Chloe_Manager',
        '<PERSON>_Investor', 'Lea_Executive', '<PERSON><PERSON>_Entrepreneur', 'Clara_Director'
    ];
    bot_personalities TEXT[] := ARRAY['aggressive', 'conservative', 'balanced', 'random'];
    i INTEGER;
    bot_name TEXT;
    bot_personality TEXT;
    next_index INTEGER;
BEGIN
    -- Get room info
    SELECT * INTO room_record FROM public.game_rooms WHERE id = target_room_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room not found';
    END IF;
    
    -- Calculate how many bots to add
    bots_to_add := room_record.max_players - room_record.current_players;
    
    IF bots_to_add <= 0 THEN
        RETURN 0;
    END IF;
    
    -- Add bots
    FOR i IN 1..bots_to_add LOOP
        -- Get next available player index
        SELECT COALESCE(MAX(player_index), -1) + 1 INTO next_index
        FROM public.room_players 
        WHERE room_players.room_id = target_room_id;
        
        -- Select random bot name and personality
        bot_name := bot_names[1 + floor(random() * array_length(bot_names, 1))::INTEGER];
        bot_personality := bot_personalities[1 + floor(random() * array_length(bot_personalities, 1))::INTEGER];
        
        -- Ensure unique bot name in this room
        WHILE EXISTS (SELECT 1 FROM public.room_players WHERE room_players.room_id = target_room_id AND player_name = bot_name) LOOP
            bot_name := bot_names[1 + floor(random() * array_length(bot_names, 1))::INTEGER] || '_' || floor(random() * 1000)::TEXT;
        END LOOP;
        
        -- Add bot
        INSERT INTO public.room_players (room_id, player_name, is_bot, bot_personality, player_index)
        VALUES (target_room_id, bot_name, TRUE, bot_personality, next_index);
    END LOOP;
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = room_record.max_players 
    WHERE id = target_room_id;
    
    RETURN bots_to_add;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
