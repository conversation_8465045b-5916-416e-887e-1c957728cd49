-- Migration pour permettre la création de salons anonymes (pour les tests locaux)
-- Cette migration est temporaire et ne doit PAS être utilisée en production

-- Supprimer l'ancienne politique restrictive
DROP POLICY IF EXISTS "Users can create game rooms" ON public.game_rooms;

-- Créer une nouvelle politique qui permet les insertions anonymes pour les tests
CREATE POLICY "Allow anonymous room creation for testing" ON public.game_rooms
    FOR INSERT WITH CHECK (true);

-- Permettre aussi la mise à jour pour les tests
DROP POLICY IF EXISTS "Room creators can update their rooms" ON public.game_rooms;

CREATE POLICY "Allow room updates for testing" ON public.game_rooms
    FOR UPDATE USING (true);

-- Permettre les insertions anonymes dans room_players aussi
CREATE POLICY "Allow anonymous players for testing" ON public.room_players
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Room players are viewable by everyone" ON public.room_players
    FOR SELECT USING (true);

-- Permettre les mises à jour des joueurs
CREATE POLICY "Allow player updates for testing" ON public.room_players
    FOR UPDATE USING (true);

-- Permettre les suppressions pour les tests
CREATE POLICY "Allow player deletions for testing" ON public.room_players
    FOR DELETE USING (true);

-- Commentaire de sécurité
COMMENT ON POLICY "Allow anonymous room creation for testing" ON public.game_rooms 
IS 'ATTENTION: Cette politique permet les insertions anonymes. À supprimer en production !';
