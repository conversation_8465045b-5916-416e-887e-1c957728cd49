'use client'

import Layout from '@/components/layout/Layout'
import But<PERSON> from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { useI18n } from '@/lib/i18n/hooks'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

export default function Home() {
  const { t } = useI18n('common')
  const router = useRouter()
  const [isStarting, setIsStarting] = useState(false)
  const viewport = useViewport()

  const handleQuickPlay = async () => {
    setIsStarting(true)
    router.push('/salons')
  }

  return (
    <Layout>
      <div className="h-full flex flex-col items-center justify-center text-center px-2 md:px-4">
        <div className="w-full max-w-4xl mx-auto flex flex-col justify-center h-full space-y-4 md:space-y-8">
          {/* Hero Section Adaptatif */}
          <div className="space-y-3 md:space-y-6">
            <div className="space-y-2 md:space-y-4">
              <h1 className={`font-black bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent animate-pulse ${
                viewport.isMobile ? 'text-4xl' : viewport.isTablet ? 'text-6xl' : 'text-8xl'
              }`}>
                TheRateRace
              </h1>
              <div className={`font-bold text-primary animate-bounce ${
                viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-3xl'
              }`}>
                .io
              </div>
            </div>

            <p className={`text-muted-foreground max-w-3xl mx-auto leading-relaxed font-light ${
              viewport.isMobile ? 'text-sm' : viewport.isTablet ? 'text-lg' : 'text-2xl'
            }`}>
              Le jeu de plateau satirique qui parodie la course à la réussite financière moderne
            </p>
          </div>

          {/* Action Principale Responsive */}
          <div className="space-y-3 md:space-y-6">
            <Button
              onClick={handleQuickPlay}
              variant="primary"
              size={viewport.isMobile ? "lg" : "xl"}
              neonGlow
              disabled={isStarting}
              className={`font-bold transform hover:scale-105 transition-all duration-300 shadow-2xl ${
                viewport.isMobile
                  ? 'text-lg px-8 py-4 min-w-[250px]'
                  : 'text-3xl px-16 py-8 min-w-[300px]'
              }`}
            >
              {isStarting ? '🎮 Démarrage...' : '🎲 JOUER MAINTENANT'}
            </Button>

            <p className={`text-muted-foreground ${
              viewport.isMobile ? 'text-sm' : 'text-lg'
            }`}>
              Un seul clic pour commencer • Aucune inscription requise
            </p>

            <div className="flex gap-2 md:gap-4 justify-center">
              <Button
                onClick={() => router.push('/game')}
                variant="outline"
                size={viewport.isMobile ? "sm" : "md"}
                className={viewport.isMobile ? 'text-sm px-4 py-2' : 'text-lg px-6 py-3'}
              >
                🎮 Démo du Jeu
              </Button>
            </div>
          </div>

          {/* Fonctionnalités - Masquées sur mobile pour économiser l'espace */}
          {!viewport.isMobile && (
            <div className={`grid gap-4 ${
              viewport.isTablet ? 'grid-cols-1 space-y-2' : 'grid-cols-3'
            }`}>
              <Card variant="glass" hover className="text-center">
                <div className={`mb-3 ${viewport.isTablet ? 'text-4xl' : 'text-6xl'}`}>💰</div>
                <h3 className={`font-bold mb-2 text-primary ${viewport.isTablet ? 'text-lg' : 'text-xl'}`}>Satirique</h3>
                <p className={`text-muted-foreground ${viewport.isTablet ? 'text-sm' : 'text-base'}`}>
                  Parodie hilarante du capitalisme moderne
                </p>
              </Card>

              <Card variant="glass" hover className="text-center">
                <div className={`mb-3 ${viewport.isTablet ? 'text-4xl' : 'text-6xl'}`}>🤖</div>
                <h3 className={`font-bold mb-2 text-primary ${viewport.isTablet ? 'text-lg' : 'text-xl'}`}>IA Avancée</h3>
                <p className={`text-muted-foreground ${viewport.isTablet ? 'text-sm' : 'text-base'}`}>
                  Bots intelligents avec personnalités uniques
                </p>
              </Card>

              <Card variant="glass" hover className="text-center">
                <div className={`mb-3 ${viewport.isTablet ? 'text-4xl' : 'text-6xl'}`}>⚡</div>
                <h3 className={`font-bold mb-2 text-primary ${viewport.isTablet ? 'text-lg' : 'text-xl'}`}>Instantané</h3>
                <p className={`text-muted-foreground ${viewport.isTablet ? 'text-sm' : 'text-base'}`}>
                  Parties rapides en temps réel
                </p>
              </Card>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}
