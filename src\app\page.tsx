'use client'

import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
import { useI18n } from '@/lib/i18n/hooks'

export default function Home() {
  const { t } = useI18n('common')

  return (
    <Layout>
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center space-y-8 mb-16">
          <h1 className="text-6xl font-bold text-primary neon-text">
            {t('appName')}
          </h1>
          <p className="text-xl text-muted max-w-2xl mx-auto">
            {t('description')}
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" neonGlow>
              {t('buttons.play')}
            </Button>
            <Button variant="outline" size="lg">
              Voir les règles
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-card p-6 rounded-lg border border-border">
            <div className="text-accent text-2xl mb-4">🎲</div>
            <h3 className="text-lg font-semibold mb-2">Multijoueur en temps réel</h3>
            <p className="text-muted">Jusqu'à 12 joueurs par partie avec synchronisation en temps réel</p>
          </div>
          <div className="bg-card p-6 rounded-lg border border-border">
            <div className="text-secondary text-2xl mb-4">💰</div>
            <h3 className="text-lg font-semibold mb-2">Satire financière</h3>
            <p className="text-muted">Parodie hilarante de la course à la réussite moderne</p>
          </div>
          <div className="bg-card p-6 rounded-lg border border-border">
            <div className="text-primary text-2xl mb-4">🏆</div>
            <h3 className="text-lg font-semibold mb-2">Cosmétiques</h3>
            <p className="text-muted">Personnalisez vos pions, dés et thèmes de plateau</p>
          </div>
        </div>

        {/* Status */}
        <div className="text-center bg-card p-8 rounded-lg border border-border">
          <h2 className="text-2xl font-bold mb-4">🚧 En développement</h2>
          <p className="text-muted mb-4">
            TheRateRace.io est actuellement en phase de développement.
            Nous travaillons dur pour vous offrir la meilleure expérience de jeu !
          </p>
          <div className="text-sm text-accent">
            Sprint 1 : Configuration initiale ✅
          </div>
        </div>
      </div>
    </Layout>
  )
}
