'use client'

import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { useI18n } from '@/lib/i18n/hooks'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export default function Home() {
  const { t } = useI18n('common')
  const router = useRouter()
  const [isStarting, setIsStarting] = useState(false)

  const handleQuickPlay = async () => {
    setIsStarting(true)
    // Redirection directe vers les salons pour une expérience simplifiée
    router.push('/salons')
  }

  return (
    <Layout>
      <div className="min-h-screen flex flex-col items-center justify-center text-center px-4">
        <div className="max-w-4xl mx-auto space-y-12">
          {/* Hero Section Ultra-Simplifié */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-7xl md:text-9xl font-black bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent animate-pulse">
                TheRateRace
              </h1>
              <div className="text-2xl md:text-3xl font-bold text-primary animate-bounce">
                .io
              </div>
            </div>

            <p className="text-2xl md:text-3xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-light">
              Le jeu de plateau satirique qui parodie la course à la réussite financière moderne
            </p>
          </div>

          {/* Bouton Principal Ultra-Visible */}
          <div className="space-y-6">
            <Button
              onClick={handleQuickPlay}
              variant="primary"
              size="lg"
              neonGlow
              disabled={isStarting}
              className="text-3xl px-16 py-8 min-w-[300px] font-bold transform hover:scale-105 transition-all duration-300 shadow-2xl"
            >
              {isStarting ? '🎮 Démarrage...' : '🎲 JOUER MAINTENANT'}
            </Button>

            <p className="text-lg text-muted-foreground">
              Un seul clic pour commencer • Aucune inscription requise
            </p>

            <div className="flex gap-4 justify-center mt-4">
              <Button
                onClick={() => router.push('/game')}
                variant="outline"
                size="md"
                className="text-lg px-6 py-3"
              >
                🎮 Démo du Jeu
              </Button>
            </div>
          </div>

          {/* Aperçu Rapide des Fonctionnalités */}
          <div className="grid md:grid-cols-3 gap-8 mt-20">
            <Card variant="glass" hover className="text-center">
              <div className="text-6xl mb-6">💰</div>
              <h3 className="text-xl font-bold mb-3 text-primary">Satirique</h3>
              <p className="text-muted-foreground">
                Parodie hilarante du capitalisme moderne
              </p>
            </Card>

            <Card variant="glass" hover className="text-center">
              <div className="text-6xl mb-6">🤖</div>
              <h3 className="text-xl font-bold mb-3 text-primary">IA Avancée</h3>
              <p className="text-muted-foreground">
                Bots intelligents avec personnalités uniques
              </p>
            </Card>

            <Card variant="glass" hover className="text-center">
              <div className="text-6xl mb-6">⚡</div>
              <h3 className="text-xl font-bold mb-3 text-primary">Instantané</h3>
              <p className="text-muted-foreground">
                Parties rapides en temps réel
              </p>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  )
}
