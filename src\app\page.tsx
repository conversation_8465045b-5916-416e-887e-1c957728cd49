'use client'

import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

export default function Home() {
  const router = useRouter()
  const [nickname, setNickname] = useState('')
  const [isStarting, setIsStarting] = useState(false)
  const viewport = useViewport()

  const handlePlay = async () => {
    if (!nickname.trim()) return
    setIsStarting(true)
    // Stocker le pseudo dans le localStorage
    localStorage.setItem('playerNickname', nickname.trim())
    router.push('/salons')
  }

  const handleAllRooms = () => {
    router.push('/salons')
  }

  const handleCreatePrivateGame = () => {
    router.push('/salons?create=private')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col">
      {/* Header minimal */}
      <header className="flex justify-between items-center p-4 md:p-6">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
            <span className="text-slate-900 font-bold text-lg">🎲</span>
          </div>
          <span className="text-white font-bold text-xl">TheRateRace.io</span>
        </div>
        <div className="flex items-center gap-4">
          <button className="text-gray-300 hover:text-white transition-colors">
            🛒 Store
          </button>
          <button className="text-gray-300 hover:text-white transition-colors">
            👤 Login
          </button>
        </div>
      </header>

      {/* Contenu principal centré */}
      <main className="flex-1 flex items-center justify-center px-4">
        <div className="text-center space-y-8 max-w-md w-full">
          {/* Dé 3D */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="w-24 h-24 md:w-32 md:h-32 bg-white rounded-xl shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-300">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="grid grid-cols-3 gap-1 p-4">
                    <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    <div></div>
                    <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    <div></div>
                    <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    <div></div>
                    <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    <div></div>
                    <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Titre */}
          <div className="space-y-2">
            <h1 className="text-5xl md:text-6xl font-black text-white">
              THERATERATE<span className="text-purple-400">.io</span>
            </h1>
            <p className="text-gray-300 text-lg">Dominez l'économie</p>
          </div>

          {/* Champ de pseudo */}
          <div className="space-y-4">
            <input
              type="text"
              placeholder="Votre pseudo..."
              value={nickname}
              onChange={(e) => setNickname(e.target.value)}
              className="w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
              maxLength={20}
              onKeyPress={(e) => e.key === 'Enter' && handlePlay()}
            />
          </div>

          {/* Bouton Play principal */}
          <button
            onClick={handlePlay}
            disabled={!nickname.trim() || isStarting}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 disabled:from-gray-600 disabled:to-gray-700 text-white font-bold py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg"
          >
            <span className="flex items-center justify-center gap-2">
              ▶️ {isStarting ? 'Démarrage...' : 'Play'}
            </span>
          </button>

          {/* Boutons secondaires */}
          <div className="flex gap-3">
            <button
              onClick={handleAllRooms}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              👥 All rooms
            </button>
            <button
              onClick={handleCreatePrivateGame}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              🔒 Create a private game
            </button>
          </div>
        </div>
      </main>

      {/* Footer minimal avec éléments décoratifs */}
      <footer className="p-4 flex justify-center">
        <div className="flex gap-4 opacity-50">
          <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-2xl">
            🏆
          </div>
          <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-2xl">
            💎
          </div>
          <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-2xl">
            💰
          </div>
        </div>
      </footer>
    </div>
  )
}
