'use client'

import { useState, useEffect } from 'react'
import { Player } from '@/types/game'

interface AnimatedPawnProps {
  player: Player
  isCurrentPlayer: boolean
  isMoving: boolean
  position: { x: number; y: number }
  size?: 'sm' | 'md' | 'lg'
}

export default function AnimatedPawn({ 
  player, 
  isCurrentPlayer, 
  isMoving, 
  position,
  size = 'md' 
}: AnimatedPawnProps) {
  const [isJumping, setIsJumping] = useState(false)
  const [jumpCount, setJumpCount] = useState(0)

  const sizeClasses = {
    sm: 'w-4 h-4 text-xs',
    md: 'w-5 h-5 text-sm',
    lg: 'w-6 h-6 text-base'
  }

  // Animation de sautillement lors du déplacement
  useEffect(() => {
    if (isMoving) {
      setIsJumping(true)
      setJumpCount(0)
      
      // Série de sautillements
      const jumpInterval = setInterval(() => {
        setJumpCount(prev => {
          if (prev >= 3) {
            clearInterval(jumpInterval)
            setIsJumping(false)
            return 0
          }
          return prev + 1
        })
      }, 200)

      return () => clearInterval(jumpInterval)
    }
  }, [isMoving])

  return (
    <div
      className={`
        ${sizeClasses[size]} 
        rounded-full border-2 border-white shadow-lg
        flex items-center justify-center font-bold
        transform transition-all duration-300 cursor-pointer
        ${isCurrentPlayer ? 'ring-2 ring-primary ring-offset-1 scale-110 z-20' : 'z-10'}
        ${isJumping ? 'animate-bounce' : ''}
        hover:scale-125 hover:shadow-xl hover:z-30
        relative
      `}
      style={{ 
        backgroundColor: player.color,
        left: position.x,
        top: position.y,
        transform: `
          translate(${position.x}px, ${position.y}px) 
          ${isCurrentPlayer ? 'scale(1.1)' : 'scale(1)'}
          ${isJumping ? `translateY(-${jumpCount * 2}px)` : ''}
        `
      }}
      title={`${player.name} - ${player.money}€`}
    >
      {/* Avatar du joueur */}
      <span className="drop-shadow-sm filter">
        {player.avatar === '👤' ? '👤' : '🤖'}
      </span>
      
      {/* Effet de brillance pour le joueur actuel */}
      {isCurrentPlayer && (
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/30 to-secondary/30 animate-pulse" />
      )}
      
      {/* Particules lors du mouvement */}
      {isMoving && (
        <>
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-accent rounded-full animate-ping opacity-75" />
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-secondary rounded-full animate-ping opacity-75 animation-delay-100" />
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-primary rounded-full animate-ping opacity-75 animation-delay-200" />
        </>
      )}
      
      {/* Ombre portée */}
      <div 
        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-4 h-1 bg-black/20 rounded-full blur-sm"
        style={{
          transform: `translateX(-50%) ${isJumping ? 'scale(0.8)' : 'scale(1)'}`
        }}
      />
    </div>
  )
}

// Composant pour gérer le déplacement animé d'un pion
interface MovingPawnProps {
  player: Player
  fromPosition: number
  toPosition: number
  onMoveComplete: () => void
  boardCases: any[]
}

export function MovingPawn({ 
  player, 
  fromPosition, 
  toPosition, 
  onMoveComplete,
  boardCases 
}: MovingPawnProps) {
  const [currentStep, setCurrentStep] = useState(fromPosition)
  const [isAnimating, setIsAnimating] = useState(false)

  // Calculer le chemin de déplacement
  const getMovementPath = () => {
    const steps = []
    let current = fromPosition
    const distance = toPosition > fromPosition ? 
      toPosition - fromPosition : 
      (40 - fromPosition) + toPosition

    for (let i = 0; i <= distance; i++) {
      steps.push((fromPosition + i) % 40)
    }
    return steps
  }

  // Animation de déplacement case par case
  useEffect(() => {
    const path = getMovementPath()
    let stepIndex = 0
    setIsAnimating(true)

    const moveInterval = setInterval(() => {
      if (stepIndex < path.length - 1) {
        stepIndex++
        setCurrentStep(path[stepIndex])
      } else {
        clearInterval(moveInterval)
        setIsAnimating(false)
        onMoveComplete()
      }
    }, 300) // 300ms par case

    return () => clearInterval(moveInterval)
  }, [fromPosition, toPosition])

  // Calculer la position visuelle de la case
  const getCasePosition = (caseId: number) => {
    // Cette fonction devrait calculer la position x,y de la case sur le plateau
    // Pour l'instant, retournons une position par défaut
    const angle = (caseId / 40) * 2 * Math.PI
    const radius = 200
    return {
      x: Math.cos(angle) * radius + 300,
      y: Math.sin(angle) * radius + 300
    }
  }

  const position = getCasePosition(currentStep)

  return (
    <AnimatedPawn
      player={player}
      isCurrentPlayer={true}
      isMoving={isAnimating}
      position={position}
      size="lg"
    />
  )
}

// Hook pour gérer les animations de déplacement
export function usePlayerMovement() {
  const [movingPlayers, setMovingPlayers] = useState<Map<string, {
    from: number
    to: number
    player: Player
  }>>(new Map())

  const startPlayerMovement = (player: Player, fromPosition: number, toPosition: number) => {
    setMovingPlayers(prev => new Map(prev).set(player.id, {
      from: fromPosition,
      to: toPosition,
      player
    }))
  }

  const completePlayerMovement = (playerId: string) => {
    setMovingPlayers(prev => {
      const newMap = new Map(prev)
      newMap.delete(playerId)
      return newMap
    })
  }

  return {
    movingPlayers,
    startPlayerMovement,
    completePlayerMovement
  }
}
