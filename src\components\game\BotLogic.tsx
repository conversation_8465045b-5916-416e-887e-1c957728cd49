import { SimplePlayer, SimpleBoardCase } from '@/types/game'

export interface BotDecision {
  action: 'roll' | 'buy' | 'pass' | 'trade' | 'build'
  data?: any
  delay: number // délai en ms avant d'exécuter l'action
}

export interface BotPersonality {
  name: string
  aggression: number // 0-1, probabilité d'acheter des propriétés
  patience: number // 0-1, délai de réflexion
  riskTaking: number // 0-1, probabilité de prendre des risques
  buildingStrategy: 'conservative' | 'aggressive' | 'balanced'
}

export const BOT_PERSONALITIES: { [key: string]: BotPersonality } = {
  aggressive: {
    name: 'Agressif',
    aggression: 0.9,
    patience: 0.1,
    riskTaking: 0.8,
    buildingStrategy: 'aggressive'
  },
  conservative: {
    name: 'Conservateur',
    aggression: 0.3,
    patience: 0.9,
    riskTaking: 0.2,
    buildingStrategy: 'conservative'
  },
  balanced: {
    name: 'Équilibré',
    aggression: 0.6,
    patience: 0.5,
    riskTaking: 0.5,
    buildingStrategy: 'balanced'
  },
  random: {
    name: 'Imprévisible',
    aggression: Math.random(),
    patience: Math.random(),
    riskTaking: Math.random(),
    buildingStrategy: ['conservative', 'aggressive', 'balanced'][Math.floor(Math.random() * 3)] as any
  }
}

export class BotLogic {
  private personality: BotPersonality
  private playerId: string

  constructor(playerId: string, personalityType: keyof typeof BOT_PERSONALITIES = 'balanced') {
    this.playerId = playerId
    this.personality = BOT_PERSONALITIES[personalityType]
  }

  // Décision principale du bot pour son tour
  decideTurn(
    player: SimplePlayer,
    currentCase: SimpleBoardCase,
    allPlayers: SimplePlayer[],
    ownedProperties: { [propertyId: number]: string }
  ): BotDecision {
    // Délai de réflexion basé sur la patience
    const baseDelay = 1000 + (this.personality.patience * 3000)
    const delay = baseDelay + (Math.random() * 1000)

    // Si c'est une propriété disponible
    if (currentCase.type === 'property' && currentCase.price && !ownedProperties[currentCase.id]) {
      return this.decideBuyProperty(player, currentCase, delay)
    }

    // Si c'est une case spéciale
    if (currentCase.type === 'chance' || currentCase.type === 'tax') {
      return {
        action: 'pass',
        delay: delay * 0.5 // Moins de réflexion pour les cases automatiques
      }
    }

    // Action par défaut
    return {
      action: 'pass',
      delay
    }
  }

  // Décision d'achat de propriété
  private decideBuyProperty(player: SimplePlayer, property: SimpleBoardCase, delay: number): BotDecision {
    if (!property.price) {
      return { action: 'pass', delay }
    }

    // Vérifier si le bot a assez d'argent
    if (player.money < property.price) {
      return { action: 'pass', delay }
    }

    // Calculer la probabilité d'achat basée sur plusieurs facteurs
    let buyProbability = this.personality.aggression

    // Ajuster selon le ratio prix/argent disponible
    const priceRatio = property.price / player.money
    if (priceRatio > 0.8) {
      buyProbability *= 0.3 // Très cher, réduire la probabilité
    } else if (priceRatio > 0.5) {
      buyProbability *= 0.7 // Cher, réduire un peu
    } else if (priceRatio < 0.2) {
      buyProbability *= 1.3 // Bon marché, augmenter
    }

    // Ajuster selon la stratégie
    if (this.personality.buildingStrategy === 'aggressive') {
      buyProbability *= 1.2
    } else if (this.personality.buildingStrategy === 'conservative') {
      buyProbability *= 0.8
    }

    // Ajouter un facteur de risque
    buyProbability *= (0.5 + this.personality.riskTaking * 0.5)

    // Décision finale
    const shouldBuy = Math.random() < Math.min(buyProbability, 0.95)

    return {
      action: shouldBuy ? 'buy' : 'pass',
      data: shouldBuy ? { propertyId: property.id, price: property.price } : undefined,
      delay
    }
  }

  // Décision de lancer les dés (toujours oui, mais avec délai)
  decideRollDice(): BotDecision {
    const delay = 500 + (this.personality.patience * 2000) + (Math.random() * 1000)
    
    return {
      action: 'roll',
      delay
    }
  }

  // Obtenir un message de réaction du bot
  getReactionMessage(action: string, success: boolean): string {
    const reactions = {
      buy_success: [
        "Excellent investissement !",
        "Cette propriété sera rentable.",
        "Parfait pour mon portefeuille.",
        "Un achat stratégique."
      ],
      buy_fail: [
        "Trop cher pour moi.",
        "Je passe mon tour.",
        "Pas intéressé par cette propriété.",
        "Je garde mon argent."
      ],
      roll: [
        "À mon tour !",
        "Voyons ce que donnent les dés...",
        "Allez, un bon score !",
        "C'est parti !"
      ],
      bankrupt: [
        "Je suis ruiné...",
        "Plus d'argent !",
        "C'est la fin pour moi.",
        "Je déclare forfait."
      ]
    }

    const key = success ? `${action}_success` : `${action}_fail`
    const messages = reactions[key as keyof typeof reactions] || reactions.roll
    
    return messages[Math.floor(Math.random() * messages.length)]
  }

  // Calculer la valeur d'une propriété pour le bot
  calculatePropertyValue(property: SimpleBoardCase, ownedProperties: { [propertyId: number]: string }): number {
    if (!property.price) return 0

    let value = property.price

    // Bonus pour les propriétés de même couleur (monopole potentiel)
    if (property.color) {
      const sameColorOwned = Object.keys(ownedProperties).filter(id => {
        // Cette logique devrait être améliorée avec les vraies données du plateau
        return false // Placeholder
      }).length
      
      value += sameColorOwned * 50
    }

    // Ajuster selon la position (propriétés plus visitées valent plus)
    if (property.id >= 1 && property.id <= 10) {
      value *= 1.2 // Premier côté, plus visité
    } else if (property.id >= 31 && property.id <= 39) {
      value *= 1.1 // Dernier côté, assez visité
    }

    return value
  }

  // Obtenir la personnalité du bot
  getPersonality(): BotPersonality {
    return this.personality
  }

  // Changer la personnalité du bot (pour les bots "random")
  setPersonality(personalityType: keyof typeof BOT_PERSONALITIES): void {
    this.personality = BOT_PERSONALITIES[personalityType]
  }
}

// Fonction utilitaire pour créer un bot avec une personnalité aléatoire
export function createRandomBot(playerId: string): BotLogic {
  const personalities = Object.keys(BOT_PERSONALITIES)
  const randomPersonality = personalities[Math.floor(Math.random() * personalities.length)]
  return new BotLogic(playerId, randomPersonality as keyof typeof BOT_PERSONALITIES)
}

// Fonction pour obtenir une couleur basée sur la personnalité
export function getPersonalityColor(personalityType: string): string {
  const colors = {
    aggressive: '#ef4444', // Rouge
    conservative: '#3b82f6', // Bleu
    balanced: '#10b981', // Vert
    random: '#f59e0b' // Orange
  }
  
  return colors[personalityType as keyof typeof colors] || '#6b7280'
}
