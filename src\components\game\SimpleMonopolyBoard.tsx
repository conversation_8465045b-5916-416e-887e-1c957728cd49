import React from 'react'
import { SimpleBoardCase, SimplePlayer } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'
import CenterDice from './CenterDice'

interface SimpleMonopolyBoardProps {
  cases: SimpleBoardCase[]
  players: SimplePlayer[]
  currentPlayer: number
  movingPlayers?: Set<string>
  onCaseClick?: (caseId: number) => void
  diceValues?: [number, number]
  isRolling?: boolean
}

export default function SimpleMonopolyBoard({
  cases,
  players,
  currentPlayer,
  movingPlayers = new Set(),
  onCaseClick,
  diceValues = [0, 0],
  isRolling = false
}: SimpleMonopolyBoardProps) {
  const viewport = useViewport()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  // Fonction pour obtenir la position d'une case sur le plateau
  const getSquarePosition = (index: number) => {
    // Dimensions uniformes pour toutes les cases
    // 40 cases total : 4 coins + 36 cases sur les côtés (9 par côté)
    // Chaque case fait 10% de largeur/hauteur
    const caseWidth = '10%'
    const caseHeight = '10%'

    // Coin inférieur droit - DÉPART (case 0)
    if (index === 0) return { bottom: '0%', right: '0%', width: caseWidth, height: caseHeight }

    // Côté bas (cases 1-9, de droite à gauche)
    if (index >= 1 && index <= 9) {
      return {
        bottom: '0%',
        right: `${index * 10}%`,
        width: caseWidth,
        height: caseHeight
      }
    }

    // Coin inférieur gauche - PRISON (case 10)
    if (index === 10) return { bottom: '0%', left: '0%', width: caseWidth, height: caseHeight }

    // Côté gauche (cases 11-19, de bas en haut)
    if (index >= 11 && index <= 19) {
      return {
        left: '0%',
        bottom: `${(index - 10) * 10}%`,
        width: caseWidth,
        height: caseHeight
      }
    }

    // Coin supérieur gauche - PARC GRATUIT (case 20)
    if (index === 20) return { top: '0%', left: '0%', width: caseWidth, height: caseHeight }

    // Côté haut (cases 21-29, de gauche à droite)
    if (index >= 21 && index <= 29) {
      return {
        top: '0%',
        left: `${(index - 20) * 10}%`,
        width: caseWidth,
        height: caseHeight
      }
    }

    // Coin supérieur droit - ALLEZ EN PRISON (case 30)
    if (index === 30) return { top: '0%', right: '0%', width: caseWidth, height: caseHeight }

    // Côté droit (cases 31-39, de haut en bas)
    if (index >= 31 && index <= 39) {
      return {
        right: '0%',
        top: `${(index - 30) * 10}%`,
        width: caseWidth,
        height: caseHeight
      }
    }

    return { bottom: '0%', right: '0%', width: caseWidth, height: caseHeight }
  }

  // Fonction pour obtenir l'orientation du texte - tout lisible horizontalement
  const getTextOrientation = () => {
    return 'rotate-0' // Tout le texte reste horizontal et lisible
  }

  // Couleurs spéciales pour les coins
  const getCornerColor = (index: number) => {
    switch (index) {
      case 0: return 'bg-red-600' // DÉPART
      case 10: return 'bg-orange-600' // PRISON
      case 20: return 'bg-green-600' // PARC GRATUIT
      case 30: return 'bg-red-700' // ALLEZ EN PRISON
      default: return 'bg-gray-50'
    }
  }

  // Fonction pour raccourcir intelligemment les noms
  const getShortName = (name: string, isMobile: boolean) => {
    if (!isMobile) return name

    // Abréviations spéciales pour les noms courants
    const abbreviations: { [key: string]: string } = {
      'Mariage': 'Mariage',
      'Crédit Immobilier': 'Crédit',
      'Pièces': 'Pièces',
      'Enfant': 'Enfant',
      'Voiture': 'Voiture',
      'Manager': 'Manager',
      'Maison': 'Maison',
      'Assurance Vie': 'Assur.',
      'Colocation': 'Coloc.',
      'Aide au Logement CAF': 'CAF',
      'Appartement 20m²': '20m²',
      'Temps en Communauté': 'Commu.',
      'Freelance': 'Freelance',
      'Précaire': 'Précaire',
      'Assurance Maladie': 'Assur.',
      'CDI': 'CDI',
      'Université': 'Univ.',
      'Bourse Étudiant': 'Bourse',
      'Lycée': 'Lycée',
      'Cantine': 'Cantine',
      'Collège': 'Collège',
      'Allocation Familiale': 'Alloc.',
      'École Primaire': 'École',
      'Château': 'Château',
      'Impôt sur la Fortune': 'Impôt',
      'Héritage': 'Héritage',
      'Investisseur': 'Invest.',
      'Jet Privé': 'Jet',
      'Crise Financière': 'Crise'
    }

    if (abbreviations[name]) return abbreviations[name]

    // Si pas d'abréviation spéciale, couper intelligemment
    if (name.length <= 8) return name

    // Essayer de garder le premier mot s'il est court
    const firstWord = name.split(' ')[0]
    if (firstWord.length <= 8) return firstWord

    // Sinon couper à 8 caractères
    return name.substring(0, 8) + '.'
  }

  const renderSquare = (caseData: SimpleBoardCase, index: number) => {
    const playersOnSquare = getPlayersOnCase(index)
    const position = getSquarePosition(index)
    const textOrientation = getTextOrientation()
    const isCorner = [0, 10, 20, 30].includes(index)
    
    return (
      <div
        key={index}
        className={`
          absolute border-2 border-gray-900
          ${isCorner ? getCornerColor(index) : 'bg-gray-50'}
          flex flex-col items-center justify-center
          cursor-pointer hover:brightness-110 transition-all
          ${textOrientation}
        `}
        style={{
          ...position
        }}
        onClick={() => onCaseClick?.(index)}
      >
        {/* Bande de couleur pour les propriétés */}
        {caseData.color && !isCorner && (
          <div
            className="absolute top-0 left-0 right-0 h-[20%]"
            style={{ backgroundColor: caseData.color }}
          />
        )}

        {/* Contenu de la case */}
        <div className="p-0.5 h-full flex flex-col justify-center items-center text-center overflow-hidden">
          <div
            className={`font-black ${isCorner ? 'text-white drop-shadow-lg' : 'text-black'} leading-none max-w-full break-words text-center`}
            style={{
              fontSize: viewport.isMobile ? '12px' : viewport.isTablet ? '15px' : '17px',
              lineHeight: viewport.isMobile ? '13px' : viewport.isTablet ? '16px' : '18px',
              textShadow: isCorner ? '2px 2px 4px rgba(0,0,0,0.8)' : '1px 1px 2px rgba(255,255,255,0.8)'
            }}
          >
            {getShortName(caseData.name, viewport.isMobile)}
          </div>
          {caseData.price && caseData.price > 0 && (
            <div
              className={`${isCorner ? 'text-yellow-300 drop-shadow-lg' : 'text-blue-900'} font-black mt-0.5 bg-white bg-opacity-95 px-1.5 py-0.5 rounded shadow-md border border-gray-300`}
              style={{
                fontSize: viewport.isMobile ? '11px' : viewport.isTablet ? '13px' : '15px',
                lineHeight: viewport.isMobile ? '12px' : viewport.isTablet ? '14px' : '16px',
                textShadow: '1px 1px 2px rgba(255,255,255,0.8)'
              }}
            >
              {caseData.price}€
            </div>
          )}
        </div>

        {/* Joueurs sur cette case */}
        {playersOnSquare.length > 0 && (
          <div className="absolute bottom-1 left-1 right-1 flex justify-center items-end space-x-0.5">
            {playersOnSquare.map((player, playerIndex) => (
              <div
                key={player.id}
                className={`
                  rounded-full 
                  ${viewport.isMobile ? 'w-2 h-2' : 'w-3 h-3'} 
                  border border-white shadow-lg
                  ${player.id === players[currentPlayer]?.id ? 'ring-2 ring-yellow-400 animate-pulse' : ''}
                  ${movingPlayers.has(player.id) ? 'animate-bounce' : ''}
                `}
                style={{
                  backgroundColor: player.color,
                  transform: `translateX(${playerIndex * 2}px)`,
                  zIndex: 10 + playerIndex
                }}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative w-full h-full bg-green-100 border-4 border-gray-800 shadow-2xl">
        {/* Toutes les cases du plateau */}
        {cases.map((caseData, index) => renderSquare(caseData, index))}

        {/* Zone centrale du plateau avec dés */}
        <div className="absolute top-[10%] left-[10%] right-[10%] bottom-[10%] bg-gradient-to-br from-green-50 via-green-100 to-green-200 border-2 border-gray-600 flex flex-col items-center justify-center">
          {/* Titre du jeu */}
          <div className="text-center mb-4">
            <div className={`font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 drop-shadow-lg ${viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-3xl'}`}>
              TheRateRace
            </div>
            <div className={`text-gray-700 font-semibold ${viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-base'}`}>
              .io
            </div>
            <div className={`text-gray-600 font-medium ${viewport.isMobile ? 'text-[8px]' : viewport.isTablet ? 'text-xs' : 'text-sm'} mt-1`}>
              Jeu de plateau satirique
            </div>
          </div>

          {/* Dés au centre du plateau */}
          <CenterDice
            diceValues={diceValues}
            isRolling={isRolling}
          />
        </div>
      </div>
    </div>
  )
}
