import React from 'react'
import { SimpleBoardCase, SimplePlayer } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'
import CenterDice from './CenterDice'

interface SimpleMonopolyBoardProps {
  cases: SimpleBoardCase[]
  players: SimplePlayer[]
  currentPlayer: number
  movingPlayers?: Set<string>
  onCaseClick?: (caseId: number) => void
  diceValues?: [number, number]
  isRolling?: boolean
}

export default function SimpleMonopolyBoard({
  cases,
  players,
  currentPlayer,
  movingPlayers = new Set(),
  onCaseClick,
  diceValues = [0, 0],
  isRolling = false
}: SimpleMonopolyBoardProps) {
  const viewport = useViewport()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  // Fonction pour obtenir la position d'une case sur le plateau
  const getSquarePosition = (index: number) => {
    // Coin inférieur droit - DÉPART (case 0)
    if (index === 0) return { bottom: '0%', right: '0%', width: '10%', height: '10%' }
    
    // Côté bas (cases 1-9, de droite à gauche)
    if (index >= 1 && index <= 9) {
      return { bottom: '0%', right: `${(10 - index) * 10}%`, width: '10%', height: '10%' }
    }
    
    // Coin inférieur gauche - PRISON (case 10)
    if (index === 10) return { bottom: '0%', left: '0%', width: '10%', height: '10%' }
    
    // Côté gauche (cases 11-19, de bas en haut)
    if (index >= 11 && index <= 19) {
      return { left: '0%', bottom: `${(index - 10) * 10}%`, width: '10%', height: '10%' }
    }
    
    // Coin supérieur gauche - PARC GRATUIT (case 20)
    if (index === 20) return { top: '0%', left: '0%', width: '10%', height: '10%' }
    
    // Côté haut (cases 21-29, de gauche à droite)
    if (index >= 21 && index <= 29) {
      return { top: '0%', left: `${(index - 20) * 10}%`, width: '10%', height: '10%' }
    }
    
    // Coin supérieur droit - ALLEZ EN PRISON (case 30)
    if (index === 30) return { top: '0%', right: '0%', width: '10%', height: '10%' }
    
    // Côté droit (cases 31-39, de haut en bas)
    if (index >= 31 && index <= 39) {
      return { right: '0%', top: `${(index - 30) * 10}%`, width: '10%', height: '10%' }
    }
    
    return { bottom: '0%', right: '0%', width: '10%', height: '10%' }
  }

  // Fonction pour obtenir l'orientation du texte - tout lisible horizontalement
  const getTextOrientation = (index: number) => {
    return 'rotate-0' // Tout le texte reste horizontal et lisible
  }

  // Couleurs spéciales pour les coins
  const getCornerColor = (index: number) => {
    switch (index) {
      case 0: return 'bg-red-600' // DÉPART
      case 10: return 'bg-orange-600' // PRISON
      case 20: return 'bg-green-600' // PARC GRATUIT
      case 30: return 'bg-red-700' // ALLEZ EN PRISON
      default: return 'bg-white'
    }
  }

  const renderSquare = (caseData: SimpleBoardCase, index: number) => {
    const playersOnSquare = getPlayersOnCase(index)
    const position = getSquarePosition(index)
    const textOrientation = getTextOrientation(index)
    const isCorner = [0, 10, 20, 30].includes(index)
    
    return (
      <div
        key={index}
        className={`
          absolute border-2 border-gray-800 
          ${isCorner ? getCornerColor(index) : 'bg-white'}
          flex flex-col items-center justify-center
          cursor-pointer hover:brightness-110 transition-all
          ${textOrientation}
        `}
        style={{
          ...position,
          fontSize: viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'
        }}
        onClick={() => onCaseClick?.(index)}
      >
        {/* Bande de couleur pour les propriétés */}
        {caseData.color && !isCorner && (
          <div
            className="absolute top-0 left-0 right-0 h-[20%]"
            style={{ backgroundColor: caseData.color }}
          />
        )}

        {/* Contenu de la case */}
        <div className="p-1 h-full flex flex-col justify-center items-center text-center">
          <div className={`font-bold ${isCorner ? 'text-white' : 'text-gray-800'} leading-tight`}>
            {caseData.name}
          </div>
          {caseData.price > 0 && (
            <div className={`text-xs ${isCorner ? 'text-yellow-200' : 'text-gray-600'} font-semibold mt-1`}>
              {caseData.price}€
            </div>
          )}
        </div>

        {/* Joueurs sur cette case */}
        {playersOnSquare.length > 0 && (
          <div className="absolute bottom-1 left-1 right-1 flex justify-center items-end space-x-0.5">
            {playersOnSquare.map((player, playerIndex) => (
              <div
                key={player.id}
                className={`
                  rounded-full 
                  ${viewport.isMobile ? 'w-2 h-2' : 'w-3 h-3'} 
                  border border-white shadow-lg
                  ${player.id === players[currentPlayer]?.id ? 'ring-2 ring-yellow-400 animate-pulse' : ''}
                  ${movingPlayers.has(player.id) ? 'animate-bounce' : ''}
                `}
                style={{
                  backgroundColor: player.color,
                  transform: `translateX(${playerIndex * 2}px)`,
                  zIndex: 10 + playerIndex
                }}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative w-full h-full bg-green-100 border-4 border-gray-800 shadow-2xl">
        {/* Toutes les cases du plateau */}
        {cases.map((caseData, index) => renderSquare(caseData, index))}

        {/* Zone centrale du plateau avec dés */}
        <div className="absolute top-[10%] left-[10%] right-[10%] bottom-[10%] bg-gradient-to-br from-green-50 via-green-100 to-green-200 border-2 border-gray-600 flex flex-col items-center justify-center">
          {/* Titre du jeu */}
          <div className="text-center mb-4">
            <div className={`font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 drop-shadow-lg ${viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-3xl'}`}>
              TheRateRace
            </div>
            <div className={`text-gray-700 font-semibold ${viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-base'}`}>
              .io
            </div>
            <div className={`text-gray-600 font-medium ${viewport.isMobile ? 'text-[8px]' : viewport.isTablet ? 'text-xs' : 'text-sm'} mt-1`}>
              Jeu de plateau satirique
            </div>
          </div>

          {/* Dés au centre du plateau */}
          <CenterDice
            diceValues={diceValues}
            isRolling={isRolling}
          />
        </div>
      </div>
    </div>
  )
}
