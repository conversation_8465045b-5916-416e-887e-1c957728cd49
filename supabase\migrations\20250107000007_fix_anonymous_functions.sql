-- Migration pour corriger les fonctions anonymes (ambiguïté des noms de colonnes)

-- Fonction corrigée pour créer un salon anonyme
CREATE OR REPLACE FUNCTION create_anonymous_game_room(
    room_name TEXT,
    room_description TEXT DEFAULT NULL,
    max_players_count INTEGER DEFAULT 4,
    room_password TEXT DEFAULT NULL,
    creator_name TEXT DEFAULT 'Joueur Anonyme'
)
RETURNS UUID AS $$
DECLARE
    new_room_id UUID;
    generated_invite_code TEXT;
    password_hash TEXT;
BEGIN
    -- Generate unique invite code
    LOOP
        generated_invite_code := generate_invite_code();
        EXIT WHEN NOT EXISTS (SELECT 1 FROM public.game_rooms WHERE game_rooms.invite_code = generated_invite_code);
    END LOOP;
    
    -- Hash password if provided
    IF room_password IS NOT NULL THEN
        password_hash := crypt(room_password, gen_salt('bf'));
    END IF;
    
    -- Create room (sans created_by pour les tests anonymes)
    INSERT INTO public.game_rooms (name, description, max_players, password_hash, invite_code)
    VALUES (room_name, room_description, max_players_count, password_hash, generated_invite_code)
    RETURNING id INTO new_room_id;
    
    -- Add anonymous creator as first player
    INSERT INTO public.room_players (room_id, player_name, player_index, is_bot)
    VALUES (new_room_id, creator_name, 0, false);
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = 1 
    WHERE id = new_room_id;
    
    RETURN new_room_id;
END;
$$ LANGUAGE plpgsql;
