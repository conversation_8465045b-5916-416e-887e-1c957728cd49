module.exports = {

"[project]/src/lib/i18n/locales/en/auth.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_en_auth_json_3dc783f8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/auth.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/en/common.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_en_common_json_0ec0c969._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/common.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/en/game.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_en_game_json_0783f749._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/en/game.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/auth.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_fr_auth_json_1f2c8fc0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/auth.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/common.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_fr_common_json_e9702505._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/common.json (json)");
    });
});
}}),
"[project]/src/lib/i18n/locales/fr/game.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_i18n_locales_fr_game_json_b3cacd8e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/i18n/locales/fr/game.json (json)");
    });
});
}}),

};