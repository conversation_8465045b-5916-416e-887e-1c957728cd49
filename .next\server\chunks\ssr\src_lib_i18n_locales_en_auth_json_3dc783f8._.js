module.exports = {

"[project]/src/lib/i18n/locales/en/auth.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"login\":{\"title\":\"Login\",\"subtitle\":\"Sign in to join the race\",\"email\":\"Email address\",\"password\":\"Password\",\"forgotPassword\":\"Forgot password?\",\"noAccount\":\"Don't have an account?\",\"signUp\":\"Create account\"},\"register\":{\"title\":\"Register\",\"subtitle\":\"Join the race for success\",\"email\":\"Email address\",\"password\":\"Password\",\"confirmPassword\":\"Confirm password\",\"username\":\"Username\",\"hasAccount\":\"Already have an account?\",\"signIn\":\"Sign in\"},\"errors\":{\"invalidEmail\":\"Invalid email address\",\"passwordTooShort\":\"Password must be at least 6 characters\",\"passwordMismatch\":\"Passwords don't match\",\"usernameTaken\":\"This username is already taken\",\"loginFailed\":\"Login failed\",\"registrationFailed\":\"Registration failed\"}}"));}}),

};