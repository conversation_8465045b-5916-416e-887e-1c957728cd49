'use client'

import { useI18n } from '@/lib/i18n/hooks'

export default function Footer() {
  const { t } = useI18n('common')

  return (
    <footer className="bg-card border-t border-border mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo & Description */}
          <div className="space-y-4">
            <div className="text-xl font-bold text-primary neon-text">
              {t('appName')}
            </div>
            <p className="text-muted text-sm">
              {t('description')}
            </p>
          </div>

          {/* Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Liens</h3>
            <div className="space-y-2 text-sm">
              <a href="/about" className="block text-muted hover:text-primary transition-colors">
                À propos
              </a>
              <a href="/rules" className="block text-muted hover:text-primary transition-colors">
                Règles du jeu
              </a>
              <a href="/support" className="block text-muted hover:text-primary transition-colors">
                Support
              </a>
            </div>
          </div>

          {/* Social */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Communauté</h3>
            <div className="space-y-2 text-sm">
              <a href="#" className="block text-muted hover:text-secondary transition-colors">
                Discord
              </a>
              <a href="#" className="block text-muted hover:text-secondary transition-colors">
                Twitter
              </a>
              <a href="#" className="block text-muted hover:text-secondary transition-colors">
                GitHub
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-border mt-8 pt-4 text-center text-sm text-muted">
          © 2025 TheRateRace.io - Tous droits réservés
        </div>
      </div>
    </footer>
  )
}
