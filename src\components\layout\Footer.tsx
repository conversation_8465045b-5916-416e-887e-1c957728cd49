'use client'

import { useI18n } from '@/lib/i18n/hooks'
import { useViewport } from '@/hooks/useViewport'

export default function Footer() {
  const { t } = useI18n('common')
  const viewport = useViewport()

  return (
    <footer className="bg-card border-t border-border mt-auto flex-shrink-0">
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col md:flex-row justify-between items-center gap-2">
          {/* Logo & Copyright */}
          <div className="flex items-center space-x-2">
            <div className={`font-bold text-primary neon-text ${viewport.isMobile ? 'text-sm' : 'text-base'}`}>
              TheRateRace.io
            </div>
            <span className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
              © 2025
            </span>
          </div>

          {/* Links compacts */}
          <div className="flex items-center space-x-4 text-xs">
            <a href="/about" className="text-muted-foreground hover:text-primary transition-colors">
              À propos
            </a>
            <a href="/rules" className="text-muted-foreground hover:text-primary transition-colors">
              Règles
            </a>
            <a href="/support" className="text-muted-foreground hover:text-primary transition-colors">
              Support
            </a>
          </div>

          {/* Social compacts */}
          <div className="flex items-center space-x-3 text-xs">
            <a href="#" className="text-muted-foreground hover:text-secondary transition-colors">
              Discord
            </a>
            <a href="#" className="text-muted-foreground hover:text-secondary transition-colors">
              GitHub
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
