{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "TwNe6f9y/iEKfHnGVRLL3m499gOy0Xme88IS6YfOuX0=", "__NEXT_PREVIEW_MODE_ID": "9164fa8c4760c934def5acc38ee1013a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3d108603a80494af46fef9dff10c7c3a68f463365a991fc541585fd21fa77166", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "986fae7b8b3962ad19e4cc856a6c25892d7e5964fd4831118db3b2b323e8eb36"}}}, "instrumentation": null, "functions": {}}