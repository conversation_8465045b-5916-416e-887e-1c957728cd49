import React from 'react'
import { SimpleBoardCase, SimplePlayer } from '@/types/game'

interface GameBoardProps {
  cases: SimpleBoardCase[]
  players: SimplePlayer[]
  currentPlayer: number
}

export default function GameBoard({ cases, players, currentPlayer }: GameBoardProps) {
  // Organiser les cases en bordure du plateau (comme Monopoly)
  const getBoardLayout = () => {
    const bottom = cases.slice(0, 11) // Cases 0-10
    const left = cases.slice(11, 20) // Cases 11-19
    const top = cases.slice(20, 31).reverse() // Cases 20-30 (inversées)
    const right = cases.slice(31, 40).reverse() // Cases 31-39 (inversées)
    
    return { bottom, left, top, right }
  }

  const { bottom, left, top, right } = getBoardLayout()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  const renderCase = (boardCase: SimpleBoardCase, position: 'bottom' | 'left' | 'top' | 'right') => {
    const playersOnCase = getPlayersOnCase(boardCase.id)
    const isCorner = [0, 10, 20, 30].includes(boardCase.id)
    
    const baseClasses = `
      relative border border-border bg-card hover:bg-card/80 transition-all duration-200
      flex flex-col items-center justify-center text-center p-1
      ${isCorner ? 'w-20 h-20' : position === 'bottom' || position === 'top' ? 'w-16 h-20' : 'w-20 h-16'}
    `
    
    const colorClasses = boardCase.color ? `border-l-4` : ''
    
    return (
      <div 
        key={boardCase.id}
        className={`${baseClasses} ${colorClasses}`}
        style={boardCase.color ? { borderLeftColor: boardCase.color } : {}}
      >
        {/* Icône de la case */}
        <div className="text-lg mb-1">{boardCase.icon}</div>
        
        {/* Nom de la case */}
        <div className="text-xs font-semibold text-foreground leading-tight">
          {boardCase.name}
        </div>
        
        {/* Prix si applicable */}
        {boardCase.price && (
          <div className="text-xs text-secondary font-bold">
            {boardCase.price}€
          </div>
        )}
        
        {/* Joueurs sur cette case */}
        {playersOnCase.length > 0 && (
          <div className="absolute -top-2 -right-2 flex flex-wrap gap-1">
            {playersOnCase.map(player => (
              <div
                key={player.id}
                className={`
                  w-4 h-4 rounded-full border-2 border-white shadow-lg
                  flex items-center justify-center text-xs
                  ${player.id === players[currentPlayer].id ? 'ring-2 ring-primary animate-pulse' : ''}
                `}
                style={{ backgroundColor: player.color }}
                title={player.name}
              >
                {player.avatar === '👤' ? '👤' : '🤖'}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-gradient-to-br from-card to-muted/20 p-6 rounded-2xl shadow-2xl">
      <div className="relative">
        {/* Plateau principal */}
        <div className="grid grid-cols-11 grid-rows-11 gap-0 bg-background/50 rounded-xl overflow-hidden border-2 border-border">
          
          {/* Ligne du haut */}
          <div className="col-span-11 flex">
            {top.map(boardCase => renderCase(boardCase, 'top'))}
          </div>
          
          {/* Lignes du milieu */}
          {Array.from({ length: 9 }, (_, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {/* Case de gauche */}
              {left[8 - rowIndex] && (
                <div className="col-span-1">
                  {renderCase(left[8 - rowIndex], 'left')}
                </div>
              )}
              
              {/* Centre du plateau */}
              <div className="col-span-9 bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                {rowIndex === 4 && (
                  <div className="text-center">
                    <div className="text-4xl font-black text-primary mb-2">
                      TheRateRace
                    </div>
                    <div className="text-lg font-bold text-secondary">
                      .io
                    </div>
                    <div className="text-sm text-muted-foreground mt-2">
                      La course à la réussite
                    </div>
                  </div>
                )}
              </div>
              
              {/* Case de droite */}
              {right[rowIndex] && (
                <div className="col-span-1">
                  {renderCase(right[rowIndex], 'right')}
                </div>
              )}
            </React.Fragment>
          ))}
          
          {/* Ligne du bas */}
          <div className="col-span-11 flex">
            {bottom.map(boardCase => renderCase(boardCase, 'bottom'))}
          </div>
        </div>
        
        {/* Légende des couleurs */}
        <div className="mt-4 flex flex-wrap gap-2 justify-center">
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-brown-600 rounded"></div>
            <span>Startups</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-sky-300 rounded"></div>
            <span>Services</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-pink-500 rounded"></div>
            <span>Marketing</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-orange-500 rounded"></div>
            <span>Luxe</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>Culture</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-yellow-500 rounded"></div>
            <span>Immobilier</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Business</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>Empire</span>
          </div>
        </div>
      </div>
    </div>
  )
}
