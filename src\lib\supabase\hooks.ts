'use client'

import { useState, useEffect } from 'react'
import { createClient } from './client'
import { GameRoom, RoomPlayer, CreateRoomData, JoinRoomData } from '@/types/game'

export function useSupabase() {
  const supabase = createClient()
  return supabase
}

export function useGameRooms() {
  const [rooms, setRooms] = useState<GameRoom[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = useSupabase()

  const fetchRooms = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('status', 'waiting')
        .order('created_at', { ascending: false })

      if (error) throw error
      setRooms(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des salons')
    } finally {
      setLoading(false)
    }
  }

  const createRoom = async (roomData: CreateRoomData): Promise<string | null> => {
    try {
      const { data, error } = await supabase.rpc('create_anonymous_game_room', {
        room_name: roomData.name,
        room_description: roomData.description,
        max_players_count: roomData.max_players,
        room_password: roomData.password,
        creator_name: 'Joueur Test'
      })

      if (error) throw error
      await fetchRooms() // Refresh the list
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la création du salon')
      return null
    }
  }

  const joinRoom = async (joinData: JoinRoomData): Promise<boolean> => {
    try {
      const { data, error } = await supabase.rpc('join_anonymous_game_room', {
        room_id_param: joinData.room_id,
        player_name_param: 'Joueur Test',
        room_password_param: joinData.password
      })

      if (error) throw error
      await fetchRooms() // Refresh the list
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la connexion au salon')
      return false
    }
  }

  const joinRoomByInviteCode = async (inviteCode: string, password?: string): Promise<string | null> => {
    try {
      // First, find the room by invite code
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .select('id')
        .eq('invite_code', inviteCode)
        .single()

      if (roomError) throw new Error('Code d\'invitation invalide')

      const success = await joinRoom({ room_id: roomData.id, password })
      return success ? roomData.id : null
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur avec le code d\'invitation')
      return null
    }
  }

  const addBotsToRoom = async (roomId: string): Promise<number> => {
    try {
      const { data, error } = await supabase.rpc('add_bots_to_room', {
        room_id: roomId
      })

      if (error) throw error
      await fetchRooms() // Refresh the list
      return data || 0
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'ajout des bots')
      return 0
    }
  }

  useEffect(() => {
    fetchRooms()

    // Subscribe to real-time updates
    const subscription = supabase
      .channel('game_rooms_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'game_rooms' },
        () => fetchRooms()
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return {
    rooms,
    loading,
    error,
    createRoom,
    joinRoom,
    joinRoomByInviteCode,
    addBotsToRoom,
    refetch: fetchRooms
  }
}

export function useRoomPlayers(roomId: string | null) {
  const [players, setPlayers] = useState<RoomPlayer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = useSupabase()

  const fetchPlayers = async () => {
    if (!roomId) {
      setPlayers([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('room_players')
        .select('*')
        .eq('room_id', roomId)
        .order('player_index', { ascending: true })

      if (error) throw error
      setPlayers(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des joueurs')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlayers()

    if (!roomId) return

    // Subscribe to real-time updates
    const subscription = supabase
      .channel(`room_players_${roomId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'room_players',
          filter: `room_id=eq.${roomId}`
        },
        () => fetchPlayers()
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [roomId])

  return {
    players,
    loading,
    error,
    refetch: fetchPlayers
  }
}

export function useAuth() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const supabase = useSupabase()

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password })
    return { error }
  }

  const signUp = async (email: string, password: string, username: string) => {
    const { error } = await supabase.auth.signUp({ 
      email, 
      password,
      options: {
        data: { username }
      }
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut
  }
}
