'use client'

import { useState, useEffect } from 'react'

interface ViewportSize {
  width: number
  height: number
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLandscape: boolean
  isPortrait: boolean
}

export function useViewport(): ViewportSize {
  const [viewport, setViewport] = useState<ViewportSize>({
    width: 0,
    height: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLandscape: false,
    isPortrait: false
  })

  useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setViewport({
        width,
        height,
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        isLandscape: width > height,
        isPortrait: height > width
      })
    }

    // Initial call
    updateViewport()

    // Listen for resize events
    window.addEventListener('resize', updateViewport)
    window.addEventListener('orientationchange', updateViewport)

    return () => {
      window.removeEventListener('resize', updateViewport)
      window.removeEventListener('orientationchange', updateViewport)
    }
  }, [])

  return viewport
}

// Hook pour obtenir les classes CSS adaptatives
export function useResponsiveClasses() {
  const viewport = useViewport()
  
  return {
    container: viewport.isMobile 
      ? 'h-screen w-screen overflow-hidden p-2' 
      : viewport.isTablet 
      ? 'h-screen w-screen overflow-hidden p-4'
      : 'h-screen w-screen overflow-hidden p-6',
    
    grid: viewport.isMobile 
      ? 'grid grid-rows-[auto_1fr] gap-2 h-full'
      : viewport.isTablet
      ? 'grid grid-rows-[auto_1fr] gap-4 h-full'
      : 'grid grid-cols-[1fr_300px] gap-6 h-full',
    
    text: {
      title: viewport.isMobile ? 'text-2xl' : viewport.isTablet ? 'text-3xl' : 'text-4xl',
      subtitle: viewport.isMobile ? 'text-sm' : viewport.isTablet ? 'text-base' : 'text-lg',
      body: viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-base'
    },
    
    spacing: {
      section: viewport.isMobile ? 'space-y-2' : viewport.isTablet ? 'space-y-3' : 'space-y-4',
      element: viewport.isMobile ? 'gap-1' : viewport.isTablet ? 'gap-2' : 'gap-3'
    }
  }
}
