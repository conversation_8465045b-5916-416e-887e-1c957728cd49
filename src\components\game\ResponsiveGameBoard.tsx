import React from 'react'
import { SimpleBoardCase, SimplePlayer } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

interface ResponsiveGameBoardProps {
  cases: SimpleBoardCase[]
  players: SimplePlayer[]
  currentPlayer: number
}

export default function ResponsiveGameBoard({ cases, players, currentPlayer }: ResponsiveGameBoardProps) {
  const viewport = useViewport()

  // Organiser les cases en bordure du plateau
  const getBoardLayout = () => {
    const bottom = cases.slice(0, 11) // Cases 0-10
    const left = cases.slice(11, 20) // Cases 11-19
    const top = cases.slice(20, 31).reverse() // Cases 20-30 (inversées)
    const right = cases.slice(31, 40).reverse() // Cases 31-39 (inversées)
    
    return { bottom, left, top, right }
  }

  const { bottom, left, top, right } = getBoardLayout()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  // Tailles adaptatives
  const boardSize = viewport.isMobile ? 'w-full h-full min-h-[300px]' : 
                   viewport.isTablet ? 'w-full h-full min-h-[400px]' : 
                   'w-full h-full min-h-[500px]'
  
  const caseSize = viewport.isMobile ? 'w-8 h-8' : 
                  viewport.isTablet ? 'w-12 h-12' : 
                  'w-16 h-16'
  
  const cornerSize = viewport.isMobile ? 'w-10 h-10' : 
                    viewport.isTablet ? 'w-14 h-14' : 
                    'w-20 h-20'

  const textSize = viewport.isMobile ? 'text-[6px]' : 
                  viewport.isTablet ? 'text-[8px]' : 
                  'text-xs'

  const iconSize = viewport.isMobile ? 'text-xs' : 
                  viewport.isTablet ? 'text-sm' : 
                  'text-base'

  const renderCase = (boardCase: SimpleBoardCase, isCorner: boolean = false) => {
    const playersHere = getPlayersOnCase(boardCase.id)
    const sizeClass = isCorner ? cornerSize : caseSize
    
    return (
      <div
        key={boardCase.id}
        className={`${sizeClass} border-2 border-border bg-card relative flex flex-col items-center justify-center p-1 transition-all duration-300 ${
          boardCase.type === 'start' ? 'bg-gradient-to-br from-accent to-accent/80' :
          boardCase.type === 'property' ? 'hover:bg-card/80' :
          boardCase.type === 'tax' ? 'bg-red-500/20' :
          'bg-card'
        }`}
        style={{
          borderTopColor: boardCase.color || 'var(--border)',
          borderTopWidth: boardCase.type === 'property' ? '4px' : '2px'
        }}
      >
        {/* Icône */}
        <div className={`${iconSize} mb-1`}>
          {boardCase.icon}
        </div>
        
        {/* Nom de la case */}
        <div className={`${textSize} text-center font-bold leading-tight text-foreground`}>
          {viewport.isMobile ? 
            boardCase.name.split(' ')[0] : // Premier mot seulement sur mobile
            boardCase.name.length > 12 ? 
              boardCase.name.substring(0, 12) + '...' : 
              boardCase.name
          }
        </div>
        
        {/* Prix */}
        {boardCase.price && (
          <div className={`${textSize} text-secondary font-semibold`}>
            {boardCase.price}€
          </div>
        )}
        
        {/* Joueurs sur cette case */}
        {playersHere.length > 0 && (
          <div className="absolute -top-1 -right-1 flex flex-wrap gap-0.5">
            {playersHere.slice(0, viewport.isMobile ? 2 : 4).map((player, idx) => (
              <div
                key={player.id}
                className={`${viewport.isMobile ? 'w-3 h-3 text-[8px]' : 'w-4 h-4 text-xs'} rounded-full flex items-center justify-center font-bold text-white shadow-lg transform transition-transform hover:scale-110`}
                style={{ backgroundColor: player.color }}
                title={player.name}
              >
                {player.avatar}
              </div>
            ))}
            {playersHere.length > (viewport.isMobile ? 2 : 4) && (
              <div className={`${viewport.isMobile ? 'w-3 h-3 text-[6px]' : 'w-4 h-4 text-[8px]'} rounded-full bg-muted flex items-center justify-center font-bold`}>
                +{playersHere.length - (viewport.isMobile ? 2 : 4)}
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`${boardSize} flex items-center justify-center p-2`}>
      <div className="relative bg-background/50 backdrop-blur-sm rounded-lg border-2 border-border shadow-2xl">
        
        {/* Plateau en grille */}
        <div className="grid grid-cols-11 grid-rows-11 gap-0">
          
          {/* Ligne du haut */}
          <div className="col-span-11 flex">
            {top.map((boardCase, index) => (
              <div key={boardCase.id}>
                {renderCase(boardCase, index === 0 || index === top.length - 1)}
              </div>
            ))}
          </div>
          
          {/* Lignes du milieu */}
          {Array.from({ length: 9 }, (_, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {/* Case de gauche */}
              <div>
                {left[8 - rowIndex] && renderCase(left[8 - rowIndex])}
              </div>
              
              {/* Centre du plateau */}
              <div className="col-span-9 flex items-center justify-center bg-gradient-to-br from-background to-primary/5 border border-border/30">
                {rowIndex === 4 && (
                  <div className="text-center">
                    <div className={`font-black text-primary ${viewport.isMobile ? 'text-lg' : 'text-3xl'}`}>
                      TheRateRace
                    </div>
                    <div className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
                      .io
                    </div>
                  </div>
                )}
              </div>
              
              {/* Case de droite */}
              <div>
                {right[rowIndex] && renderCase(right[rowIndex])}
              </div>
            </React.Fragment>
          ))}
          
          {/* Ligne du bas */}
          <div className="col-span-11 flex">
            {bottom.map((boardCase, index) => (
              <div key={boardCase.id}>
                {renderCase(boardCase, index === 0 || index === bottom.length - 1)}
              </div>
            ))}
          </div>
          
        </div>
      </div>
    </div>
  )
}
