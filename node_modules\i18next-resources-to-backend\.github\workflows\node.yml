name: node

on:
  push:
    branches:
      - main

jobs:
  test:
    name: Test on node ${{ matrix.node }} and ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node: [ '20.x', '18.x', '16.x' ]
        # os: [ubuntu-latest, windows-latest, macOS-latest]
        os: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node }}
      - run: npm install
      - run: npm test