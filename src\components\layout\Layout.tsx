'use client'

import Header from './Header'
import Footer from './Footer'
import { useViewport } from '@/hooks/useViewport'

interface LayoutProps {
  children: React.ReactNode
  showHeader?: boolean
  showFooter?: boolean
  className?: string
  fullScreen?: boolean // Nouveau prop pour les pages de jeu
}

export default function Layout({
  children,
  showHeader = true,
  showFooter = true,
  className = '',
  fullScreen = false
}: LayoutProps) {
  const viewport = useViewport()

  if (fullScreen) {
    // Layout plein écran pour le jeu (sans header/footer)
    return (
      <div className="h-screen w-screen overflow-hidden bg-background">
        {children}
      </div>
    )
  }

  // Layout adaptatif standard
  const headerHeight = showHeader ? (viewport.isMobile ? '60px' : '80px') : '0px'
  const footerHeight = showFooter ? (viewport.isMobile ? '120px' : '160px') : '0px'

  return (
    <div className="h-screen w-screen overflow-hidden flex flex-col bg-background">
      {showHeader && (
        <Header />
      )}

      <main
        className="flex-1 overflow-auto"
        style={{
          height: `calc(100vh - ${headerHeight} - ${footerHeight})`
        }}
      >
        <div className={`h-full ${className}`}>
          {children}
        </div>
      </main>

      {showFooter && viewport.isDesktop && (
        <Footer />
      )}
    </div>
  )
}
