import { SimpleBoardCase } from '@/types/game'

export const BOARD_CASES: SimpleBoardCase[] = [
  // DÉPART - Naissance
  { id: 0, name: 'NAISSANCE', type: 'start', icon: '👶', price: 0 },
  
  // Enfance et éducation
  { id: 1, name: 'École Primaire', type: 'education', price: 0, icon: '🎒', color: '#87CEEB' },
  { id: 2, name: 'Allocation Familiale', type: 'chance', icon: '👪', price: 0 },
  { id: 3, name: 'Collège', type: 'education', price: 0, icon: '📚', color: '#87CEEB' },
  { id: 4, name: 'Frais de Cantine', type: 'tax', price: 50, icon: '🍽️' },
  { id: 5, name: 'Lycée', type: 'education', price: 0, icon: '🎓', color: '#87CEEB' },
  { id: 6, name: 'Job Étudiant', type: 'job', price: 200, icon: '💼', color: '#90EE90' },
  { id: 7, name: '<PERSON><PERSON>e Étudiante', type: 'chance', icon: '💰', price: 0 },
  { id: 8, name: 'Université', type: 'education', price: 500, icon: '🏛️', color: '#87CEEB' },
  { id: 9, name: 'Stage Non Payé', type: 'job', price: 0, icon: '😤', color: '#FFB6C1' },
  
  // CHÔMAGE
  { id: 10, name: 'CHÔMAGE', type: 'corner', icon: '😔', price: 0 },
  
  // Premiers emplois
  { id: 11, name: 'Premier CDI', type: 'job', price: 1500, icon: '🎉', color: '#90EE90' },
  { id: 12, name: 'Assurance Maladie', type: 'utility', price: 100, icon: '🏥' },
  { id: 13, name: 'CDD Précaire', type: 'job', price: 800, icon: '📝', color: '#FFB6C1' },
  { id: 14, name: 'Freelance', type: 'job', price: 1200, icon: '💻', color: '#DDA0DD' },
  { id: 15, name: 'Transport en Commun', type: 'transport', price: 75, icon: '🚇' },
  { id: 16, name: 'Studio 20m²', type: 'housing', price: 600, icon: '🏠', color: '#F0E68C' },
  { id: 17, name: 'Aide au Logement CAF', type: 'chance', icon: '🏛️', price: 0 },
  { id: 18, name: 'Colocation', type: 'housing', price: 400, icon: '🏡', color: '#F0E68C' },
  { id: 19, name: 'Promotion', type: 'job', price: 2000, icon: '📈', color: '#90EE90' },
  
  // RETRAITE
  { id: 20, name: 'RETRAITE', type: 'corner', icon: '👴', price: 0 },
  
  // Vie adulte
  { id: 21, name: 'Mariage', type: 'life', price: 5000, icon: '💒', color: '#FFB6C1' },
  { id: 22, name: 'Crédit Immobilier', type: 'chance', icon: '🏦', price: 0 },
  { id: 23, name: 'Appartement 3 Pièces', type: 'housing', price: 1200, icon: '🏠', color: '#F0E68C' },
  { id: 24, name: 'Enfant', type: 'life', price: 800, icon: '👶', color: '#FFB6C1' },
  { id: 25, name: 'Voiture', type: 'transport', price: 300, icon: '🚗' },
  { id: 26, name: 'Manager', type: 'job', price: 3000, icon: '👔', color: '#90EE90' },
  { id: 27, name: 'Maison', type: 'housing', price: 1800, icon: '🏘️', color: '#F0E68C' },
  { id: 28, name: 'Assurance Vie', type: 'utility', price: 200, icon: '🛡️' },
  { id: 29, name: 'Directeur', type: 'job', price: 5000, icon: '💼', color: '#90EE90' },
  
  // PRISON (Dettes)
  { id: 30, name: 'SURENDETTEMENT', type: 'corner', icon: '💸', price: 0 },
  
  // Fin de carrière
  { id: 31, name: 'CEO', type: 'job', price: 8000, icon: '👑', color: '#FFD700' },
  { id: 32, name: 'Villa de Luxe', type: 'housing', price: 3000, icon: '🏰', color: '#F0E68C' },
  { id: 33, name: 'Héritage', type: 'chance', icon: '💎', price: 0 },
  { id: 34, name: 'Investisseur', type: 'job', price: 10000, icon: '📊', color: '#FFD700' },
  { id: 35, name: 'Jet Privé', type: 'transport', price: 1000, icon: '✈️' },
  { id: 36, name: 'Crise Financière', type: 'chance', icon: '📉', price: 0 },
  { id: 37, name: 'Château', type: 'housing', price: 5000, icon: '🏰', color: '#F0E68C' },
  { id: 38, name: 'Impôt sur la Fortune', type: 'tax', price: 2000, icon: '💰' },
  { id: 39, name: 'MILLIONNAIRE', type: 'property', price: 15000, icon: '💎', color: '#FFD700' }
]
