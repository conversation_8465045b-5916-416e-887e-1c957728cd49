(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/i18n/locales/en/common.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"appName\":\"TheRateRace.io\",\"welcome\":\"Welcome to TheRateRace.io\",\"description\":\"The satirical board game that parodies the modern race for financial success\",\"navigation\":{\"home\":\"Home\",\"play\":\"Play\",\"profile\":\"Profile\",\"settings\":\"Settings\",\"logout\":\"Logout\"},\"buttons\":{\"login\":\"Login\",\"register\":\"Register\",\"play\":\"Play\",\"create\":\"Create\",\"join\":\"Join\",\"cancel\":\"Cancel\",\"confirm\":\"Confirm\",\"save\":\"Save\",\"back\":\"Back\",\"next\":\"Next\"},\"loading\":\"Loading...\",\"error\":\"An error occurred\",\"success\":\"Success!\",\"language\":\"Language\"}"));}}),
}]);