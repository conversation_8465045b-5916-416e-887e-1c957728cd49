{"name": "theraterace-io", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "clsx": "^2.1.1", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-resources-to-backend": "^1.2.1", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.6.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}