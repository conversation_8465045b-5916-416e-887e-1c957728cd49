'use client'

import { useState } from 'react'
import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import Modal from '@/components/ui/Modal'
import Input from '@/components/ui/Input'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { useGameRooms, useRoomPlayers } from '@/lib/supabase/hooks'
import { CreateRoomData } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

export default function SalonsPage() {
  const viewport = useViewport()
  const { rooms, loading, error, createRoom, joinRoom, addBotsToRoom } = useGameRooms()
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null)
  const { players } = useRoomPlayers(selectedRoom)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [newRoom, setNewRoom] = useState<CreateRoomData>({
    name: '',
    description: '',
    max_players: 4,
    password: ''
  })

  const handleCreateRoom = async () => {
    if (!newRoom.name.trim()) {
      alert('Veuillez entrer un nom pour le salon')
      return
    }

    setIsCreating(true)
    try {
      const roomId = await createRoom(newRoom)
      if (roomId) {
        setShowCreateModal(false)
        setNewRoom({ name: '', description: '', max_players: 4, password: '' })
        setSelectedRoom(roomId)
        console.log('Salon créé avec succès:', roomId)
      }
    } catch (err) {
      console.error('Erreur lors de la création du salon:', err)
      alert('Erreur lors de la création du salon')
    } finally {
      setIsCreating(false)
    }
  }

  const handleJoinRoom = async (roomId: string) => {
    try {
      const success = await joinRoom({ room_id: roomId })
      if (success) {
        setSelectedRoom(roomId)
        console.log('Rejoint le salon:', roomId)
        alert('Salon rejoint avec succès !')
      } else {
        console.error('Échec de la connexion au salon')
        alert('Impossible de rejoindre le salon. Vérifiez que Supabase est démarré.')
      }
    } catch (err) {
      console.error('Erreur lors de la connexion au salon:', err)
      alert(`Erreur lors de la connexion au salon: ${err instanceof Error ? err.message : 'Erreur inconnue'}`)
    }
  }

  const handleStartGameWithBots = async () => {
    if (!selectedRoom) return

    try {
      const botsAdded = await addBotsToRoom(selectedRoom)
      console.log(`${botsAdded} bots ajoutés au salon`)
      alert(`Partie démarrée avec ${botsAdded} bots !`)
    } catch (err) {
      console.error('Erreur lors de l\'ajout des bots:', err)
      alert('Erreur lors de l\'ajout des bots')
    }
  }

  const selectedRoomData = rooms.find(r => r.id === selectedRoom)

  if (loading) {
    return (
      <Layout>
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className={`animate-spin rounded-full border-b-2 border-primary mx-auto ${
              viewport.isMobile ? 'h-16 w-16' : 'h-32 w-32'
            }`}></div>
            <p className={`mt-4 text-muted-foreground ${viewport.isMobile ? 'text-sm' : 'text-base'}`}>
              Chargement des salons...
            </p>
          </div>
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <div className="h-full flex items-center justify-center p-4">
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 text-center max-w-md w-full">
            <h2 className={`font-semibold text-red-400 mb-2 ${viewport.isMobile ? 'text-lg' : 'text-xl'}`}>
              Erreur de connexion
            </h2>
            <p className={`text-red-300 mb-4 ${viewport.isMobile ? 'text-sm' : 'text-base'}`}>
              {error}
            </p>
            <p className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
              Assurez-vous que Supabase est démarré avec <code className="bg-muted px-2 py-1 rounded">supabase start</code>
            </p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="h-full flex flex-col overflow-hidden">
        {/* Header Compact */}
        <div className="flex-shrink-0 p-2 md:p-3 border-b border-border">
          <h1 className={`font-bold text-primary neon-text mb-1 ${
            viewport.isMobile ? 'text-xl' : viewport.isTablet ? 'text-2xl' : 'text-3xl'
          }`}>
            🎮 Salons de Jeu
          </h1>
          <p className={`text-muted-foreground ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
            Créez ou rejoignez un salon pour commencer une partie
          </p>
        </div>

        {/* Contenu Principal */}
        <div className={`flex-1 overflow-hidden ${
          viewport.isMobile ? 'flex flex-col' : 'grid grid-cols-1 lg:grid-cols-2'
        } gap-3 p-2 md:p-3`}>
          
          {/* Liste des salons */}
          <div className={`${viewport.isMobile ? 'flex-1' : ''} flex flex-col overflow-hidden`}>
            <div className="flex-shrink-0 flex justify-between items-center mb-2">
              <h2 className={`font-semibold ${viewport.isMobile ? 'text-base' : 'text-xl'}`}>
                Salons ({rooms.length})
              </h2>
              <Button
                onClick={() => setShowCreateModal(true)}
                variant="primary"
                size={viewport.isMobile ? 'sm' : 'md'}
                neonGlow
              >
                {viewport.isMobile ? '+ Salon' : 'Créer un Salon'}
              </Button>
            </div>
            
            <div className="flex-1 overflow-y-auto space-y-2">
              {/* Modal de création de salon */}
              <Modal 
                isOpen={showCreateModal} 
                onClose={() => setShowCreateModal(false)}
                title="🏗️ Créer un Nouveau Salon"
                size="md"
              >
                <div className="space-y-4">
                  <Input
                    label="Nom du salon"
                    placeholder="Nom du salon *"
                    value={newRoom.name}
                    onChange={(e) => setNewRoom({...newRoom, name: e.target.value})}
                  />
                  
                  <Input
                    label="Description (optionnel)"
                    placeholder="Description du salon"
                    value={newRoom.description || ''}
                    onChange={(e) => setNewRoom({...newRoom, description: e.target.value})}
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Joueurs max
                      </label>
                      <select
                        value={newRoom.max_players}
                        onChange={(e) => setNewRoom({...newRoom, max_players: parseInt(e.target.value)})}
                        className="w-full bg-muted text-foreground border border-border rounded-lg px-3 py-2 focus:border-primary focus:outline-none"
                      >
                        {[2,3,4,5,6,7,8,9,10,11,12].map(n => (
                          <option key={n} value={n}>{n} joueurs</option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <Input
                        label="Mot de passe (optionnel)"
                        type="password"
                        placeholder="Mot de passe"
                        value={newRoom.password || ''}
                        onChange={(e) => setNewRoom({...newRoom, password: e.target.value})}
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <Button 
                      onClick={handleCreateRoom} 
                      variant="primary"
                      disabled={isCreating}
                      className="flex-1"
                    >
                      {isCreating ? (
                        <>
                          <LoadingSpinner size="sm" color="white" className="mr-2" />
                          Création...
                        </>
                      ) : (
                        'Créer le salon'
                      )}
                    </Button>
                    <Button 
                      onClick={() => setShowCreateModal(false)} 
                      variant="outline"
                      className="flex-1"
                    >
                      Annuler
                    </Button>
                  </div>
                </div>
              </Modal>

              {/* Liste des salons */}
              {rooms.length === 0 ? (
                <Card className="text-center p-8">
                  <h3 className="text-lg font-semibold mb-2">Aucun salon disponible</h3>
                  <p className="text-muted-foreground mb-4">Soyez le premier à créer un salon !</p>
                  <Button onClick={() => setShowCreateModal(true)} variant="primary">
                    Créer le premier salon
                  </Button>
                </Card>
              ) : (
                rooms.map((room) => (
                  <Card
                    key={room.id}
                    hover
                    onClick={() => setSelectedRoom(room.id)}
                    className={`cursor-pointer transition-colors ${
                      selectedRoom === room.id ? 'border-primary bg-primary/10' : 'border-border'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg">{room.name}</h3>
                      <span className="text-xs bg-accent text-black px-2 py-1 rounded">
                        {room.current_players}/{room.max_players}
                      </span>
                    </div>
                    {room.description && (
                      <p className="text-muted-foreground text-sm mb-3">{room.description}</p>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-muted-foreground">
                        {room.status === 'waiting' ? '⏳ En attente' : '🎮 En cours'}
                      </span>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleJoinRoom(room.id)
                        }}
                        variant="outline"
                        size="sm"
                      >
                        Rejoindre
                      </Button>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>

          {/* Panneau de détails */}
          <div className={`${viewport.isMobile ? 'flex-shrink-0' : ''} flex flex-col overflow-hidden`}>
            {selectedRoomData ? (
              <Card className="h-full flex flex-col">
                <h3 className="text-xl font-bold mb-4">📋 Détails du Salon</h3>
                
                <div className="flex-1 space-y-4">
                  <div>
                    <h4 className="font-semibold text-lg">{selectedRoomData.name}</h4>
                    {selectedRoomData.description && (
                      <p className="text-muted-foreground">{selectedRoomData.description}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <p>• Joueurs: <span className="text-accent">{selectedRoomData.current_players}/{selectedRoomData.max_players}</span></p>
                    <p>• Status: <span className="text-accent">{selectedRoomData.status}</span></p>
                    <p>• Code: <span className="text-primary">{selectedRoomData.invite_code}</span></p>
                  </div>
                  
                  {players.length > 0 && (
                    <div>
                      <h5 className="font-semibold mb-2">Joueurs connectés:</h5>
                      <div className="space-y-1">
                        {players.map((player) => (
                          <div key={player.id} className="text-sm flex items-center gap-2">
                            <span>{player.is_bot ? '🤖' : '👤'}</span>
                            <span>{player.player_name}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex-shrink-0 pt-4 space-y-2">
                  <Button
                    onClick={handleStartGameWithBots}
                    variant="primary"
                    className="w-full"
                    neonGlow
                  >
                    🤖 Démarrer avec des Bots
                  </Button>
                  <Button
                    onClick={() => window.location.href = '/game'}
                    variant="outline"
                    className="w-full"
                  >
                    🎮 Aller au Jeu
                  </Button>
                </div>
              </Card>
            ) : (
              <Card className="h-full flex items-center justify-center">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Sélectionnez un salon</h3>
                  <p className="text-muted-foreground">Choisissez un salon dans la liste pour voir les détails</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
