'use client'

import Layout from '@/components/layout/Layout'

export default function SalonsPage() {
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-primary neon-text mb-4">
            🎮 Salons de Jeu
          </h1>
          <p className="text-muted">
            Créez ou rejoignez un salon pour commencer une partie de TheRateRace.io
          </p>
        </div>

        <div className="bg-card p-8 rounded-lg border border-border text-center">
          <h2 className="text-2xl font-semibold mb-4">Connexion à Supabase</h2>
          <p className="text-muted mb-4">
            Vérification de la connexion à la base de données...
          </p>
          <div className="bg-green-900/20 border border-green-500 rounded-lg p-4">
            <p className="text-green-400">✅ Supabase est configuré et démarré !</p>
            <p className="text-sm text-muted mt-2">
              Studio: <a href="http://127.0.0.1:55323" target="_blank" className="text-primary hover:underline">http://127.0.0.1:55323</a>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  )
}
