'use client'

import React from 'react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useViewport } from '@/hooks/useViewport'

// Mock data pour la démo
const mockRooms = [
  { id: '1', name: 'Umar', players: 4, maxPlayers: 4, status: 'playing', avatar: '🟢' },
  { id: '2', name: '<PERSON>', players: 3, maxPlayers: 4, status: 'waiting', avatar: '🔵' },
  { id: '3', name: 'papi', players: 2, maxPlayers: 4, status: 'waiting', avatar: '🟡' },
  { id: '4', name: 'qpaqps', players: 1, maxPlayers: 4, status: 'waiting', avatar: '🟠' },
]

export default function SalonsPage() {
  const router = useRouter()
  const viewport = useViewport()
  const [nickname, setNickname] = useState('')
  const [maxPlayers, setMaxPlayers] = useState(4)
  const [isPrivateRoom, setIsPrivateRoom] = useState(false)
  const [allowBots, setAllowBots] = useState(true)
  const [selectedMap, setSelectedMap] = useState('Classic')
  const [shareUrl, setShareUrl] = useState('')
  const [showChat, setShowChat] = useState(false)

  useEffect(() => {
    // Récupérer le pseudo depuis localStorage
    const savedNickname = localStorage.getItem('playerNickname')
    if (savedNickname) {
      setNickname(savedNickname)
    }

    // Générer une URL de partage fictive
    setShareUrl(`https://theraterace.io/room/2i6zt`)
  }, [])

  const handleStartGame = () => {
    router.push('/game')
  }

  const handleBackToHome = () => {
    router.push('/')
  }

  const copyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl)
    alert('URL copiée dans le presse-papiers!')
  }

  return (
    <div className="h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col">
      {/* Header compact */}
      <header className="flex justify-between items-center p-2 z-10 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <button
            onClick={handleBackToHome}
            className="text-white hover:text-purple-300 transition-colors text-sm"
          >
            ← Retour
          </button>
          <span className="text-white font-bold text-lg">TheRateRace.io</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 bg-slate-800 rounded-lg px-2 py-1">
            <span className="text-gray-300 text-xs">Share</span>
            <button
              onClick={copyShareUrl}
              className="bg-slate-700 hover:bg-slate-600 text-white px-2 py-1 rounded text-xs transition-colors"
            >
              📋
            </button>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-gray-300 text-sm">🔊</span>
            <span className="text-gray-300 text-sm">🔍</span>
          </div>
        </div>
      </header>

      {/* Contenu principal */}
      <div className="flex-1 flex overflow-hidden">
        {/* Plateau de jeu central */}
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="relative">
            {/* Plateau de jeu style TheRateRace - taille adaptative */}
            <div className="w-72 h-72 bg-gradient-to-br from-slate-800 to-slate-900 rounded-xl border-4 border-white/10 relative shadow-2xl overflow-hidden">
              {/* Grille du plateau */}
              <div className="grid grid-cols-11 grid-rows-11 gap-0 h-full">

                {/* Ligne du haut */}
                <div className="col-span-11 flex">
                  {Array.from({length: 11}).map((_, i) => (
                    <div key={`top-${i}`} className="flex-1 h-6 border border-white/20 bg-gradient-to-br from-blue-600/30 to-blue-800/30 flex items-center justify-center text-xs">
                      {i === 0 ? '🏠' : i === 10 ? '⚡' : i === 5 ? '💼' : '🏢'}
                    </div>
                  ))}
                </div>

                {/* Lignes du milieu avec cases gauche et droite */}
                {Array.from({ length: 9 }, (_, rowIndex) => (
                  <React.Fragment key={`row-${rowIndex}`}>
                    {/* Case de gauche */}
                    <div className="w-6 h-6 border border-white/20 bg-gradient-to-br from-green-600/30 to-green-800/30 flex items-center justify-center text-xs">
                      📈
                    </div>

                    {/* Centre du plateau */}
                    <div className="col-span-9 bg-gradient-to-br from-purple-900/50 to-slate-900/50 flex items-center justify-center">
                      {rowIndex === 4 && (
                        <div className="text-center">
                          <div className="text-sm font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                            TheRateRace
                          </div>
                          <div className="text-xs text-white/80 font-semibold">
                            .io
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Case de droite */}
                    <div className="w-6 h-6 border border-white/20 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 flex items-center justify-center text-xs">
                      💰
                    </div>
                  </React.Fragment>
                ))}

                {/* Ligne du bas */}
                <div className="col-span-11 flex">
                  {Array.from({length: 11}).map((_, i) => (
                    <div key={`bottom-${i}`} className="flex-1 h-6 border border-white/20 bg-gradient-to-br from-red-600/30 to-red-800/30 flex items-center justify-center text-xs">
                      {i === 0 ? '🎯' : i === 10 ? '🏆' : i === 5 ? '💼' : '🏢'}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Message d'attente */}
            <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 text-center">
              <p className="text-white text-sm">Waiting for 🟢 Umar to start the game...</p>
              <p className="text-gray-400 text-xs mt-1">Joined from 2i6zt</p>
            </div>
          </div>
        </div>

        {/* Panneau des joueurs à droite */}
        <div className="w-64 bg-slate-800 p-3 flex flex-col overflow-y-auto">
          {/* Liste des joueurs */}
          <div className="mb-4">
            <h3 className="text-white font-semibold mb-2 text-sm">Joueurs</h3>
            <div className="space-y-2">
              {mockRooms.map((room) => (
                <div key={room.id} className="flex items-center gap-2 p-2 bg-slate-700 rounded-lg">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                    {room.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-medium text-sm">{room.name}</div>
                    <div className="text-gray-400 text-xs">Case {room.id}</div>
                  </div>
                  <div className="text-yellow-400 font-bold text-xs">1500€</div>
                </div>
              ))}
            </div>
          </div>

          {/* Paramètres de jeu */}
          <div className="mb-4">
            <h3 className="text-white font-semibold mb-2 text-sm">Game settings</h3>
            <div className="space-y-3">
              <div>
                <label className="text-gray-300 text-xs block mb-1">Maximum players</label>
                <select
                  value={maxPlayers}
                  onChange={(e) => setMaxPlayers(Number(e.target.value))}
                  className="w-full bg-slate-700 text-white rounded px-2 py-1 text-xs"
                >
                  <option value={4}>4</option>
                  <option value={6}>6</option>
                  <option value={8}>8</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-xs">Private room</span>
                <button
                  onClick={() => setIsPrivateRoom(!isPrivateRoom)}
                  className={`w-8 h-4 rounded-full transition-colors ${isPrivateRoom ? 'bg-purple-600' : 'bg-gray-600'}`}
                >
                  <div className={`w-3 h-3 bg-white rounded-full transition-transform ${isPrivateRoom ? 'translate-x-4' : 'translate-x-0.5'}`}></div>
                </button>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-xs">Allow bots to join</span>
                <button
                  onClick={() => setAllowBots(!allowBots)}
                  className={`w-8 h-4 rounded-full transition-colors ${allowBots ? 'bg-purple-600' : 'bg-gray-600'}`}
                >
                  <div className={`w-3 h-3 bg-white rounded-full transition-transform ${allowBots ? 'translate-x-4' : 'translate-x-0.5'}`}></div>
                </button>
              </div>
            </div>
          </div>

          {/* Sélection de carte */}
          <div className="mb-4">
            <h3 className="text-white font-semibold mb-2 text-sm">Board map</h3>
            <div className="bg-slate-700 rounded-lg p-2">
              <div className="text-white font-medium text-sm">{selectedMap}</div>
              <button className="text-purple-400 text-xs hover:text-purple-300 transition-colors">
                Browse maps →
              </button>
            </div>
          </div>

          {/* Bouton de démarrage */}
          <button
            onClick={handleStartGame}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm"
          >
            🎮 Démarrer la partie
          </button>
        </div>
      </div>

      {/* Chat (optionnel) */}
      {showChat && (
        <div className="absolute bottom-4 left-4 w-80 h-40 bg-slate-800 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-white font-medium">Chat</h4>
            <button
              onClick={() => setShowChat(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
          <div className="text-gray-400 text-sm">No messages yet</div>
        </div>
      )}

      {/* Bouton chat */}
      <button
        onClick={() => setShowChat(!showChat)}
        className="absolute bottom-4 left-4 bg-slate-700 hover:bg-slate-600 text-white p-3 rounded-lg transition-colors"
      >
        💬
      </button>
    </div>
  )
}
