'use client'

import React from 'react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useViewport } from '@/hooks/useViewport'

// Mock data pour la démo
const mockRooms = [
  { id: '1', name: 'Umar', players: 4, maxPlayers: 4, status: 'playing', avatar: '🟢' },
  { id: '2', name: '<PERSON>', players: 3, maxPlayers: 4, status: 'waiting', avatar: '🔵' },
  { id: '3', name: 'papi', players: 2, maxPlayers: 4, status: 'waiting', avatar: '🟡' },
  { id: '4', name: 'qpaqps', players: 1, maxPlayers: 4, status: 'waiting', avatar: '🟠' },
]

export default function SalonsPage() {
  const router = useRouter()
  const viewport = useViewport()
  const [nickname, setNickname] = useState('')
  const [maxPlayers, setMaxPlayers] = useState(4)
  const [isPrivateRoom, setIsPrivateRoom] = useState(false)
  const [allowBots, setAllowBots] = useState(true)
  const [selectedMap, setSelectedMap] = useState('Classic')
  const [shareUrl, setShareUrl] = useState('')
  const [showChat, setShowChat] = useState(false)

  useEffect(() => {
    // Récupérer le pseudo depuis localStorage
    const savedNickname = localStorage.getItem('playerNickname')
    if (savedNickname) {
      setNickname(savedNickname)
    }

    // Générer une URL de partage fictive
    setShareUrl(`https://theraterace.io/room/2i6zt`)
  }, [])

  const handleStartGame = () => {
    router.push('/game')
  }

  const handleBackToHome = () => {
    router.push('/')
  }

  const copyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl)
    alert('URL copiée dans le presse-papiers!')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 flex justify-between items-center p-4 z-10">
        <div className="flex items-center gap-2">
          <button
            onClick={handleBackToHome}
            className="text-white hover:text-purple-300 transition-colors"
          >
            ← Retour
          </button>
          <span className="text-white font-bold text-xl">TheRateRace.io</span>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 bg-slate-800 rounded-lg px-3 py-2">
            <span className="text-gray-300 text-sm">Share this game</span>
            <button
              onClick={copyShareUrl}
              className="bg-slate-700 hover:bg-slate-600 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              📋 Copy
            </button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-300">🔊</span>
            <span className="text-gray-300">🔍</span>
          </div>
        </div>
      </header>

      {/* Plateau de jeu central */}
      <div className="flex-1 flex items-center justify-center p-20">
        <div className="relative">
          {/* Plateau de jeu style TheRateRace */}
          <div className="w-96 h-96 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border-4 border-white/10 relative shadow-2xl overflow-hidden">
            {/* Grille du plateau */}
            <div className="grid grid-cols-11 grid-rows-11 gap-0 h-full">

              {/* Ligne du haut */}
              <div className="col-span-11 flex">
                {Array.from({length: 11}).map((_, i) => (
                  <div key={`top-${i}`} className="w-8 h-8 border border-white/20 bg-gradient-to-br from-blue-600/30 to-blue-800/30 flex items-center justify-center text-xs">
                    {i === 0 ? '🏠' : i === 10 ? '⚡' : i === 5 ? '💼' : '🏢'}
                  </div>
                ))}
              </div>


              {/* Lignes du milieu avec cases gauche et droite */}
              {Array.from({ length: 9 }, (_, rowIndex) => (
                <React.Fragment key={`row-${rowIndex}`}>
                  {/* Case de gauche */}
                  <div className="w-8 h-8 border border-white/20 bg-gradient-to-br from-green-600/30 to-green-800/30 flex items-center justify-center text-xs">
                    📈
                  </div>

                  {/* Centre du plateau */}
                  <div className="col-span-9 bg-gradient-to-br from-purple-900/50 to-slate-900/50 flex items-center justify-center">
                    {rowIndex === 4 && (
                      <div className="text-center">
                        <div className="text-lg font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                          TheRateRace
                        </div>
                        <div className="text-xs text-white/80 font-semibold">
                          .io
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Case de droite */}
                  <div className="w-8 h-8 border border-white/20 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 flex items-center justify-center text-xs">
                    💰
                  </div>
                </React.Fragment>
              ))}

              {/* Ligne du bas */}
              <div className="col-span-11 flex">
                {Array.from({length: 11}).map((_, i) => (
                  <div key={`bottom-${i}`} className="w-8 h-8 border border-white/20 bg-gradient-to-br from-red-600/30 to-red-800/30 flex items-center justify-center text-xs">
                    {i === 0 ? '🎯' : i === 10 ? '🏆' : i === 5 ? '💼' : '🏢'}
                  </div>
                ))}
              </div>
            </div>
          </div>
                <div></div>
                <div></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
              </div>
            </div>
            <div className="w-16 h-16 bg-white rounded-lg shadow-lg flex items-center justify-center">
              <div className="grid grid-cols-3 gap-1">
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
              </div>
            </div>

          {/* Message d'attente */}
          <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center">
            <p className="text-white text-lg">Waiting for 🟢 Umar to start the game...</p>
            <p className="text-gray-400 text-sm mt-1">Joined from 2i6zt</p>
          </div>
        </div>
      </div>

      {/* Panneau des joueurs à droite */}
      <div className="w-80 bg-slate-800 p-6 flex flex-col">
        {/* Liste des joueurs */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-4">Joueurs</h3>
          <div className="space-y-3">
            {mockRooms.map((room) => (
              <div key={room.id} className="flex items-center gap-3 p-3 bg-slate-700 rounded-lg">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                  {room.avatar}
                </div>
                <div className="flex-1">
                  <div className="text-white font-medium">{room.name}</div>
                  <div className="text-gray-400 text-sm">Case {room.id}</div>
                </div>
                <div className="text-yellow-400 font-bold">1500€</div>
              </div>
            ))}
          </div>
        </div>

        {/* Paramètres de jeu */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-4">Game settings</h3>
          <div className="space-y-4">
            <div>
              <label className="text-gray-300 text-sm block mb-2">Maximum players</label>
              <select
                value={maxPlayers}
                onChange={(e) => setMaxPlayers(Number(e.target.value))}
                className="w-full bg-slate-700 text-white rounded px-3 py-2 text-sm"
              >
                <option value={4}>4</option>
                <option value={6}>6</option>
                <option value={8}>8</option>
              </select>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm">Private room</span>
              <button
                onClick={() => setIsPrivateRoom(!isPrivateRoom)}
                className={`w-10 h-6 rounded-full transition-colors ${isPrivateRoom ? 'bg-purple-600' : 'bg-gray-600'}`}
              >
                <div className={`w-4 h-4 bg-white rounded-full transition-transform ${isPrivateRoom ? 'translate-x-5' : 'translate-x-1'}`}></div>
              </button>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm">Allow bots to join</span>
              <button
                onClick={() => setAllowBots(!allowBots)}
                className={`w-10 h-6 rounded-full transition-colors ${allowBots ? 'bg-purple-600' : 'bg-gray-600'}`}
              >
                <div className={`w-4 h-4 bg-white rounded-full transition-transform ${allowBots ? 'translate-x-5' : 'translate-x-1'}`}></div>
              </button>
            </div>
          </div>
        </div>

        {/* Sélection de carte */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-4">Board map</h3>
          <div className="bg-slate-700 rounded-lg p-3">
            <div className="text-white font-medium">{selectedMap}</div>
            <button className="text-purple-400 text-sm hover:text-purple-300 transition-colors">
              Browse maps →
            </button>
          </div>
        </div>

        {/* Bouton de démarrage */}
        <button
          onClick={handleStartGame}
          className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
        >
          🎮 Démarrer la partie
        </button>
      </div>

      {/* Chat (optionnel) */}
      {showChat && (
        <div className="absolute bottom-4 left-4 w-80 h-40 bg-slate-800 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-white font-medium">Chat</h4>
            <button
              onClick={() => setShowChat(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
          <div className="text-gray-400 text-sm">No messages yet</div>
        </div>
      )}

      {/* Bouton chat */}
      <button
        onClick={() => setShowChat(!showChat)}
        className="absolute bottom-4 left-4 bg-slate-700 hover:bg-slate-600 text-white p-3 rounded-lg transition-colors"
      >
        💬
      </button>
    </div>
  )
}
