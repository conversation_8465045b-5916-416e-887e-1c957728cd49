'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
import { useGameRooms, useRoomPlayers } from '@/lib/supabase/hooks'
import { CreateRoomData } from '@/types/game'

export default function SalonsPage() {
  const { rooms, loading, error, createRoom, joinRoom, addBotsToRoom } = useGameRooms()
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null)
  const { players } = useRoomPlayers(selectedRoom)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newRoom, setNewRoom] = useState<CreateRoomData>({
    name: '',
    description: '',
    max_players: 4,
    password: ''
  })

  const handleCreateRoom = async () => {
    if (!newRoom.name.trim()) {
      alert('Veuillez entrer un nom pour le salon')
      return
    }

    try {
      const roomId = await createRoom(newRoom)
      if (roomId) {
        setShowCreateForm(false)
        setNewRoom({ name: '', description: '', max_players: 4, password: '' })
        setSelectedRoom(roomId)
        console.log('Salon créé avec succès:', roomId)
      }
    } catch (err) {
      console.error('Erreur lors de la création du salon:', err)
      alert('Erreur lors de la création du salon')
    }
  }

  const handleJoinRoom = async (roomId: string) => {
    try {
      const success = await joinRoom({ room_id: roomId })
      if (success) {
        setSelectedRoom(roomId)
        console.log('Rejoint le salon:', roomId)
      }
    } catch (err) {
      console.error('Erreur lors de la connexion au salon:', err)
      alert('Erreur lors de la connexion au salon')
    }
  }

  const handleStartGameWithBots = async () => {
    if (!selectedRoom) return

    try {
      const botsAdded = await addBotsToRoom(selectedRoom)
      console.log(`${botsAdded} bots ajoutés au salon`)
      alert(`Partie démarrée avec ${botsAdded} bots !`)
    } catch (err) {
      console.error('Erreur lors de l\'ajout des bots:', err)
      alert('Erreur lors de l\'ajout des bots')
    }
  }

  const selectedRoomData = rooms.find(r => r.id === selectedRoom)

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted">Chargement des salons...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-red-400 mb-2">Erreur de connexion</h2>
            <p className="text-red-300 mb-4">{error}</p>
            <p className="text-sm text-muted">
              Assurez-vous que Supabase est démarré avec <code className="bg-muted px-2 py-1 rounded">supabase start</code>
            </p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-primary neon-text mb-4">
            🎮 Salons de Jeu
          </h1>
          <p className="text-muted">
            Créez ou rejoignez un salon pour commencer une partie de TheRateRace.io
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Liste des salons */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Salons Disponibles ({rooms.length})</h2>
              <Button
                onClick={() => setShowCreateForm(true)}
                variant="primary"
                neonGlow
              >
                Créer un Salon
              </Button>
            </div>

            {/* Formulaire de création */}
            {showCreateForm && (
              <div className="bg-card p-6 rounded-lg border border-border">
                <h3 className="text-lg font-semibold mb-4">Nouveau Salon</h3>
                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Nom du salon *"
                    value={newRoom.name}
                    onChange={(e) => setNewRoom({...newRoom, name: e.target.value})}
                    className="w-full bg-muted text-foreground border border-border rounded px-3 py-2 focus:border-primary focus:outline-none"
                  />
                  <textarea
                    placeholder="Description (optionnel)"
                    value={newRoom.description}
                    onChange={(e) => setNewRoom({...newRoom, description: e.target.value})}
                    className="w-full bg-muted text-foreground border border-border rounded px-3 py-2 focus:border-primary focus:outline-none"
                    rows={3}
                  />
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="block text-sm text-muted mb-1">Joueurs max</label>
                      <select
                        value={newRoom.max_players}
                        onChange={(e) => setNewRoom({...newRoom, max_players: parseInt(e.target.value)})}
                        className="w-full bg-muted text-foreground border border-border rounded px-3 py-2 focus:border-primary focus:outline-none"
                      >
                        {[2,3,4,5,6,7,8,9,10,11,12].map(n => (
                          <option key={n} value={n}>{n} joueurs</option>
                        ))}
                      </select>
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm text-muted mb-1">Mot de passe (optionnel)</label>
                      <input
                        type="password"
                        placeholder="Mot de passe"
                        value={newRoom.password}
                        onChange={(e) => setNewRoom({...newRoom, password: e.target.value})}
                        className="w-full bg-muted text-foreground border border-border rounded px-3 py-2 focus:border-primary focus:outline-none"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleCreateRoom} variant="primary">
                      Créer
                    </Button>
                    <Button onClick={() => setShowCreateForm(false)} variant="outline">
                      Annuler
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Liste des salons */}
            <div className="space-y-4">
              {rooms.length === 0 ? (
                <div className="bg-card p-8 rounded-lg border border-border text-center">
                  <h3 className="text-lg font-semibold mb-2">Aucun salon disponible</h3>
                  <p className="text-muted mb-4">Soyez le premier à créer un salon !</p>
                  <Button onClick={() => setShowCreateForm(true)} variant="primary">
                    Créer le premier salon
                  </Button>
                </div>
              ) : (
                rooms.map((room) => (
                  <div
                    key={room.id}
                    className={`bg-card p-4 rounded-lg border transition-colors cursor-pointer hover:border-primary/50 ${
                      selectedRoom === room.id ? 'border-primary' : 'border-border'
                    }`}
                    onClick={() => setSelectedRoom(room.id)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg">{room.name}</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-accent text-black px-2 py-1 rounded">
                          {room.current_players}/{room.max_players}
                        </span>
                        {room.password_hash && (
                          <span className="text-xs bg-secondary text-black px-2 py-1 rounded">🔒</span>
                        )}
                      </div>
                    </div>
                    {room.description && (
                      <p className="text-muted text-sm mb-3">{room.description}</p>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-muted">
                        Code: {room.invite_code}
                      </span>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleJoinRoom(room.id)
                        }}
                        disabled={room.current_players >= room.max_players}
                      >
                        {room.current_players >= room.max_players ? 'Complet' : 'Rejoindre'}
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Détails du salon sélectionné */}
          <div className="space-y-6">
            {selectedRoom && selectedRoomData ? (
              <>
                <h2 className="text-2xl font-semibold">Salon: {selectedRoomData.name}</h2>

                <div className="bg-card p-6 rounded-lg border border-border">
                  <h3 className="text-lg font-semibold mb-4">
                    Joueurs dans le salon ({players.length}/{selectedRoomData.max_players})
                  </h3>
                  <div className="space-y-3">
                    {players.map((player) => (
                      <div key={player.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${player.is_bot ? 'bg-secondary' : 'bg-primary'}`}></div>
                          <span>{player.player_name}</span>
                          {player.is_bot && (
                            <span className="text-xs bg-muted px-2 py-1 rounded">BOT</span>
                          )}
                        </div>
                        <span className="text-sm text-muted">Joueur {player.player_index + 1}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 space-y-3">
                    <Button
                      onClick={handleStartGameWithBots}
                      variant="primary"
                      neonGlow
                      className="w-full"
                      disabled={players.length === 0}
                    >
                      🎲 Démarrer la Partie (avec bots)
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const inviteLink = `${window.location.origin}/invite/${selectedRoomData.invite_code}`
                        navigator.clipboard.writeText(inviteLink)
                        alert('Lien d\'invitation copié !')
                      }}
                    >
                      📋 Copier le lien d'invitation
                    </Button>
                  </div>
                </div>

                <div className="bg-card p-4 rounded-lg border border-border">
                  <h4 className="font-semibold mb-2">Informations du salon:</h4>
                  <div className="text-sm text-muted space-y-1">
                    <p>• Status: <span className="text-accent">{selectedRoomData.status}</span></p>
                    <p>• Code d'invitation: <span className="text-primary">{selectedRoomData.invite_code}</span></p>
                    <p>• Créé le: {new Date(selectedRoomData.created_at).toLocaleString('fr-FR')}</p>
                    {selectedRoomData.password_hash && <p>• 🔒 Salon protégé par mot de passe</p>}
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-card p-8 rounded-lg border border-border text-center">
                <h3 className="text-lg font-semibold mb-2">Sélectionnez un salon</h3>
                <p className="text-muted">Choisissez un salon dans la liste pour voir les détails</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
