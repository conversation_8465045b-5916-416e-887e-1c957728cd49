@import "tailwindcss";

:root {
  /* TheRateRace.io - Thème Sophistiqué Universel */
  --background: #0f1419;
  --foreground: #ffffff;
  --primary: #3b82f6; /* Bleu moderne */
  --secondary: #f59e0b; /* Or/Ambre */
  --accent: #10b981; /* Vert é<PERSON>aude */
  --muted: #374151;
  --muted-foreground: #9ca3af;
  --border: #374151;
  --card: #1f2937;
  --destructive: #ef4444;
  --warning: #f59e0b;
  --success: #10b981;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-destructive: var(--destructive);
  --color-warning: var(--warning);
  --color-success: var(--success);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
}

.neon-text {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
}

/* Game board animations */
@keyframes pulse-neon {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite;
}

/* Nouvelles animations pour l'UI moderne */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Classes utilitaires */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation: fade-in 0.2s ease-out;
}

.slide-in-from-top-1 {
  animation: slide-in-from-top 0.2s ease-out;
}

.zoom-in-95 {
  animation: zoom-in 0.2s ease-out;
}

/* Effets de verre */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Améliorations pour les boutons */
.btn-hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Effets de focus améliorés */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background;
}

/* Gradients personnalisés */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.gradient-card {
  background: linear-gradient(135deg, var(--card), rgba(255, 255, 255, 0.05));
}
