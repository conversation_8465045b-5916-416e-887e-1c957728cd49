@import "tailwindcss";

:root {
  /* TheRateRace.io Dark Theme Colors */
  --background: #1a1a1a;
  --foreground: #ffffff;
  --primary: #ff0080; /* Magenta */
  --secondary: #00ffff; /* Cyan */
  --accent: #00ff00; /* <PERSON>e <PERSON> */
  --muted: #404040;
  --border: #333333;
  --card: #2a2a2a;
  --destructive: #ff4444;
  --warning: #ffaa00;
  --success: #00ff88;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-destructive: var(--destructive);
  --color-warning: var(--warning);
  --color-success: var(--success);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
}

.neon-text {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
}

/* Game board animations */
@keyframes pulse-neon {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite;
}
