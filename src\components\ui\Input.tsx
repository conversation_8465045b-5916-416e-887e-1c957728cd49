import React from 'react'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  icon?: React.ReactNode
  variant?: 'default' | 'filled' | 'outlined'
}

export default function Input({ 
  label, 
  error, 
  icon, 
  variant = 'default',
  className = '', 
  ...props 
}: InputProps) {
  const baseClasses = 'w-full px-4 py-3 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50'
  
  const variantClasses = {
    default: 'bg-muted border border-border text-foreground placeholder:text-muted-foreground focus:border-primary',
    filled: 'bg-muted/50 border-0 text-foreground placeholder:text-muted-foreground focus:bg-muted',
    outlined: 'bg-transparent border-2 border-border text-foreground placeholder:text-muted-foreground focus:border-primary'
  }
  
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/50' : ''
  
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-foreground">
          {label}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {icon}
          </div>
        )}
        
        <input
          className={`${baseClasses} ${variantClasses[variant]} ${errorClasses} ${icon ? 'pl-10' : ''} ${className}`}
          {...props}
        />
      </div>
      
      {error && (
        <p className="text-sm text-red-500 animate-in slide-in-from-top-1 duration-200">
          {error}
        </p>
      )}
    </div>
  )
}
