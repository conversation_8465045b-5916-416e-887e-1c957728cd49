name: deno

on:
  push:
    branches:
      - main

jobs:
  test:
    name: Test on deno ${{ matrix.deno }} and ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        deno: [ '1.x' ]
        # os: [ubuntu-latest, windows-latest, macOS-latest]
        os: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v2
      - name: Setup Deno
        uses: denolib/setup-deno@master
        with:
          deno-version: ${{ matrix.deno }}
      - run: deno --version
      - run: deno test test/deno/*.ts --reload --no-check
