'use client'

import { useState, useEffect } from 'react'
import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
// import { createClient } from '@/lib/supabase/client'

export default function TestDbPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [rooms, setRooms] = useState<any[]>([])

  const testConnection = async () => {
    setStatus('loading')
    setMessage('Test de connexion en cours...')

    try {
      // Simulation pour tester l'interface
      await new Promise(resolve => setTimeout(resolve, 1000))

      setRooms([])
      setStatus('success')
      setMessage(`✅ Interface fonctionnelle ! Prêt pour les tests Supabase`)

    } catch (error: any) {
      setStatus('error')
      setMessage(`❌ ${error.message}`)
      console.error('Erreur de test:', error)
    }
  }

  const createTestRoom = async () => {
    try {
      alert('Fonction de création désactivée pour les tests')
    } catch (error: any) {
      alert(`Erreur: ${error.message}`)
      console.error('Erreur de création:', error)
    }
  }

  useEffect(() => {
    testConnection()
  }, [])

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-primary neon-text mb-4">
            🧪 Test Base de Données
          </h1>
          <p className="text-muted">
            Test de connexion à Supabase et aux fonctionnalités de base
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Status de connexion */}
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg border border-border">
              <h2 className="text-xl font-semibold mb-4">Status de Connexion</h2>
              
              <div className={`p-4 rounded-lg border ${
                status === 'loading' ? 'bg-blue-900/20 border-blue-500' :
                status === 'success' ? 'bg-green-900/20 border-green-500' :
                'bg-red-900/20 border-red-500'
              }`}>
                <p className={`${
                  status === 'loading' ? 'text-blue-400' :
                  status === 'success' ? 'text-green-400' :
                  'text-red-400'
                }`}>
                  {message}
                </p>
              </div>

              <div className="mt-4 space-y-2">
                <Button onClick={testConnection} variant="primary" className="w-full">
                  🔄 Retester la Connexion
                </Button>
                <Button onClick={createTestRoom} variant="outline" className="w-full">
                  ➕ Créer un Salon Test
                </Button>
              </div>

              <div className="mt-4 text-sm text-muted">
                <p><strong>Configuration actuelle:</strong></p>
                <p>• URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
                <p>• Studio: <a href="http://127.0.0.1:55323" target="_blank" className="text-primary hover:underline">http://127.0.0.1:55323</a></p>
              </div>
            </div>
          </div>

          {/* Liste des salons */}
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg border border-border">
              <h2 className="text-xl font-semibold mb-4">Salons Existants ({rooms.length})</h2>
              
              {rooms.length === 0 ? (
                <p className="text-muted text-center py-8">Aucun salon trouvé</p>
              ) : (
                <div className="space-y-3">
                  {rooms.map((room) => (
                    <div key={room.id} className="bg-muted p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold">{room.name}</h3>
                          {room.description && (
                            <p className="text-sm text-muted">{room.description}</p>
                          )}
                        </div>
                        <div className="text-xs bg-accent text-black px-2 py-1 rounded">
                          {room.current_players || 0}/{room.max_players}
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-muted">
                        <p>Code: {room.invite_code}</p>
                        <p>Créé: {new Date(room.created_at).toLocaleString('fr-FR')}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
