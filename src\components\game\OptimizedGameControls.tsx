import React from 'react'
import { SimplePlayer, SimpleBoardCase } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'
import Dice from '@/components/game/Dice'

interface OptimizedGameControlsProps {
  currentPlayer: SimplePlayer
  allPlayers: SimplePlayer[]
  currentCase: SimpleBoardCase
  diceValues: [number, number]
  isRolling: boolean
  isMyTurn: boolean
  gameMessage: string
  onRollDice: () => void
  onBuyProperty: () => void
  onEndTurn: () => void
  onQuitGame: () => void
}

export default function OptimizedGameControls({
  currentPlayer,
  allPlayers,
  currentCase,
  diceValues,
  isRolling,
  isMyTurn,
  gameMessage,
  onRollDice,
  onBuyProperty,
  onEndTurn,
  onQuitGame
}: OptimizedGameControlsProps) {
  const viewport = useViewport()

  const canBuyProperty = currentCase.type === 'property' && currentCase.price && 
                        currentPlayer.money >= currentCase.price && isMyTurn

  const getDiceTotal = () => diceValues[0] + diceValues[1]

  return (
    <div className={`
      ${viewport.isMobile ? 'space-y-2' : 'space-y-4'}
      ${viewport.isMobile ? 'p-2' : 'p-4'}
      bg-gradient-to-br from-slate-800 to-slate-900 
      rounded-xl border-2 border-white/10 shadow-xl
    `}>
      
      {/* Informations du joueur actuel */}
      <div className="text-center space-y-2">
        <div className={`
          font-bold text-white
          ${viewport.isMobile ? 'text-sm' : 'text-lg'}
        `}>
          {isMyTurn ? 'Votre tour' : `Tour de ${currentPlayer.name}`}
        </div>
        
        <div className="flex items-center justify-center space-x-2">
          <div 
            className={`
              ${viewport.isMobile ? 'w-6 h-6' : 'w-8 h-8'}
              rounded-full border-2 border-white flex items-center justify-center
              ${isMyTurn ? 'ring-2 ring-yellow-400 animate-pulse' : ''}
            `}
            style={{ backgroundColor: currentPlayer.color }}
          >
            <span className={viewport.isMobile ? 'text-xs' : 'text-sm'}>
              {currentPlayer.avatar}
            </span>
          </div>
          <div className="text-white">
            <div className={`font-semibold ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
              {currentPlayer.name}
            </div>
            <div className={`text-green-400 font-bold ${viewport.isMobile ? 'text-xs' : 'text-sm'}`}>
              {currentPlayer.money}€
            </div>
          </div>
        </div>
      </div>

      {/* Dés */}
      <div className="flex justify-center space-x-3">
        <Dice 
          value={diceValues[0]} 
          isRolling={isRolling} 
          size={viewport.isMobile ? "sm" : "md"}
        />
        <Dice 
          value={diceValues[1]} 
          isRolling={isRolling} 
          size={viewport.isMobile ? "sm" : "md"}
        />
      </div>

      {/* Total des dés */}
      {(diceValues[0] > 0 || diceValues[1] > 0) && (
        <div className="text-center">
          <div className={`
            font-bold text-yellow-400
            ${viewport.isMobile ? 'text-lg' : 'text-2xl'}
          `}>
            Total: {getDiceTotal()}
          </div>
          {diceValues[0] === diceValues[1] && (
            <div className={`
              text-orange-400 font-semibold animate-pulse
              ${viewport.isMobile ? 'text-xs' : 'text-sm'}
            `}>
              🎲 Double ! Rejouez !
            </div>
          )}
        </div>
      )}

      {/* Message de jeu */}
      <div className={`
        text-center text-white/80 
        ${viewport.isMobile ? 'text-xs' : 'text-sm'}
        min-h-[2rem] flex items-center justify-center
      `}>
        {gameMessage}
      </div>

      {/* Actions disponibles */}
      <div className="space-y-2">
        
        {/* Bouton lancer les dés */}
        {isMyTurn && !isRolling && (diceValues[0] === 0 || diceValues[0] === diceValues[1]) && (
          <button
            onClick={onRollDice}
            className={`
              w-full bg-gradient-to-r from-blue-600 to-blue-700 
              hover:from-blue-500 hover:to-blue-600 
              text-white font-bold rounded-lg transition-all duration-300 
              transform hover:scale-105 active:scale-95
              ${viewport.isMobile ? 'py-2 text-sm' : 'py-3 text-lg'}
              shadow-lg hover:shadow-xl
            `}
          >
            🎲 Lancer les dés
          </button>
        )}

        {/* Bouton acheter propriété */}
        {canBuyProperty && (
          <button
            onClick={onBuyProperty}
            className={`
              w-full bg-gradient-to-r from-green-600 to-green-700 
              hover:from-green-500 hover:to-green-600 
              text-white font-bold rounded-lg transition-all duration-300 
              transform hover:scale-105 active:scale-95
              ${viewport.isMobile ? 'py-2 text-sm' : 'py-3 text-lg'}
              shadow-lg hover:shadow-xl
            `}
          >
            💰 Acheter {currentCase.name} ({currentCase.price}€)
          </button>
        )}

        {/* Bouton terminer le tour */}
        {isMyTurn && diceValues[0] > 0 && diceValues[0] !== diceValues[1] && (
          <button
            onClick={onEndTurn}
            className={`
              w-full bg-gradient-to-r from-purple-600 to-purple-700 
              hover:from-purple-500 hover:to-purple-600 
              text-white font-bold rounded-lg transition-all duration-300 
              transform hover:scale-105 active:scale-95
              ${viewport.isMobile ? 'py-2 text-sm' : 'py-3 text-lg'}
              shadow-lg hover:shadow-xl
            `}
          >
            ✅ Terminer le tour
          </button>
        )}
      </div>

      {/* Informations sur la case actuelle */}
      <div className={`
        bg-black/20 rounded-lg p-2 border border-white/10
        ${viewport.isMobile ? 'text-xs' : 'text-sm'}
      `}>
        <div className="text-center text-white">
          <div className="flex items-center justify-center space-x-2 mb-1">
            <span className={viewport.isMobile ? 'text-sm' : 'text-lg'}>
              {currentCase.icon}
            </span>
            <span className="font-semibold">{currentCase.name}</span>
          </div>
          
          {currentCase.price && currentCase.price > 0 && (
            <div className="text-yellow-400 font-bold">
              Prix: {currentCase.price}€
            </div>
          )}
          
          <div className="text-white/60 capitalize">
            {currentCase.type === 'property' && 'Propriété'}
            {currentCase.type === 'chance' && 'Carte Opportunité'}
            {currentCase.type === 'tax' && 'Taxe'}
            {currentCase.type === 'start' && 'Case Départ'}
            {currentCase.type === 'corner' && 'Case Spéciale'}
            {currentCase.type === 'utility' && 'Service Public'}
            {currentCase.type === 'railroad' && 'Transport'}
          </div>
        </div>
      </div>

      {/* Liste des joueurs */}
      <div className={`
        bg-black/20 rounded-lg p-2 border border-white/10
        ${viewport.isMobile ? 'text-xs' : 'text-sm'}
      `}>
        <div className="text-white font-semibold mb-2 text-center">
          Joueurs
        </div>
        <div className="space-y-1">
          {allPlayers.map((player, index) => (
            <div 
              key={player.id}
              className={`
                flex items-center justify-between p-1 rounded
                ${player.id === currentPlayer.id ? 'bg-white/10' : ''}
              `}
            >
              <div className="flex items-center space-x-2">
                <div 
                  className={`
                    ${viewport.isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                    rounded-full border border-white flex items-center justify-center
                  `}
                  style={{ backgroundColor: player.color }}
                >
                  <span className="text-xs">{player.avatar}</span>
                </div>
                <span className="text-white truncate">
                  {player.name}
                  {player.isBot && ' 🤖'}
                </span>
              </div>
              <span className="text-green-400 font-bold">
                {player.money}€
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Bouton quitter */}
      <button
        onClick={onQuitGame}
        className={`
          w-full bg-gradient-to-r from-red-600 to-red-700 
          hover:from-red-500 hover:to-red-600 
          text-white font-medium rounded-lg transition-all duration-300 
          ${viewport.isMobile ? 'py-2 text-sm' : 'py-3 text-base'}
          shadow-lg hover:shadow-xl
        `}
      >
        🚪 Quitter la partie
      </button>
    </div>
  )
}
