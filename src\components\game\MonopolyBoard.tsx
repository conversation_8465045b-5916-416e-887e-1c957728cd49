'use client'

import React from 'react'
import { useViewport } from '@/hooks/useViewport'
import { SimpleBoardCase } from '@/types/game'
import CenterDice from './CenterDice'

interface MonopolyBoardProps {
  squares: SimpleBoardCase[]
  playerPositions: { [playerId: string]: number }
  currentPlayer: string
  diceValues: [number, number]
  isRolling: boolean
}

export const MonopolyBoard: React.FC<MonopolyBoardProps> = ({
  squares,
  playerPositions,
  currentPlayer,
  diceValues,
  isRolling
}) => {
  const viewport = useViewport()

  // Organiser les cases en bordure du plateau (style Monopoly)
  const bottomRow = squares.slice(0, 11) // Cases 0-10 (bas)
  const rightColumn = squares.slice(11, 20) // Cases 11-19 (droite)
  const topRow = squares.slice(20, 31).reverse() // Cases 20-30 (haut, inversé)
  const leftColumn = squares.slice(31, 40).reverse() // Cases 31-39 (gauche, inversé)

  const squareSize = viewport.isMobile ? 'w-12 h-16' : viewport.isTablet ? 'w-16 h-20' : 'w-20 h-24'
  const cornerSize = viewport.isMobile ? 'w-12 h-16' : viewport.isTablet ? 'w-16 h-20' : 'w-20 h-24'

  const renderSquare = (square: SimpleBoardCase, index: number, position: 'bottom' | 'right' | 'top' | 'left') => {
    const playersOnSquare = Object.entries(playerPositions).filter(([_, pos]) => pos === index)
    
    let rotationClass = ''
    let textRotation = ''
    
    switch (position) {
      case 'right':
        rotationClass = 'rotate-90'
        textRotation = '-rotate-90'
        break
      case 'top':
        rotationClass = 'rotate-180'
        textRotation = 'rotate-180'
        break
      case 'left':
        rotationClass = '-rotate-90'
        textRotation = 'rotate-90'
        break
    }

    return (
      <div
        key={index}
        className={`${squareSize} border-2 border-slate-600 bg-gradient-to-br from-slate-100 to-slate-200 relative overflow-hidden ${rotationClass}`}
      >
        {/* Couleur de propriété */}
        {square.color && (
          <div
            className="absolute top-0 left-0 right-0 h-3"
            style={{ backgroundColor: square.color }}
          />
        )}

        {/* Contenu de la case */}
        <div className={`p-1 h-full flex flex-col justify-between ${textRotation}`}>
          <div className={`text-center ${viewport.isMobile ? 'text-[6px]' : viewport.isTablet ? 'text-[8px]' : 'text-xs'} font-bold text-slate-800 leading-tight`}>
            {square.name}
          </div>

          {square.price && (
            <div className={`text-center ${viewport.isMobile ? 'text-[5px]' : viewport.isTablet ? 'text-[6px]' : 'text-[8px]'} text-slate-600 font-semibold`}>
              {square.price}€
            </div>
          )}
        </div>

        {/* Joueurs sur cette case */}
        {playersOnSquare.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 flex justify-center items-end space-x-0.5 p-0.5">
            {playersOnSquare.map(([playerId], playerIndex) => {
              const colors = ['bg-blue-500', 'bg-red-500', 'bg-green-500', 'bg-yellow-500']
              const colorIndex = parseInt(playerId) % colors.length
              return (
                <div
                  key={playerId}
                  className={`${colors[colorIndex]} rounded-full ${viewport.isMobile ? 'w-2 h-2' : 'w-3 h-3'} border border-white ${playerId === currentPlayer ? 'ring-2 ring-white' : ''}`}
                  style={{
                    transform: `translateX(${playerIndex * 2}px)`,
                    zIndex: 10 + playerIndex
                  }}
                />
              )
            })}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full h-full flex items-center justify-center p-4">
      <div className="relative bg-gradient-to-br from-emerald-800 via-emerald-900 to-slate-900 rounded-lg shadow-2xl border-4 border-slate-700">
        {/* Structure du plateau Monopoly */}
        <div className="grid grid-cols-11 grid-rows-11 gap-0">
          {/* Ligne du haut */}
          <div className="col-span-11 flex">
            {topRow.map((square, index) => renderSquare(square, index, 'top'))}
          </div>

          {/* Lignes du milieu */}
          {Array.from({ length: 9 }, (_, rowIndex) => (
            <React.Fragment key={rowIndex}>
              {/* Colonne de gauche */}
              <div className="flex">
                {leftColumn[rowIndex] && renderSquare(leftColumn[rowIndex], rowIndex, 'left')}
              </div>

              {/* Centre du plateau */}
              <div className="col-span-9 flex items-center justify-center bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 relative">
                {/* Logo au centre */}
                {rowIndex === 4 && (
                  <div className="text-center relative z-10">
                    <div className={`font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 drop-shadow-lg ${viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-4xl'}`}>
                      TheRateRace
                    </div>
                    <div className={`text-white/80 font-semibold ${viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-lg'}`}>
                      .io
                    </div>
                    <div className={`text-white/50 font-medium ${viewport.isMobile ? 'text-[8px]' : viewport.isTablet ? 'text-xs' : 'text-sm'} mt-1`}>
                      Jeu de plateau satirique
                    </div>
                  </div>
                )}
              </div>

              {/* Colonne de droite */}
              <div className="flex">
                {rightColumn[rowIndex] && renderSquare(rightColumn[rowIndex], rowIndex, 'right')}
              </div>
            </React.Fragment>
          ))}

          {/* Ligne du bas */}
          <div className="col-span-11 flex">
            {bottomRow.map((square, index) => renderSquare(square, index, 'bottom'))}
          </div>
        </div>

        {/* Dés au centre du plateau */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
          <CenterDice
            diceValues={diceValues}
            isRolling={isRolling}
          />
        </div>
      </div>
    </div>
  )
}
