'use client'

import React from 'react'
import { useViewport } from '@/hooks/useViewport'
import { SimpleBoardCase } from '@/types/game'
import CenterDice from './CenterDice'

interface MonopolyBoardProps {
  squares: SimpleBoardCase[]
  playerPositions: { [playerId: string]: number }
  currentPlayer: string
  diceValues: [number, number]
  isRolling: boolean
}

export const MonopolyBoard: React.FC<MonopolyBoardProps> = ({
  squares,
  playerPositions,
  currentPlayer,
  diceValues,
  isRolling
}) => {
  const viewport = useViewport()

  // Taille du plateau responsive
  const getBoardSize = () => {
    if (viewport.isMobile) return 'w-[95vw] h-[95vw] max-w-[400px] max-h-[400px]'
    if (viewport.isTablet) return 'w-[85vw] h-[85vw] max-w-[600px] max-h-[600px]'
    return 'w-[75vw] h-[75vw] max-w-[700px] max-h-[700px]'
  }

  // Taille des cases
  const getCaseSize = () => {
    if (viewport.isMobile) return { width: '10%', height: '10%' }
    if (viewport.isTablet) return { width: '10%', height: '10%' }
    return { width: '10%', height: '10%' }
  }

  // Organiser les cases selon la disposition Monopoly classique
  const getSquarePosition = (index: number) => {
    const caseSize = getCaseSize()

    // Coin inférieur droit - DÉPART (case 0)
    if (index === 0) return { bottom: '0%', right: '0%', width: '10%', height: '10%' }

    // Côté bas (cases 1-9, de droite à gauche)
    if (index >= 1 && index <= 9) {
      return { bottom: '0%', right: `${(10 - index) * 10}%`, width: '10%', height: '10%' }
    }

    // Coin inférieur gauche - PRISON (case 10)
    if (index === 10) return { bottom: '0%', left: '0%', width: '10%', height: '10%' }

    // Côté gauche (cases 11-19, de bas en haut)
    if (index >= 11 && index <= 19) {
      return { left: '0%', bottom: `${(index - 10) * 10}%`, width: '10%', height: '10%' }
    }

    // Coin supérieur gauche - PARC GRATUIT (case 20)
    if (index === 20) return { top: '0%', left: '0%', width: '10%', height: '10%' }

    // Côté haut (cases 21-29, de gauche à droite)
    if (index >= 21 && index <= 29) {
      return { top: '0%', left: `${(index - 20) * 10}%`, width: '10%', height: '10%' }
    }

    // Coin supérieur droit - ALLEZ EN PRISON (case 30)
    if (index === 30) return { top: '0%', right: '0%', width: '10%', height: '10%' }

    // Côté droit (cases 31-39, de haut en bas)
    if (index >= 31 && index <= 39) {
      return { right: '0%', top: `${(index - 30) * 10}%`, width: '10%', height: '10%' }
    }

    return { bottom: '0%', right: '0%', width: '10%', height: '10%' }
  }

  // Fonction pour obtenir l'orientation du texte selon la position
  const getTextOrientation = (index: number) => {
    if (index >= 0 && index <= 10) return 'rotate-0' // Côté bas
    if (index >= 11 && index <= 20) return 'rotate-90' // Côté gauche
    if (index >= 21 && index <= 30) return 'rotate-180' // Côté haut
    if (index >= 31 && index <= 39) return 'rotate-270' // Côté droit
    return 'rotate-0'
  }

  const renderSquare = (square: SimpleBoardCase, index: number) => {
    const playersOnSquare = Object.entries(playerPositions).filter(([_, pos]) => pos === index)
    const position = getSquarePosition(index)
    const textOrientation = getTextOrientation(index)
    const isCorner = [0, 10, 20, 30].includes(index)

    // Couleurs spéciales pour les coins
    const getCornerColor = (idx: number) => {
      switch (idx) {
        case 0: return 'bg-red-600' // DÉPART
        case 10: return 'bg-orange-600' // PRISON
        case 20: return 'bg-green-600' // PARC GRATUIT
        case 30: return 'bg-red-700' // ALLEZ EN PRISON
        default: return 'bg-white'
      }
    }

    return (
      <div
        key={index}
        className={`
          absolute border-2 border-gray-800
          ${isCorner ? getCornerColor(index) : 'bg-white'}
          flex flex-col items-center justify-center
          cursor-pointer hover:brightness-110 transition-all
          ${textOrientation}
        `}
        style={{
          ...position,
          fontSize: viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'
        }}
      >
        {/* Bande de couleur pour les propriétés */}
        {square.color && !isCorner && (
          <div
            className="absolute top-0 left-0 right-0 h-[20%]"
            style={{ backgroundColor: square.color }}
          />
        )}

        {/* Contenu de la case */}
        <div className="p-1 h-full flex flex-col justify-center items-center text-center">
          <div className={`font-bold ${isCorner ? 'text-white' : 'text-gray-800'} leading-tight`}>
            {square.name}
          </div>
          {square.price && (
            <div className={`text-xs ${isCorner ? 'text-yellow-200' : 'text-gray-600'} font-semibold mt-1`}>
              {square.price}€
            </div>
          )}
        </div>

        {/* Joueurs sur cette case */}
        {playersOnSquare.length > 0 && (
          <div className="absolute bottom-1 left-1 right-1 flex justify-center items-end space-x-0.5">
            {playersOnSquare.map(([playerId], playerIndex) => {
              const colors = ['bg-blue-500', 'bg-red-500', 'bg-green-500', 'bg-yellow-500']
              const colorIndex = parseInt(playerId) % colors.length
              return (
                <div
                  key={playerId}
                  className={`
                    ${colors[colorIndex]} rounded-full
                    ${viewport.isMobile ? 'w-2 h-2' : 'w-3 h-3'}
                    border border-white shadow-lg
                    ${playerId === currentPlayer ? 'ring-2 ring-yellow-400 animate-pulse' : ''}
                  `}
                  style={{
                    transform: `translateX(${playerIndex * 2}px)`,
                    zIndex: 10 + playerIndex
                  }}
                />
              )
            })}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full h-full flex items-center justify-center p-2">
      <div className={`relative ${getBoardSize()} bg-green-100 border-4 border-gray-800 shadow-2xl`}>
        {/* Toutes les cases du plateau */}
        {squares.map((square, index) => renderSquare(square, index))}

        {/* Zone centrale du plateau */}
        <div className="absolute top-[10%] left-[10%] right-[10%] bottom-[10%] bg-gradient-to-br from-green-50 via-green-100 to-green-200 border-2 border-gray-600 flex items-center justify-center">
          <div className="text-center">
            <div className={`font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 drop-shadow-lg ${viewport.isMobile ? 'text-lg' : viewport.isTablet ? 'text-2xl' : 'text-4xl'}`}>
              TheRateRace
            </div>
            <div className={`text-gray-700 font-semibold ${viewport.isMobile ? 'text-xs' : viewport.isTablet ? 'text-sm' : 'text-lg'}`}>
              .io
            </div>
            <div className={`text-gray-600 font-medium ${viewport.isMobile ? 'text-[8px]' : viewport.isTablet ? 'text-xs' : 'text-sm'} mt-1`}>
              Jeu de plateau satirique
            </div>
          </div>
        </div>

        {/* Dés au centre du plateau */}
        <CenterDice
          diceValues={diceValues}
          isRolling={isRolling}
        />
      </div>
    </div>
  )
}
