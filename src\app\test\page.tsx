'use client'

import { useState } from 'react'
import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'
import { useI18n } from '@/lib/i18n/hooks'

// Mock data pour tester l'interface sans Supabase
const mockRooms = [
  {
    id: '1',
    name: 'Salon des Débutants',
    description: 'Parfait pour apprendre les règles',
    max_players: 4,
    current_players: 2,
    status: 'waiting' as const,
    invite_code: 'ABC123XY',
    created_at: new Date().toISOString()
  },
  {
    id: '2', 
    name: 'Partie Rapide',
    description: 'Jeu accéléré pour les pressés',
    max_players: 6,
    current_players: 4,
    status: 'waiting' as const,
    invite_code: 'DEF456ZW',
    created_at: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Tournoi des Champions',
    description: 'Réservé aux experts',
    max_players: 8,
    current_players: 7,
    status: 'waiting' as const,
    invite_code: 'GHI789UV',
    created_at: new Date().toISOString()
  }
]

const mockPlayers = [
  { id: '1', name: 'Sophie_CEO', is_bot: false, player_index: 0 },
  { id: '2', name: '<PERSON>_Startup', is_bot: true, player_index: 1 },
  { id: '3', name: 'Julie_Finance', is_bot: true, player_index: 2 },
  { id: '4', name: 'Thomas_Tech', is_bot: true, player_index: 3 }
]

export default function TestPage() {
  const { t } = useI18n('common')
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newRoom, setNewRoom] = useState({
    name: '',
    description: '',
    max_players: 4,
    password: ''
  })

  const handleCreateRoom = () => {
    console.log('Création du salon:', newRoom)
    setShowCreateForm(false)
    setNewRoom({ name: '', description: '', max_players: 4, password: '' })
  }

  const handleJoinRoom = (roomId: string) => {
    console.log('Rejoindre le salon:', roomId)
    setSelectedRoom(roomId)
  }

  const handleStartGame = () => {
    console.log('Démarrer la partie avec des bots')
    // Ici on ajouterait la logique pour remplir avec des bots et démarrer
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-primary neon-text mb-4">
            🧪 Page de Test - Salons de Jeu
          </h1>
          <p className="text-muted">
            Interface de test pour le système de salons (sans Supabase)
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Liste des salons */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Salons Disponibles</h2>
              <Button 
                onClick={() => setShowCreateForm(true)}
                variant="primary"
                neonGlow
              >
                Créer un Salon
              </Button>
            </div>

            {/* Formulaire de création */}
            {showCreateForm && (
              <div className="bg-card p-6 rounded-lg border border-border">
                <h3 className="text-lg font-semibold mb-4">Nouveau Salon</h3>
                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Nom du salon"
                    value={newRoom.name}
                    onChange={(e) => setNewRoom({...newRoom, name: e.target.value})}
                    className="w-full bg-muted text-foreground border border-border rounded px-3 py-2"
                  />
                  <textarea
                    placeholder="Description (optionnel)"
                    value={newRoom.description}
                    onChange={(e) => setNewRoom({...newRoom, description: e.target.value})}
                    className="w-full bg-muted text-foreground border border-border rounded px-3 py-2"
                    rows={3}
                  />
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="block text-sm text-muted mb-1">Joueurs max</label>
                      <select
                        value={newRoom.max_players}
                        onChange={(e) => setNewRoom({...newRoom, max_players: parseInt(e.target.value)})}
                        className="w-full bg-muted text-foreground border border-border rounded px-3 py-2"
                      >
                        {[2,3,4,5,6,7,8,9,10,11,12].map(n => (
                          <option key={n} value={n}>{n} joueurs</option>
                        ))}
                      </select>
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm text-muted mb-1">Mot de passe (optionnel)</label>
                      <input
                        type="password"
                        placeholder="Mot de passe"
                        value={newRoom.password}
                        onChange={(e) => setNewRoom({...newRoom, password: e.target.value})}
                        className="w-full bg-muted text-foreground border border-border rounded px-3 py-2"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleCreateRoom} variant="primary">
                      Créer
                    </Button>
                    <Button onClick={() => setShowCreateForm(false)} variant="outline">
                      Annuler
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Liste des salons */}
            <div className="space-y-4">
              {mockRooms.map((room) => (
                <div 
                  key={room.id} 
                  className={`bg-card p-4 rounded-lg border transition-colors ${
                    selectedRoom === room.id ? 'border-primary' : 'border-border'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-lg">{room.name}</h3>
                    <span className="text-xs bg-accent text-black px-2 py-1 rounded">
                      {room.current_players}/{room.max_players}
                    </span>
                  </div>
                  {room.description && (
                    <p className="text-muted text-sm mb-3">{room.description}</p>
                  )}
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted">
                      Code: {room.invite_code}
                    </span>
                    <Button 
                      size="sm"
                      onClick={() => handleJoinRoom(room.id)}
                      disabled={room.current_players >= room.max_players}
                    >
                      {room.current_players >= room.max_players ? 'Complet' : 'Rejoindre'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Détails du salon sélectionné */}
          <div className="space-y-6">
            {selectedRoom ? (
              <>
                <h2 className="text-2xl font-semibold">Salon: {mockRooms.find(r => r.id === selectedRoom)?.name}</h2>
                
                <div className="bg-card p-6 rounded-lg border border-border">
                  <h3 className="text-lg font-semibold mb-4">Joueurs dans le salon</h3>
                  <div className="space-y-3">
                    {mockPlayers.map((player) => (
                      <div key={player.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${player.is_bot ? 'bg-secondary' : 'bg-primary'}`}></div>
                          <span>{player.name}</span>
                          {player.is_bot && (
                            <span className="text-xs bg-muted px-2 py-1 rounded">BOT</span>
                          )}
                        </div>
                        <span className="text-sm text-muted">Joueur {player.player_index + 1}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 space-y-3">
                    <Button 
                      onClick={handleStartGame}
                      variant="primary"
                      neonGlow
                      className="w-full"
                    >
                      🎲 Démarrer la Partie (avec bots)
                    </Button>
                    <Button 
                      variant="outline"
                      className="w-full"
                    >
                      📋 Copier le lien d'invitation
                    </Button>
                  </div>
                </div>

                <div className="bg-card p-4 rounded-lg border border-border">
                  <h4 className="font-semibold mb-2">Fonctionnalités à tester:</h4>
                  <ul className="text-sm text-muted space-y-1">
                    <li>✅ Interface de création de salon</li>
                    <li>✅ Liste des salons disponibles</li>
                    <li>✅ Affichage des joueurs</li>
                    <li>🔄 Système de bots (en cours)</li>
                    <li>⏳ Démarrage de partie</li>
                    <li>⏳ Liens d'invitation</li>
                  </ul>
                </div>
              </>
            ) : (
              <div className="bg-card p-8 rounded-lg border border-border text-center">
                <h3 className="text-lg font-semibold mb-2">Sélectionnez un salon</h3>
                <p className="text-muted">Choisissez un salon dans la liste pour voir les détails</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
