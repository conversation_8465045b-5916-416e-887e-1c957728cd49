-- Migration pour permettre les fonctions anonymes (pour les tests locaux)
-- Cette migration est temporaire et ne doit PAS être utilisée en production

-- Fonction pour créer un salon anonyme (pour les tests)
CREATE OR REPLACE FUNCTION create_anonymous_game_room(
    room_name TEXT,
    room_description TEXT DEFAULT NULL,
    max_players_count INTEGER DEFAULT 4,
    room_password TEXT DEFAULT NULL,
    creator_name TEXT DEFAULT 'Joueur Anonyme'
)
RETURNS UUID AS $$
DECLARE
    new_room_id UUID;
    invite_code TEXT;
    password_hash TEXT;
BEGIN
    -- Generate unique invite code
    LOOP
        invite_code := generate_invite_code();
        EXIT WHEN NOT EXISTS (SELECT 1 FROM public.game_rooms WHERE invite_code = invite_code);
    END LOOP;
    
    -- Hash password if provided
    IF room_password IS NOT NULL THEN
        password_hash := crypt(room_password, gen_salt('bf'));
    END IF;
    
    -- Create room (sans created_by pour les tests anonymes)
    INSERT INTO public.game_rooms (name, description, max_players, password_hash, invite_code)
    VALUES (room_name, room_description, max_players_count, password_hash, invite_code)
    RETURNING id INTO new_room_id;
    
    -- Add anonymous creator as first player
    INSERT INTO public.room_players (room_id, player_name, player_index, is_bot)
    VALUES (new_room_id, creator_name, 0, false);
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = 1 
    WHERE id = new_room_id;
    
    RETURN new_room_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour rejoindre un salon anonyme
CREATE OR REPLACE FUNCTION join_anonymous_game_room(
    room_id_param UUID,
    player_name_param TEXT DEFAULT 'Joueur Anonyme',
    room_password_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    room_record RECORD;
    next_player_index INTEGER;
BEGIN
    -- Get room info
    SELECT * INTO room_record 
    FROM public.game_rooms 
    WHERE id = room_id_param AND status = 'waiting';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Salon non trouvé ou déjà commencé';
    END IF;
    
    -- Check password if required
    IF room_record.password_hash IS NOT NULL THEN
        IF room_password_param IS NULL OR NOT (crypt(room_password_param, room_record.password_hash) = room_record.password_hash) THEN
            RAISE EXCEPTION 'Mot de passe incorrect';
        END IF;
    END IF;
    
    -- Check if room is full
    IF room_record.current_players >= room_record.max_players THEN
        RAISE EXCEPTION 'Salon complet';
    END IF;
    
    -- Get next player index
    SELECT COALESCE(MAX(player_index), -1) + 1 INTO next_player_index
    FROM public.room_players 
    WHERE room_id = room_id_param;
    
    -- Add player
    INSERT INTO public.room_players (room_id, player_name, player_index, is_bot)
    VALUES (room_id_param, player_name_param, next_player_index, false);
    
    -- Update room player count
    UPDATE public.game_rooms 
    SET current_players = current_players + 1 
    WHERE id = room_id_param;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Commentaire de sécurité
COMMENT ON FUNCTION create_anonymous_game_room IS 'ATTENTION: Fonction pour tests anonymes. À supprimer en production !';
COMMENT ON FUNCTION join_anonymous_game_room IS 'ATTENTION: Fonction pour tests anonymes. À supprimer en production !';
