import React from 'react'

interface DiceProps {
  value: number | null
  isRolling: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function Dice({ value, isRolling, size = 'md' }: DiceProps) {
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  }

  const dotSizeClasses = {
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  }

  const getDiceFace = (num: number | null) => {
    if (!num) return []

    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }

    return faces[num as keyof typeof faces] || []
  }

  const activeDots = getDiceFace(value)

  return (
    <div
      className={`
        ${sizeClasses[size]}
        relative transform-gpu transition-all duration-500
        ${isRolling ? 'animate-bounce' : 'hover:scale-105'}
      `}
      style={{
        perspective: '200px',
        transformStyle: 'preserve-3d'
      }}
    >
      {/* Dé 3D avec faces */}
      <div
        className={`
          w-full h-full relative
          ${isRolling ? 'animate-spin' : ''}
          transition-transform duration-500
        `}
        style={{
          transformStyle: 'preserve-3d',
          transform: isRolling ? 'rotateX(360deg) rotateY(360deg)' : 'rotateX(-15deg) rotateY(15deg)'
        }}
      >
        {/* Face avant */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-white to-gray-100 border-2 border-gray-300 rounded-lg shadow-lg flex items-center justify-center"
          style={{
            transform: `translateZ(${size === 'sm' ? '6px' : size === 'md' ? '8px' : '12px'})`
          }}
        >
          <div className="grid grid-cols-3 gap-1 w-full h-full p-2">
            {Array.from({ length: 9 }, (_, i) => (
              <div
                key={i}
                className={`
                  ${dotSizeClasses[size]}
                  rounded-full transition-all duration-200
                  ${activeDots.includes(i)
                    ? 'bg-gray-800 scale-100 shadow-sm'
                    : 'bg-transparent scale-0'
                  }
                `}
              />
            ))}
          </div>
        </div>

        {/* Face droite */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-200 border-2 border-gray-400 rounded-lg shadow-md"
          style={{
            transform: `rotateY(90deg) translateZ(${size === 'sm' ? '6px' : size === 'md' ? '8px' : '12px'})`
          }}
        />

        {/* Face haut */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300 border-2 border-gray-500 rounded-lg shadow-sm"
          style={{
            transform: `rotateX(90deg) translateZ(${size === 'sm' ? '6px' : size === 'md' ? '8px' : '12px'})`
          }}
        />
      </div>

      {/* Ombre portée */}
      <div
        className={`
          absolute top-full left-1/2 transform -translate-x-1/2
          ${size === 'sm' ? 'w-8 h-4' : size === 'md' ? 'w-12 h-6' : 'w-16 h-8'}
          bg-black/20 rounded-full blur-sm
          ${isRolling ? 'animate-pulse' : ''}
        `}
        style={{
          marginTop: '2px'
        }}
      />

      {/* Effet de brillance */}
      {!isRolling && (
        <div
          className="absolute inset-0 bg-gradient-to-tr from-white/40 to-transparent rounded-lg pointer-events-none"
          style={{
            transform: 'translateZ(1px)'
          }}
        />
      )}
    </div>
  )
}
