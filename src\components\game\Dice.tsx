import React from 'react'

interface DiceProps {
  value: number | null
  isRolling: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function Dice({ value, isRolling, size = 'md' }: DiceProps) {
  const sizeClasses = {
    sm: 'w-12 h-12 text-xs',
    md: 'w-20 h-20 text-sm',
    lg: 'w-32 h-32 text-lg'
  }

  const getDiceFace = (num: number | null) => {
    if (!num) return []
    
    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }
    
    return faces[num as keyof typeof faces] || []
  }

  const activeDots = getDiceFace(value)

  return (
    <div className={`
      ${sizeClasses[size]} 
      bg-white border-2 border-gray-300 rounded-xl shadow-lg
      flex items-center justify-center relative
      ${isRolling ? 'animate-spin' : ''}
      transition-all duration-300
    `}>
      {/* Grille 3x3 pour les points du dé */}
      <div className="grid grid-cols-3 gap-1 w-full h-full p-2">
        {Array.from({ length: 9 }, (_, i) => (
          <div
            key={i}
            className={`
              rounded-full transition-all duration-200
              ${activeDots.includes(i) 
                ? 'bg-gray-800 scale-100' 
                : 'bg-transparent scale-0'
              }
            `}
          />
        ))}
      </div>
      
      {/* Animation de roulement */}
      {isRolling && (
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl animate-pulse" />
      )}
    </div>
  )
}
