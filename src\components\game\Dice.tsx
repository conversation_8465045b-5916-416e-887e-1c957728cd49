import React from 'react'

interface DiceProps {
  value: number | null
  isRolling: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function Dice({ value, isRolling, size = 'md' }: DiceProps) {
  const sizeClasses = {
    sm: 'w-14 h-14',
    md: 'w-20 h-20',
    lg: 'w-28 h-28'
  }

  const dotSizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-4 h-4'
  }

  const getDiceFace = (num: number | null) => {
    if (!num) return []

    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }

    return faces[num as keyof typeof faces] || []
  }

  const activeDots = getDiceFace(value)

  return (
    <div
      className={`
        ${sizeClasses[size]}
        relative transform-gpu transition-all duration-500
        ${isRolling ? 'animate-spin' : 'hover:scale-110'}
        drop-shadow-2xl
      `}
      style={{
        perspective: '300px',
        transformStyle: 'preserve-3d'
      }}
    >
      {/* Dé 3D avec faces */}
      <div
        className={`
          w-full h-full relative
          ${isRolling ? 'animate-spin' : ''}
          transition-transform duration-700 ease-out
        `}
        style={{
          transformStyle: 'preserve-3d',
          transform: isRolling ? 'rotateX(720deg) rotateY(720deg) rotateZ(360deg)' : 'rotateX(-20deg) rotateY(20deg) rotateZ(5deg)'
        }}
      >
        {/* Face avant */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-gray-200 border-3 border-gray-400 rounded-xl shadow-2xl flex items-center justify-center"
          style={{
            transform: `translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`,
            boxShadow: '0 8px 16px rgba(0,0,0,0.3), inset 0 2px 4px rgba(255,255,255,0.8)'
          }}
        >
          <div className="grid grid-cols-3 gap-1 w-full h-full p-3">
            {Array.from({ length: 9 }, (_, i) => (
              <div
                key={i}
                className={`
                  ${dotSizeClasses[size]}
                  rounded-full transition-all duration-300
                  ${activeDots.includes(i)
                    ? 'bg-gray-900 scale-100 shadow-lg border border-gray-700'
                    : 'bg-transparent scale-0'
                  }
                `}
                style={{
                  boxShadow: activeDots.includes(i) ? 'inset 0 1px 2px rgba(0,0,0,0.5)' : 'none'
                }}
              />
            ))}
          </div>
        </div>

        {/* Face droite */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400 border-3 border-gray-500 rounded-xl shadow-lg"
          style={{
            transform: `rotateY(90deg) translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`,
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
          }}
        />

        {/* Face haut */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-500 border-3 border-gray-600 rounded-xl shadow-md"
          style={{
            transform: `rotateX(90deg) translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`,
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
          }}
        />

        {/* Face arrière */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-400 to-gray-600 border-3 border-gray-700 rounded-xl shadow-sm"
          style={{
            transform: `rotateY(180deg) translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`
          }}
        />

        {/* Face gauche */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-500 border-3 border-gray-600 rounded-xl shadow-md"
          style={{
            transform: `rotateY(-90deg) translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`
          }}
        />

        {/* Face bas */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-700 border-3 border-gray-800 rounded-xl shadow-sm"
          style={{
            transform: `rotateX(-90deg) translateZ(${size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px'})`
          }}
        />
      </div>

      {/* Ombre portée réaliste */}
      <div
        className={`
          absolute top-full left-1/2 transform -translate-x-1/2
          ${size === 'sm' ? 'w-10 h-5' : size === 'md' ? 'w-16 h-8' : 'w-20 h-10'}
          bg-black/40 rounded-full blur-md
          ${isRolling ? 'animate-pulse scale-110' : ''}
          transition-all duration-300
        `}
        style={{
          marginTop: '4px',
          filter: 'blur(8px)'
        }}
      />

      {/* Effet de brillance amélioré */}
      {!isRolling && (
        <div
          className="absolute inset-0 bg-gradient-to-tr from-white/60 via-white/20 to-transparent rounded-xl pointer-events-none"
          style={{
            transform: 'translateZ(2px)'
          }}
        />
      )}

      {/* Particules lors du lancement */}
      {isRolling && (
        <>
          <div className="absolute -top-2 -left-2 w-1 h-1 bg-yellow-400 rounded-full animate-ping" />
          <div className="absolute -top-2 -right-2 w-1 h-1 bg-blue-400 rounded-full animate-ping animation-delay-200" />
          <div className="absolute -bottom-2 -left-2 w-1 h-1 bg-red-400 rounded-full animate-ping animation-delay-400" />
          <div className="absolute -bottom-2 -right-2 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-600" />
        </>
      )}
    </div>
  )
}
