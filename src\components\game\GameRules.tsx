import React, { useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

interface GameRulesProps {
  isOpen: boolean
  onClose: () => void
}

export default function GameRules({ isOpen, onClose }: GameRulesProps) {
  const viewport = useViewport()
  const [currentStep, setCurrentStep] = useState(0)

  const rules = [
    {
      title: "🎯 Objectif du jeu",
      content: [
        "Devenez le plus riche en achetant des propriétés et en collectant des loyers",
        "Évitez la faillite et éliminez vos adversaires",
        "Le dernier joueur avec de l'argent gagne la partie !"
      ]
    },
    {
      title: "🎲 Tour de jeu",
      content: [
        "1. Lancez les deux dés pour vous déplacer",
        "2. Déplacez-vous du nombre de cases indiqué",
        "3. Effectuez l'action de la case où vous arrivez",
        "4. Si vous faites un double (même valeur sur les deux dés), rejouez !"
      ]
    },
    {
      title: "🏠 Types de cases",
      content: [
        "🏁 DÉPART : Recevez 200€ à chaque passage",
        "🏢 PROPRIÉTÉS : Achetez-les ou payez un loyer au propriétaire",
        "💡 OPPORTUNITÉ : Piochez une carte chance",
        "💸 IMPÔTS : Payez la somme indiquée",
        "🔒 PRISON : Restez bloqué un tour (ou payez pour sortir)"
      ]
    },
    {
      title: "💰 Acheter des propriétés",
      content: [
        "Quand vous arrivez sur une propriété libre, vous pouvez l'acheter",
        "Payez le prix indiqué sur la case",
        "Les autres joueurs devront vous payer un loyer s'ils s'y arrêtent",
        "Plus vous possédez de propriétés de la même couleur, plus les loyers sont élevés"
      ]
    },
    {
      title: "🤖 Bots intelligents",
      content: [
        "Les bots ont des personnalités différentes :",
        "🔥 AGRESSIF : Achète beaucoup, prend des risques",
        "🛡️ CONSERVATEUR : Économise son argent, achète peu",
        "⚖️ ÉQUILIBRÉ : Stratégie mixte et réfléchie",
        "🎲 IMPRÉVISIBLE : Comportement aléatoire"
      ]
    },
    {
      title: "🏆 Conseils pour gagner",
      content: [
        "Achetez des propriétés tôt dans la partie",
        "Essayez d'obtenir des monopoles (toutes les propriétés d'une couleur)",
        "Gardez toujours un peu d'argent pour les imprévus",
        "Observez les stratégies des autres joueurs",
        "N'hésitez pas à négocier des échanges !"
      ]
    }
  ]

  if (!isOpen) return null

  const nextStep = () => {
    if (currentStep < rules.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className={`
        bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border-2 border-white/10 shadow-2xl
        ${viewport.isMobile ? 'w-full max-w-sm max-h-[80vh]' : 'w-full max-w-2xl max-h-[90vh]'}
        overflow-hidden
      `}>
        
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 flex justify-between items-center">
          <h2 className={`text-white font-bold ${viewport.isMobile ? 'text-lg' : 'text-2xl'}`}>
            📚 Règles du jeu
          </h2>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-300 transition-colors text-2xl"
          >
            ✕
          </button>
        </div>

        {/* Progress bar */}
        <div className="bg-slate-700 h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-blue-500 h-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / rules.length) * 100}%` }}
          />
        </div>

        {/* Content */}
        <div className={`p-6 overflow-y-auto ${viewport.isMobile ? 'max-h-96' : 'max-h-[60vh]'}`}>
          <div className="text-center mb-6">
            <h3 className={`text-white font-bold mb-4 ${viewport.isMobile ? 'text-xl' : 'text-3xl'}`}>
              {rules[currentStep].title}
            </h3>
            
            <div className="space-y-3">
              {rules[currentStep].content.map((item, index) => (
                <div 
                  key={index}
                  className={`
                    text-white/90 leading-relaxed
                    ${viewport.isMobile ? 'text-sm' : 'text-lg'}
                    ${item.startsWith('🔥') || item.startsWith('🛡️') || item.startsWith('⚖️') || item.startsWith('🎲') ? 'ml-4' : ''}
                  `}
                >
                  {item}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="bg-slate-700/50 p-4 flex justify-between items-center">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`
              px-4 py-2 rounded-lg font-medium transition-all duration-300
              ${currentStep === 0 
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                : 'bg-purple-600 hover:bg-purple-500 text-white transform hover:scale-105'
              }
              ${viewport.isMobile ? 'text-sm px-3 py-1' : ''}
            `}
          >
            ← Précédent
          </button>

          <div className="flex space-x-2">
            {rules.map((_, index) => (
              <div
                key={index}
                className={`
                  w-3 h-3 rounded-full transition-all duration-300
                  ${index === currentStep ? 'bg-purple-500 scale-125' : 'bg-gray-600'}
                `}
              />
            ))}
          </div>

          {currentStep < rules.length - 1 ? (
            <button
              onClick={nextStep}
              className={`
                bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded-lg font-medium 
                transition-all duration-300 transform hover:scale-105
                ${viewport.isMobile ? 'text-sm px-3 py-1' : ''}
              `}
            >
              Suivant →
            </button>
          ) : (
            <button
              onClick={onClose}
              className={`
                bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded-lg font-medium 
                transition-all duration-300 transform hover:scale-105
                ${viewport.isMobile ? 'text-sm px-3 py-1' : ''}
              `}
            >
              Commencer ! 🚀
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Composant pour le bouton d'aide dans le jeu
export function GameHelpButton({ onClick }: { onClick: () => void }) {
  const viewport = useViewport()
  
  return (
    <button
      onClick={onClick}
      className={`
        fixed bottom-4 right-4 z-40
        bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500
        text-white rounded-full shadow-lg transition-all duration-300 transform hover:scale-110
        ${viewport.isMobile ? 'w-12 h-12 text-lg' : 'w-14 h-14 text-xl'}
      `}
      title="Règles du jeu"
    >
      ❓
    </button>
  )
}

// Hook pour gérer l'état des règles
export function useGameRules() {
  const [isRulesOpen, setIsRulesOpen] = useState(false)
  
  const openRules = () => setIsRulesOpen(true)
  const closeRules = () => setIsRulesOpen(false)
  
  return {
    isRulesOpen,
    openRules,
    closeRules
  }
}
