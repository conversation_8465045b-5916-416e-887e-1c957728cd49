import React from 'react'
import { SimplePlayer } from '@/types/game'
import { useViewport } from '@/hooks/useViewport'

export interface EventCard {
  id: string
  title: string
  description: string
  icon: string
  effect: 'money' | 'move' | 'special'
  value: number
  isPositive: boolean
}

export const CHANCE_CARDS: EventCard[] = [
  {
    id: 'startup_success',
    title: 'Startup à succès !',
    description: 'Votre startup décolle ! Recevez 300€',
    icon: '🚀',
    effect: 'money',
    value: 300,
    isPositive: true
  },
  {
    id: 'investor_meeting',
    title: 'Rendez-vous investisseur',
    description: 'Un investisseur vous donne 200€',
    icon: '💼',
    effect: 'money',
    value: 200,
    isPositive: true
  },
  {
    id: 'viral_content',
    title: 'Contenu viral !',
    description: 'Votre contenu devient viral ! +250€',
    icon: '📱',
    effect: 'money',
    value: 250,
    isPositive: true
  },
  {
    id: 'bug_fix',
    title: 'Bug critique',
    description: 'Un bug coûteux à réparer. Payez 150€',
    icon: '🐛',
    effect: 'money',
    value: -150,
    isPositive: false
  },
  {
    id: 'server_crash',
    title: 'Serveur en panne',
    description: 'Coûts de réparation : 200€',
    icon: '💥',
    effect: 'money',
    value: -200,
    isPositive: false
  },
  {
    id: 'tax_audit',
    title: 'Contrôle fiscal',
    description: 'Amendes et pénalités : 180€',
    icon: '📋',
    effect: 'money',
    value: -180,
    isPositive: false
  },
  {
    id: 'networking_event',
    title: 'Événement networking',
    description: 'Avancez de 3 cases',
    icon: '🤝',
    effect: 'move',
    value: 3,
    isPositive: true
  },
  {
    id: 'market_crash',
    title: 'Krach boursier',
    description: 'Reculez de 2 cases',
    icon: '📉',
    effect: 'move',
    value: -2,
    isPositive: false
  },
  {
    id: 'innovation_award',
    title: 'Prix de l\'innovation',
    description: 'Recevez 400€ pour votre innovation',
    icon: '🏆',
    effect: 'money',
    value: 400,
    isPositive: true
  },
  {
    id: 'partnership_deal',
    title: 'Partenariat stratégique',
    description: 'Un partenariat vous rapporte 350€',
    icon: '🤝',
    effect: 'money',
    value: 350,
    isPositive: true
  }
]

interface EventCardModalProps {
  card: EventCard | null
  isOpen: boolean
  onClose: () => void
  onApply: () => void
}

export function EventCardModal({ card, isOpen, onClose, onApply }: EventCardModalProps) {
  const viewport = useViewport()

  if (!isOpen || !card) return null

  const handleApply = () => {
    onApply()
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className={`
        bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border-2 border-white/10 shadow-2xl
        ${viewport.isMobile ? 'w-full max-w-sm' : 'w-full max-w-md'}
        overflow-hidden transform transition-all duration-300 scale-100
      `}>
        
        {/* Header */}
        <div className={`
          bg-gradient-to-r p-4 text-center
          ${card.isPositive ? 'from-green-600 to-blue-600' : 'from-red-600 to-orange-600'}
        `}>
          <div className={`${viewport.isMobile ? 'text-4xl' : 'text-6xl'} mb-2`}>
            {card.icon}
          </div>
          <h2 className={`text-white font-bold ${viewport.isMobile ? 'text-lg' : 'text-2xl'}`}>
            Carte Opportunité
          </h2>
        </div>

        {/* Content */}
        <div className="p-6 text-center">
          <h3 className={`text-white font-bold mb-4 ${viewport.isMobile ? 'text-xl' : 'text-2xl'}`}>
            {card.title}
          </h3>
          
          <p className={`text-white/90 mb-6 leading-relaxed ${viewport.isMobile ? 'text-sm' : 'text-lg'}`}>
            {card.description}
          </p>

          {/* Effect display */}
          <div className={`
            p-4 rounded-lg mb-6
            ${card.isPositive ? 'bg-green-500/20 border border-green-500/30' : 'bg-red-500/20 border border-red-500/30'}
          `}>
            <div className={`
              font-bold text-2xl
              ${card.isPositive ? 'text-green-400' : 'text-red-400'}
            `}>
              {card.effect === 'money' && (
                <>
                  {card.value > 0 ? '+' : ''}{card.value}€
                </>
              )}
              {card.effect === 'move' && (
                <>
                  {card.value > 0 ? 'Avancer de ' : 'Reculer de '}{Math.abs(card.value)} case{Math.abs(card.value) > 1 ? 's' : ''}
                </>
              )}
            </div>
          </div>

          <button
            onClick={handleApply}
            className={`
              w-full py-3 px-6 rounded-lg font-bold text-white transition-all duration-300 transform hover:scale-105
              ${card.isPositive 
                ? 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-500 hover:to-blue-500' 
                : 'bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-500 hover:to-orange-500'
              }
              ${viewport.isMobile ? 'text-sm' : 'text-lg'}
            `}
          >
            Appliquer l'effet
          </button>
        </div>
      </div>
    </div>
  )
}

// Hook pour gérer les cartes événements
export function useEventCards() {
  const [currentCard, setCurrentCard] = React.useState<EventCard | null>(null)
  const [isCardOpen, setIsCardOpen] = React.useState(false)

  const drawRandomCard = (): EventCard => {
    const randomIndex = Math.floor(Math.random() * CHANCE_CARDS.length)
    return CHANCE_CARDS[randomIndex]
  }

  const showCard = (card: EventCard) => {
    setCurrentCard(card)
    setIsCardOpen(true)
  }

  const hideCard = () => {
    setIsCardOpen(false)
    setCurrentCard(null)
  }

  const drawAndShowCard = () => {
    const card = drawRandomCard()
    showCard(card)
    return card
  }

  return {
    currentCard,
    isCardOpen,
    showCard,
    hideCard,
    drawRandomCard,
    drawAndShowCard
  }
}

// Fonction pour appliquer l'effet d'une carte
export function applyCardEffect(
  card: EventCard, 
  player: SimplePlayer, 
  setPlayers: React.Dispatch<React.SetStateAction<SimplePlayer[]>>,
  currentPlayerIndex: number
): string {
  let message = ''

  setPlayers(prev => {
    const newPlayers = [...prev]
    const targetPlayer = newPlayers[currentPlayerIndex]

    switch (card.effect) {
      case 'money':
        targetPlayer.money += card.value
        if (card.value > 0) {
          message = `${targetPlayer.name} reçoit ${card.value}€ !`
        } else {
          message = `${targetPlayer.name} perd ${Math.abs(card.value)}€ !`
        }
        break

      case 'move':
        const oldPosition = targetPlayer.position
        let newPosition = targetPlayer.position + card.value
        
        // Gérer le passage par la case départ
        if (newPosition >= 40) {
          newPosition = newPosition % 40
          targetPlayer.money += 200
          message = `${targetPlayer.name} avance et passe par la case DÉPART ! +200€`
        } else if (newPosition < 0) {
          newPosition = 40 + newPosition
          message = `${targetPlayer.name} recule de ${Math.abs(card.value)} case${Math.abs(card.value) > 1 ? 's' : ''}`
        } else {
          message = `${targetPlayer.name} ${card.value > 0 ? 'avance' : 'recule'} de ${Math.abs(card.value)} case${Math.abs(card.value) > 1 ? 's' : ''}`
        }
        
        targetPlayer.position = newPosition
        break

      case 'special':
        // Pour les effets spéciaux futurs
        message = `Effet spécial appliqué à ${targetPlayer.name}`
        break
    }

    return newPlayers
  })

  return message
}
