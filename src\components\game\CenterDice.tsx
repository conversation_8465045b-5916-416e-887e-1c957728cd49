import React, { useEffect, useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

interface CenterDiceProps {
  diceValues: [number, number]
  isRolling: boolean
  onRollComplete?: () => void
}

export default function CenterDice({ diceValues, isRolling, onRollComplete }: CenterDiceProps) {
  const viewport = useViewport()
  const [animationPhase, setAnimationPhase] = useState<'idle' | 'throwing' | 'bouncing' | 'settled'>('idle')
  const [showDice, setShowDice] = useState(false)

  useEffect(() => {
    if (isRolling) {
      setShowDice(true)
      setAnimationPhase('throwing')

      // Phase de lancement
      setTimeout(() => {
        setAnimationPhase('bouncing')
      }, 800)

      // Phase de rebond
      setTimeout(() => {
        setAnimationPhase('settled')
        onRollComplete?.()
      }, 1500)

      // Masquer les dés après 3 secondes
      setTimeout(() => {
        setShowDice(false)
        setAnimationPhase('idle')
      }, 3500)
    }
  }, [isRolling, onRollComplete])

  const getDiceSize = () => {
    if (viewport.isMobile) return { size: 'w-12 h-12', perspective: '60px' }
    if (viewport.isTablet) return { size: 'w-16 h-16', perspective: '80px' }
    return { size: 'w-20 h-20', perspective: '100px' }
  }

  const getDotSize = () => {
    if (viewport.isMobile) return 'w-1.5 h-1.5'
    if (viewport.isTablet) return 'w-2 h-2'
    return 'w-2.5 h-2.5'
  }

  const getDiceFace = (num: number) => {
    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }
    return faces[num as keyof typeof faces] || []
  }

  const renderDice = (value: number, index: number) => {
    const activeDots = getDiceFace(value)
    const { size, perspective } = getDiceSize()
    const dotSize = getDotSize()

    const getAnimationClass = () => {
      switch (animationPhase) {
        case 'throwing':
          return `scale-110 ${index === 0 ? 'translate-x-2 -translate-y-3' : '-translate-x-2 -translate-y-3'}`
        case 'bouncing':
          return `scale-105 ${index === 0 ? 'translate-x-1' : '-translate-x-1'}`
        case 'settled':
          return 'scale-100'
        default:
          return 'scale-100'
      }
    }

    return (
      <div
        key={index}
        className={`
          ${size} relative transform-gpu transition-all duration-700 ease-out
          ${getAnimationClass()}
          bg-white border-2 border-gray-400 rounded-lg shadow-2xl
          flex items-center justify-center
        `}
        style={{
          transform: animationPhase === 'throwing'
            ? `rotate(${Math.random() * 360}deg) scale(1.1)`
            : 'rotate(0deg) scale(1)',
          boxShadow: '0 8px 16px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.8)',
          animationDelay: `${index * 150}ms`
        }}
      >
        {/* Grille 3x3 pour les points */}
        <div className="grid grid-cols-3 grid-rows-3 gap-0.5 w-full h-full p-2">
          {Array.from({ length: 9 }, (_, dotIndex) => (
            <div
              key={dotIndex}
              className={`
                ${dotSize} rounded-full mx-auto my-auto
                ${activeDots.includes(dotIndex) ? 'bg-black shadow-sm' : 'bg-transparent'}
                transition-all duration-300
              `}
            />
          ))}
        </div>

        {/* Effet de brillance */}
        <div className="absolute top-1 left-1 w-2 h-2 bg-white/60 rounded-full blur-sm" />

        {/* Ombre portée */}
        <div
          className={`
            absolute top-full left-1/2 transform -translate-x-1/2
            ${viewport.isMobile ? 'w-8 h-4' : viewport.isTablet ? 'w-12 h-6' : 'w-16 h-8'}
            bg-black/30 rounded-full blur-md transition-all duration-300
            ${animationPhase === 'throwing' ? 'scale-150 opacity-20' :
              animationPhase === 'bouncing' ? 'scale-125 opacity-30' : 'scale-100 opacity-40'}
          `}
          style={{
            marginTop: '2px'
          }}
        />
      </div>
    )
  }

  // Ne pas afficher les dés si showDice est false
  if (!showDice) {
    return null
  }

  // Valeurs à afficher
  const displayValues: [number, number] = [
    diceValues[0] || 1,
    diceValues[1] || 1
  ]

  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center pointer-events-none z-40">
      <div className={`flex ${viewport.isMobile ? 'space-x-2' : viewport.isTablet ? 'space-x-3' : 'space-x-4'}`}>
        {renderDice(displayValues[0], 0)}
        {renderDice(displayValues[1], 1)}
      </div>

      {/* Effet de fond lors du lancement */}
      {isRolling && (
        <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse -z-10"
             style={{
               width: viewport.isMobile ? '80px' : viewport.isTablet ? '100px' : '120px',
               height: viewport.isMobile ? '80px' : viewport.isTablet ? '100px' : '120px',
               filter: 'blur(10px)'
             }}
        />
      )}
    </div>
  )
}
