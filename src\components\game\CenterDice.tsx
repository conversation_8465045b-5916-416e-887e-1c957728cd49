import React, { useEffect, useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

interface CenterDiceProps {
  diceValues: [number, number]
  isRolling: boolean
  onRollComplete?: () => void
}

export default function CenterDice({ diceValues, isRolling, onRollComplete }: CenterDiceProps) {
  const viewport = useViewport()
  const [animationPhase, setAnimationPhase] = useState<'idle' | 'throwing' | 'bouncing' | 'settled'>('idle')

  useEffect(() => {
    if (isRolling) {
      setAnimationPhase('throwing')
      
      // Phase de lancement
      setTimeout(() => {
        setAnimationPhase('bouncing')
      }, 800)
      
      // Phase de rebond
      setTimeout(() => {
        setAnimationPhase('settled')
        onRollComplete?.()
      }, 1500)
    } else {
      setAnimationPhase('idle')
    }
  }, [isRolling, onRollComplete])

  const getDiceSize = () => {
    if (viewport.isMobile) return 'w-12 h-12'
    if (viewport.isTablet) return 'w-16 h-16'
    return 'w-20 h-20'
  }

  const getDotSize = () => {
    if (viewport.isMobile) return 'w-1.5 h-1.5'
    if (viewport.isTablet) return 'w-2 h-2'
    return 'w-2.5 h-2.5'
  }

  const getDiceFace = (num: number) => {
    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }
    return faces[num as keyof typeof faces] || []
  }

  const renderDice = (value: number, index: number) => {
    const activeDots = getDiceFace(value)
    const diceSize = getDiceSize()
    const dotSize = getDotSize()
    
    const getAnimationClass = () => {
      switch (animationPhase) {
        case 'throwing':
          return `animate-spin scale-110 ${index === 0 ? 'translate-x-2 -translate-y-4' : '-translate-x-2 -translate-y-4'}`
        case 'bouncing':
          return `animate-bounce scale-105 ${index === 0 ? 'translate-x-1' : '-translate-x-1'}`
        case 'settled':
          return 'scale-100'
        default:
          return 'scale-100'
      }
    }

    return (
      <div
        key={index}
        className={`
          ${diceSize} relative transform-gpu transition-all duration-700 ease-out
          ${getAnimationClass()}
          drop-shadow-2xl
        `}
        style={{
          perspective: '300px',
          transformStyle: 'preserve-3d',
          animationDelay: `${index * 100}ms`
        }}
      >
        {/* Dé 3D réaliste */}
        <div
          className="w-full h-full relative"
          style={{
            transformStyle: 'preserve-3d',
            transform: animationPhase === 'throwing' 
              ? `rotateX(${720 + index * 180}deg) rotateY(${720 + index * 90}deg) rotateZ(${360 + index * 45}deg)`
              : 'rotateX(-15deg) rotateY(15deg) rotateZ(2deg)'
          }}
        >
          {/* Face avant avec points */}
          <div
            className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-gray-200 border-2 border-gray-400 rounded-lg shadow-2xl flex items-center justify-center"
            style={{
              transform: `translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`,
              boxShadow: '0 8px 16px rgba(0,0,0,0.4), inset 0 2px 4px rgba(255,255,255,0.8)'
            }}
          >
            <div className="grid grid-cols-3 gap-0.5 w-full h-full p-2">
              {Array.from({ length: 9 }, (_, i) => (
                <div
                  key={i}
                  className={`
                    ${dotSize} rounded-full transition-all duration-300 mx-auto my-auto
                    ${activeDots.includes(i)
                      ? 'bg-gray-900 scale-100 shadow-lg border border-gray-700'
                      : 'bg-transparent scale-0'
                    }
                  `}
                  style={{
                    boxShadow: activeDots.includes(i) ? 'inset 0 1px 2px rgba(0,0,0,0.6)' : 'none'
                  }}
                />
              ))}
            </div>
          </div>

          {/* Faces latérales pour l'effet 3D */}
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400 border-2 border-gray-500 rounded-lg"
            style={{
              transform: `rotateY(90deg) translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-500 border-2 border-gray-600 rounded-lg"
            style={{
              transform: `rotateY(-90deg) translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300 border-2 border-gray-400 rounded-lg"
            style={{
              transform: `rotateX(90deg) translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-400 to-gray-600 border-2 border-gray-700 rounded-lg"
            style={{
              transform: `rotateX(-90deg) translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-700 border-2 border-gray-800 rounded-lg"
            style={{
              transform: `rotateY(180deg) translateZ(${viewport.isMobile ? '6px' : viewport.isTablet ? '8px' : '10px'})`
            }}
          />
        </div>

        {/* Ombre portée dynamique */}
        <div
          className={`
            absolute top-full left-1/2 transform -translate-x-1/2
            ${viewport.isMobile ? 'w-8 h-4' : viewport.isTablet ? 'w-12 h-6' : 'w-16 h-8'}
            bg-black/50 rounded-full blur-md transition-all duration-300
            ${animationPhase === 'throwing' ? 'scale-150 opacity-30' : 
              animationPhase === 'bouncing' ? 'scale-125 opacity-40' : 'scale-100 opacity-50'}
          `}
          style={{
            marginTop: '2px',
            filter: 'blur(6px)'
          }}
        />

        {/* Particules d'impact */}
        {animationPhase === 'bouncing' && (
          <>
            <div className="absolute -top-1 -left-1 w-1 h-1 bg-yellow-400 rounded-full animate-ping" />
            <div className="absolute -top-1 -right-1 w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '100ms' }} />
            <div className="absolute -bottom-1 -left-1 w-1 h-1 bg-red-400 rounded-full animate-ping" style={{ animationDelay: '200ms' }} />
            <div className="absolute -bottom-1 -right-1 w-1 h-1 bg-green-400 rounded-full animate-ping" style={{ animationDelay: '300ms' }} />
          </>
        )}
      </div>
    )
  }

  if (diceValues[0] === 0 && diceValues[1] === 0 && !isRolling) {
    return null
  }

  return (
    <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-30">
      <div className="flex space-x-4">
        {renderDice(diceValues[0] || 1, 0)}
        {renderDice(diceValues[1] || 1, 1)}
      </div>
      
      {/* Effet de fond lors du lancement */}
      {isRolling && (
        <div className="absolute inset-0 bg-black/20 rounded-full animate-pulse" 
             style={{ 
               width: '200px', 
               height: '200px',
               filter: 'blur(20px)'
             }} 
        />
      )}
    </div>
  )
}
