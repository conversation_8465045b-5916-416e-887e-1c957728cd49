import React, { useEffect, useState } from 'react'
import { useViewport } from '@/hooks/useViewport'

interface CenterDiceProps {
  diceValues: [number, number]
  isRolling: boolean
  onRollComplete?: () => void
}

export default function CenterDice({ diceValues, isRolling, onRollComplete }: CenterDiceProps) {
  const viewport = useViewport()
  const [animationPhase, setAnimationPhase] = useState<'idle' | 'throwing' | 'bouncing' | 'settled'>('idle')

  useEffect(() => {
    if (isRolling) {
      setAnimationPhase('throwing')
      
      // Phase de lancement
      setTimeout(() => {
        setAnimationPhase('bouncing')
      }, 800)
      
      // Phase de rebond
      setTimeout(() => {
        setAnimationPhase('settled')
        onRollComplete?.()
      }, 1500)
    } else {
      setAnimationPhase('idle')
    }
  }, [isRolling, onRollComplete])

  const getDiceSize = () => {
    if (viewport.isMobile) return 'w-8 h-8'
    if (viewport.isTablet) return 'w-10 h-10'
    return 'w-12 h-12'
  }

  const getDotSize = () => {
    if (viewport.isMobile) return 'w-1 h-1'
    if (viewport.isTablet) return 'w-1.5 h-1.5'
    return 'w-2 h-2'
  }

  const getDiceFace = (num: number) => {
    const faces = {
      1: [4], // centre
      2: [0, 8], // diagonale
      3: [0, 4, 8], // diagonale + centre
      4: [0, 2, 6, 8], // coins
      5: [0, 2, 4, 6, 8], // coins + centre
      6: [0, 2, 3, 5, 6, 8] // deux colonnes
    }
    return faces[num as keyof typeof faces] || []
  }

  const renderDice = (value: number, index: number) => {
    const activeDots = getDiceFace(value)
    const diceSize = getDiceSize()
    const dotSize = getDotSize()
    
    const getAnimationClass = () => {
      switch (animationPhase) {
        case 'throwing':
          return `scale-110 ${index === 0 ? 'translate-x-1 -translate-y-2' : '-translate-x-1 -translate-y-2'}`
        case 'bouncing':
          return `scale-105 ${index === 0 ? 'translate-x-0.5' : '-translate-x-0.5'}`
        case 'settled':
          return 'scale-100'
        default:
          return 'scale-100'
      }
    }

    return (
      <div
        key={index}
        className={`
          ${diceSize} relative transform-gpu transition-all duration-700 ease-out
          ${getAnimationClass()}
          drop-shadow-2xl
        `}
        style={{
          perspective: '200px',
          transformStyle: 'preserve-3d',
          animationDelay: `${index * 100}ms`
        }}
      >
        {/* Dé 3D cubique réaliste */}
        <div
          className="w-full h-full relative"
          style={{
            transformStyle: 'preserve-3d',
            transform: animationPhase === 'throwing'
              ? `rotateX(${360 + index * 90}deg) rotateY(${360 + index * 45}deg) rotateZ(${180 + index * 30}deg)`
              : 'rotateX(-25deg) rotateY(25deg) rotateZ(8deg)'
          }}
        >
          {/* Face avant avec points */}
          <div
            className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-gray-200 border border-gray-400 rounded-sm shadow-lg flex items-center justify-center"
            style={{
              transform: `translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`,
              boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.8)'
            }}
          >
            <div className="grid grid-cols-3 gap-0 w-full h-full p-1">
              {Array.from({ length: 9 }, (_, i) => (
                <div
                  key={i}
                  className={`
                    ${dotSize} rounded-full transition-all duration-300 mx-auto my-auto
                    ${activeDots.includes(i)
                      ? 'bg-gray-900 scale-100 shadow-sm'
                      : 'bg-transparent scale-0'
                    }
                  `}
                  style={{
                    boxShadow: activeDots.includes(i) ? 'inset 0 0.5px 1px rgba(0,0,0,0.6)' : 'none'
                  }}
                />
              ))}
            </div>
          </div>

          {/* Faces latérales pour l'effet 3D cubique */}
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400 border border-gray-500 rounded-sm"
            style={{
              transform: `rotateY(90deg) translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-500 border border-gray-600 rounded-sm"
            style={{
              transform: `rotateY(-90deg) translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300 border border-gray-400 rounded-sm"
            style={{
              transform: `rotateX(90deg) translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-400 to-gray-600 border border-gray-700 rounded-sm"
            style={{
              transform: `rotateX(-90deg) translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`
            }}
          />
          <div
            className="absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-700 border border-gray-800 rounded-sm"
            style={{
              transform: `rotateY(180deg) translateZ(${viewport.isMobile ? '4px' : viewport.isTablet ? '5px' : '6px'})`
            }}
          />
        </div>

        {/* Ombre portée dynamique */}
        <div
          className={`
            absolute top-full left-1/2 transform -translate-x-1/2
            ${viewport.isMobile ? 'w-6 h-3' : viewport.isTablet ? 'w-8 h-4' : 'w-10 h-5'}
            bg-black/40 rounded-full blur-sm transition-all duration-300
            ${animationPhase === 'throwing' ? 'scale-150 opacity-20' :
              animationPhase === 'bouncing' ? 'scale-125 opacity-30' : 'scale-100 opacity-40'}
          `}
          style={{
            marginTop: '1px',
            filter: 'blur(3px)'
          }}
        />

        {/* Particules d'impact */}
        {animationPhase === 'bouncing' && (
          <>
            <div className="absolute -top-1 -left-1 w-1 h-1 bg-yellow-400 rounded-full animate-ping" />
            <div className="absolute -top-1 -right-1 w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '100ms' }} />
            <div className="absolute -bottom-1 -left-1 w-1 h-1 bg-red-400 rounded-full animate-ping" style={{ animationDelay: '200ms' }} />
            <div className="absolute -bottom-1 -right-1 w-1 h-1 bg-green-400 rounded-full animate-ping" style={{ animationDelay: '300ms' }} />
          </>
        )}
      </div>
    )
  }

  // Toujours afficher les dés, même avec des valeurs par défaut
  const displayValues: [number, number] = [
    diceValues[0] || 1,
    diceValues[1] || 1
  ]

  // Ne pas afficher les dés si les valeurs sont 0 (état initial)
  if (diceValues[0] === 0 && diceValues[1] === 0 && !isRolling) {
    return null
  }

  return (
    <div className="absolute bottom-4 right-4 flex items-center justify-center pointer-events-none z-30">
      <div className={`flex ${viewport.isMobile ? 'space-x-1' : viewport.isTablet ? 'space-x-2' : 'space-x-3'}`}>
        {renderDice(displayValues[0], 0)}
        {renderDice(displayValues[1], 1)}
      </div>

      {/* Effet de fond lors du lancement */}
      {isRolling && (
        <div className="absolute inset-0 bg-black/10 rounded-full animate-pulse"
             style={{
               width: viewport.isMobile ? '60px' : viewport.isTablet ? '80px' : '100px',
               height: viewport.isMobile ? '60px' : viewport.isTablet ? '80px' : '100px',
               filter: 'blur(8px)'
             }}
        />
      )}
    </div>
  )
}
