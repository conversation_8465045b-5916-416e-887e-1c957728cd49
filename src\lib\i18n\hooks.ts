'use client'

import { useTranslation } from 'react-i18next'
import { useEffect } from 'react'
import './client'

export function useI18n(namespace?: string) {
  const { t, i18n } = useTranslation(namespace)

  useEffect(() => {
    // Ensure i18n is initialized on client side
    if (!i18n.isInitialized) {
      i18n.init()
    }
  }, [i18n])

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  return {
    t,
    i18n,
    changeLanguage,
    currentLanguage: i18n.language,
    isLoading: !i18n.isInitialized,
  }
}
