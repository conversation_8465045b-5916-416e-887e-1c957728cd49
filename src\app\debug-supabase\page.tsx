'use client'

import { useState } from 'react'
import Layout from '@/components/layout/Layout'
import Button from '@/components/ui/Button'

export default function DebugSupabasePage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testSupabaseConnection = async () => {
    setLoading(true)
    setResult('Test en cours...')
    
    try {
      // Test direct avec fetch
      const response = await fetch('http://127.0.0.1:55321/rest/v1/game_rooms', {
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setResult(`✅ Succès ! Trouvé ${data.length} salon(s):\n${JSON.stringify(data, null, 2)}`)
      
    } catch (error: any) {
      setResult(`❌ Erreur: ${error.message}`)
      console.error('Erreur Supabase:', error)
    } finally {
      setLoading(false)
    }
  }

  const testCreateRoom = async () => {
    setLoading(true)
    setResult('Création de salon en cours...')

    try {
      // Test avec la nouvelle fonction anonyme
      const response = await fetch('http://127.0.0.1:55321/rest/v1/rpc/create_anonymous_game_room', {
        method: 'POST',
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          room_name: `Test Salon ${Date.now()}`,
          room_description: 'Salon créé via fonction anonyme',
          max_players_count: 4,
          creator_name: 'Testeur Debug'
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const roomId = await response.json()
      setResult(`✅ Salon créé avec succès !\nID du salon: ${roomId}`)

    } catch (error: any) {
      setResult(`❌ Erreur lors de la création: ${error.message}`)
      console.error('Erreur création:', error)
    } finally {
      setLoading(false)
    }
  }

  const testAddBots = async () => {
    setLoading(true)
    setResult('Test d\'ajout de bots en cours...')

    try {
      // Utiliser l'ID du salon créé précédemment ou un ID de test
      const roomId = 'adbab031-6d80-4199-bd80-4baea995780d' // ID du salon créé

      const response = await fetch('http://127.0.0.1:55321/rest/v1/rpc/add_bots_to_room', {
        method: 'POST',
        headers: {
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          room_id: roomId
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const botsAdded = await response.json()
      setResult(`✅ Bots ajoutés avec succès !\nNombre de bots ajoutés: ${botsAdded}`)

    } catch (error: any) {
      setResult(`❌ Erreur lors de l'ajout des bots: ${error.message}`)
      console.error('Erreur bots:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-primary neon-text mb-4">
            🔧 Debug Supabase
          </h1>
          <p className="text-muted">
            Tests directs de connexion à Supabase
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg border border-border">
          <div className="space-y-4 mb-6">
            <Button 
              onClick={testSupabaseConnection} 
              disabled={loading}
              variant="primary"
              className="w-full"
            >
              {loading ? 'Test en cours...' : '🔍 Tester la Connexion'}
            </Button>
            
            <Button
              onClick={testCreateRoom}
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? 'Création en cours...' : '➕ Tester la Création de Salon'}
            </Button>

            <Button
              onClick={testAddBots}
              disabled={loading}
              variant="secondary"
              className="w-full"
            >
              {loading ? 'Ajout en cours...' : '🤖 Tester l\'Ajout de Bots'}
            </Button>
          </div>

          <div className="bg-muted p-4 rounded border">
            <h3 className="font-semibold mb-2">Résultat:</h3>
            <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Aucun test effectué'}
            </pre>
          </div>

          <div className="mt-4 text-sm text-muted">
            <p><strong>Configuration:</strong></p>
            <p>• URL: http://127.0.0.1:55321</p>
            <p>• Studio: <a href="http://127.0.0.1:55323" target="_blank" className="text-primary hover:underline">http://127.0.0.1:55323</a></p>
          </div>
        </div>
      </div>
    </Layout>
  )
}
