'use client'

import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import resourcesToBackend from 'i18next-resources-to-backend'
import { defaultLocale, locales } from './config'

const runsOnServerSide = typeof window === 'undefined'

i18next
  .use(initReactI18next)
  .use(LanguageDetector)
  .use(
    resourcesToBackend((language: string, namespace: string) => 
      import(`./locales/${language}/${namespace}.json`)
    )
  )
  .init({
    debug: process.env.NODE_ENV === 'development',
    fallbackLng: defaultLocale,
    lng: undefined, // let detect the language on client side
    detection: {
      order: ['localStorage', 'navigator'],
    },
    preload: runsOnServerSide ? locales : [],
    supportedLngs: locales,
    defaultNS: 'common',
    fallbackNS: 'common',
    ns: ['common', 'game', 'auth'],
    interpolation: {
      escapeValue: false,
    },
  })

export default i18next
