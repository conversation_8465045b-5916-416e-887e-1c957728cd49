'use client'

import { useState, useEffect } from 'react'
import { useViewport } from '@/hooks/useViewport'
import { SimpleBoardCase, Player } from '@/types/game'

interface GameBoard3DProps {
  cases: SimpleBoardCase[]
  players: Player[]
  currentPlayer: number
}

export default function GameBoard3D({ cases, players, currentPlayer }: GameBoard3DProps) {
  const viewport = useViewport()
  const [animatingPlayers, setAnimatingPlayers] = useState<Set<string>>(new Set())

  // Organiser les cases en bordure du plateau (comme Monopoly)
  const getBoardLayout = () => {
    const bottom = cases.slice(0, 11) // Cases 0-10
    const left = cases.slice(11, 20) // Cases 11-19
    const top = cases.slice(20, 31).reverse() // Cases 20-30 (inversées)
    const right = cases.slice(31, 40).reverse() // Cases 31-39 (inversées)
    
    return { bottom, left, top, right }
  }

  const { bottom, left, top, right } = getBoardLayout()

  const getPlayersOnCase = (caseId: number) => {
    return players.filter(player => player.position === caseId)
  }

  // Animation des pions qui sautillent
  const animatePlayerMovement = (playerId: string) => {
    setAnimatingPlayers(prev => new Set(prev).add(playerId))
    setTimeout(() => {
      setAnimatingPlayers(prev => {
        const newSet = new Set(prev)
        newSet.delete(playerId)
        return newSet
      })
    }, 1000)
  }

  // Détecter les changements de position pour déclencher les animations
  useEffect(() => {
    players.forEach(player => {
      if (player.id === players[currentPlayer].id) {
        animatePlayerMovement(player.id)
      }
    })
  }, [players.map(p => p.position).join(',')])

  const renderCase = (boardCase: SimpleBoardCase, position: 'bottom' | 'left' | 'top' | 'right') => {
    const playersOnCase = getPlayersOnCase(boardCase.id)
    const isCorner = [0, 10, 20, 30].includes(boardCase.id)
    
    // Classes CSS pour l'effet 3D
    const perspectiveClasses = `
      transform-gpu transition-all duration-300 hover:scale-105
      ${position === 'bottom' ? 'hover:-translate-y-1' : ''}
      ${position === 'top' ? 'hover:translate-y-1' : ''}
      ${position === 'left' ? 'hover:translate-x-1' : ''}
      ${position === 'right' ? 'hover:-translate-x-1' : ''}
    `
    
    const baseClasses = `
      relative border-2 border-border bg-gradient-to-br from-card to-card/80 
      backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300
      flex flex-col items-center justify-center text-center p-1
      ${isCorner ? 'w-20 h-20' : position === 'bottom' || position === 'top' ? 'w-16 h-20' : 'w-20 h-16'}
      ${perspectiveClasses}
    `
    
    const colorClasses = boardCase.color ? `border-l-4 shadow-[inset_4px_0_0_${boardCase.color}]` : ''
    
    return (
      <div 
        key={boardCase.id}
        className={`${baseClasses} ${colorClasses}`}
        style={boardCase.color ? { 
          borderLeftColor: boardCase.color,
          boxShadow: `inset 4px 0 0 ${boardCase.color}, 0 4px 6px -1px rgba(0, 0, 0, 0.1)`
        } : {}}
      >
        {/* Icône de la case avec effet 3D */}
        <div className="text-lg mb-1 transform transition-transform duration-200 hover:scale-110">
          {boardCase.icon}
        </div>
        
        {/* Nom de la case */}
        <div className="text-xs font-semibold text-foreground leading-tight">
          {boardCase.name}
        </div>
        
        {/* Prix si applicable */}
        {boardCase.price && (
          <div className="text-xs text-secondary font-bold bg-secondary/10 px-1 rounded">
            {boardCase.price}€
          </div>
        )}
        
        {/* Joueurs sur cette case avec animations */}
        {playersOnCase.length > 0 && (
          <div className="absolute -top-3 -right-3 flex flex-wrap gap-1">
            {playersOnCase.map((player, index) => (
              <div
                key={player.id}
                className={`
                  w-5 h-5 rounded-full border-2 border-white shadow-lg
                  flex items-center justify-center text-xs transform transition-all duration-300
                  ${player.id === players[currentPlayer].id ? 'ring-2 ring-primary scale-110' : ''}
                  ${animatingPlayers.has(player.id) ? 'animate-bounce' : ''}
                  hover:scale-125 hover:z-10
                `}
                style={{ 
                  backgroundColor: player.color,
                  transform: `translateY(${index * -2}px) translateX(${index * 2}px)`,
                  zIndex: playersOnCase.length - index
                }}
                title={player.name}
              >
                <span className="drop-shadow-sm">
                  {player.avatar === '👤' ? '👤' : '🤖'}
                </span>
              </div>
            ))}
          </div>
        )}
        
        {/* Effet de brillance pour les cases spéciales */}
        {(boardCase.type === 'start' || boardCase.type === 'special') && (
          <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-transparent rounded pointer-events-none" />
        )}
      </div>
    )
  }

  const boardSize = viewport.isMobile ? 'w-[350px] h-[350px]' : 
                   viewport.isTablet ? 'w-[500px] h-[500px]' : 
                   'w-[600px] h-[600px]'

  return (
    <div className={`${boardSize} flex items-center justify-center p-4`}>
      {/* Container principal avec perspective 3D */}
      <div 
        className="relative bg-gradient-to-br from-background/80 to-muted/40 backdrop-blur-md rounded-2xl border-2 border-border shadow-2xl"
        style={{
          perspective: '1000px',
          transformStyle: 'preserve-3d'
        }}
      >
        
        {/* Plateau en grille avec effet 3D */}
        <div 
          className="grid grid-cols-11 grid-rows-11 gap-0 transform transition-transform duration-500 hover:rotateX-1"
          style={{
            transformStyle: 'preserve-3d'
          }}
        >
          
          {/* Ligne du haut */}
          <div className="col-span-11 flex">
            {top.map((boardCase, index) => (
              <div key={boardCase.id}>
                {renderCase(boardCase, 'top')}
              </div>
            ))}
          </div>
          
          {/* Lignes du milieu avec côtés gauche et droit */}
          {Array.from({ length: 9 }, (_, rowIndex) => (
            <div key={`row-${rowIndex}`} className="col-span-11 flex">
              {/* Case de gauche */}
              <div>
                {renderCase(left[rowIndex], 'left')}
              </div>
              
              {/* Espace central avec logo et informations */}
              {rowIndex === 4 && (
                <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 border border-border/50 rounded-lg m-2">
                  <div className="text-center space-y-2">
                    <div className="text-2xl font-bold text-primary neon-text">
                      TheRateRace
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Tour de {players[currentPlayer].name}
                    </div>
                    <div className="flex justify-center space-x-2">
                      {players.map((player, index) => (
                        <div
                          key={player.id}
                          className={`w-3 h-3 rounded-full border ${
                            index === currentPlayer ? 'ring-2 ring-primary' : ''
                          }`}
                          style={{ backgroundColor: player.color }}
                          title={`${player.name}: ${player.money}€`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              )}
              
              {rowIndex !== 4 && (
                <div className="flex-1" />
              )}
              
              {/* Case de droite */}
              <div>
                {renderCase(right[rowIndex], 'right')}
              </div>
            </div>
          ))}
          
          {/* Ligne du bas */}
          <div className="col-span-11 flex">
            {bottom.map((boardCase, index) => (
              <div key={boardCase.id}>
                {renderCase(boardCase, 'bottom')}
              </div>
            ))}
          </div>
        </div>
        
        {/* Effet de lueur autour du plateau */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 pointer-events-none" />
      </div>
    </div>
  )
}
